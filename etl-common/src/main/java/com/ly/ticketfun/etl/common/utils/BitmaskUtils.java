package com.ly.ticketfun.etl.common.utils;

import cn.hutool.core.collection.CollectionUtil;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 位掩码Util
 * 最高支持32项选择
 *
 * <AUTHOR>
 * @date 2024/08/08
 */
public class BitmaskUtils {

    private static final Integer MAX_OPTION = 31; // 只支持32位整数，可以处理31个选项（从1到31）

    /**
     * 将选项编码到位掩码
     *
     * @param selectedOptions 所选选项
     * @return {@link Integer }
     */
    public static Integer encodeOptionsToBitmask(List<Integer> selectedOptions) {
        if (selectedOptions == null || selectedOptions.isEmpty()) {
            return 0;
        }
        AtomicInteger number = new AtomicInteger();
        selectedOptions.stream().distinct().
                forEach(option -> {
                    if (option < 1 || option > MAX_OPTION) {
                        throw new IllegalArgumentException("Option value must be between 1 and " + MAX_OPTION);
                    }
                    number.updateAndGet(v -> v | (1 << (option - 1)));
                });
        return number.get();
    }

    /**
     * 将位掩码解码为选项
     *
     * @param number 编号
     * @return {@link List }<{@link Integer }>
     */
    public static List<Integer> decodeBitmaskToOptions(Integer number) {
        if (number == null || number == 0) {
            return new ArrayList<>();
        }
        Set<Integer> selectedOptions = new HashSet<>();
        int optionIndex = 0;
        while (number > 0) {
            if ((number & 1) == 1) {
                selectedOptions.add(optionIndex + 1);
            }
            number >>= 1;
            optionIndex++;
        }
        return new ArrayList<>(selectedOptions);
    }

    /**
     * 获取包含指定选项的位掩码集合
     *
     * @param selectedOptions 所选选项
     * @param maxOptionValue  最大选项值
     * @return {@link Set }<{@link Integer }>
     */
    public static List<Integer> getBitmaskListIncludeSpecifiedOptions(List<Integer> selectedOptions, Integer maxOptionValue) {
        if (maxOptionValue > MAX_OPTION) {
            throw new IllegalArgumentException("Max option value must be less than or equal to " + MAX_OPTION);
        }
        int minBitMask = encodeOptionsToBitmask(selectedOptions);
        Set<Integer> bitMaskList = new HashSet<>();
        for (int bitMask = minBitMask; bitMask <= (1 << maxOptionValue) - 1; bitMask++) {
            if ((bitMask & minBitMask) == minBitMask) {
                bitMaskList.add(bitMask);
            }
        }

        return new ArrayList<>(bitMaskList);
    }


    public static void main(String[] args) {
        List<Integer> selectedOptions = CollectionUtil.toList(2,1,5,7);
        System.out.println(BitmaskUtils.encodeOptionsToBitmask(Arrays.asList(new Integer(2), new Integer(2),new Integer(4))));

        List<Integer> bitmaskListIncludeSpecifiedOptions = getBitmaskListIncludeSpecifiedOptions(selectedOptions, 8);
        bitmaskListIncludeSpecifiedOptions.forEach(
                bitmask -> System.out.println(decodeBitmaskToOptions(bitmask).containsAll(selectedOptions))
        );
        System.out.println(bitmaskListIncludeSpecifiedOptions);
//        Set<Integer> decodedList = decodeBitmaskToOptions(encodedNumber);
//
//        System.out.println("Encoded number: " + encodedNumber);  // 输出: 21
//        System.out.println("Decoded list: " + decodedList);      // 输出: [1, 3, 5]
    }
}
