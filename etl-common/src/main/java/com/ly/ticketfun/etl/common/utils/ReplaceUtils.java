package com.ly.ticketfun.etl.common.utils;


import org.apache.commons.lang3.StringUtils;

public class ReplaceUtils {

    public static String replaceBracket(String str) {
        if (StringUtils.isEmpty(str)) {
            return str;
        }
        return str.replaceAll("（", "(")
                .replaceAll("）", ")")
                .replaceAll("【", "[")
                .replaceAll("】", "]");
    }

    /**
     * 将小数折扣率转换为中文折数
     *
     * @param discount 折扣率（范围0.0-1.0，如0.95表示95折）
     * @return 格式化后的折数字符串
     */
    public static String formatDiscount(double discount) {
        if (discount <= 0 || discount > 1) {
            return "";
        }

        // 放大100倍后四舍五入取整
        int value = (int) Math.round(discount * 100);

        if (value < 10) {
            // 处理小于10的情况（如0.09→0.9折）
            return String.format("%.1f折", value / 10.0);
        } else if (value % 10 == 0) {
            // 处理整十倍数（如0.9→9折）
            return (value / 10) + "折";
        } else {
            // 普通两位数（如0.95→95折）
            return value + "折";
        }
    }


    /**
     * 红包折数
     *
     * @param discount
     * @return
     */
    public static String processRedPackageDiscount(Integer discount) {
        if (discount.compareTo(0) > 0) {
            if (discount % 10 == 0) {
                // 处理整十倍数
                return (discount / 10) + "折";
            } else {
                return String.format("%s折", discount);
            }
        }
        return "";
    }

}
