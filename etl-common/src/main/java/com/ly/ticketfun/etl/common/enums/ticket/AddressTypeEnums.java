package com.ly.ticketfun.etl.common.enums.ticket;


public enum AddressTypeEnums {
    //0 景区入口1游客中心2检票口3售票口4闸机口5取票机6取票口7无
    Entrance("景区入口", 0),
    TouristCentre("游客中心", 1),
    TicketCheck("检票口", 2),
    Tickets("售票口", 3),
    GateOpening("闸机口", 4),
    TicketMachine("取票机", 5),
    TicketGet("取票口", 6),
    None("无", 7);
    private String name;
    private int index;

    private AddressTypeEnums(String name, int index) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (AddressTypeEnums c : AddressTypeEnums.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
