package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.Getter;

@Getter
public enum RefundTypeEnums {
    NoRefund(0, "不可退"),
    AnyTimeRefund(1, "随时退"),
    ConditionRefund(2, "条件退");

    private final Integer code;
    private final String message;

    RefundTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getName(int code) {
        for (RefundTypeEnums c : RefundTypeEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }
}
