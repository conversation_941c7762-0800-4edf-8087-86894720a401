package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@AllArgsConstructor
@Getter
public enum AdmissionVoucherEnums {
    //电子凭证
    QRCode(3, "二维码"),
    AdmissionAssistantCode(8, "入园辅助码"),
    //短信凭证
    BookingSuccessSMS(1, "预订成功短信"),
    SecondarySendSMS(2, "供应商二次发送的短信"),
    PaymentSuccessSMS(7, "支付成功短信"),
    //取票人信息凭证
    NameAndPhone(5, "取票人姓名+手机号"),
    //实体证件凭证
    IDCard(4, "身份证"),
    StudentCard(11, "学生证"),
    HouseholdRegistrationBook(18, "户口本"),
    PassPort(19, "护照"),
    ElderlyCard(17, "老年优待证"),
    TeacherCard(12, "教师证"),
    SoldierCard(13, "军人证"),
    DisabledCard(14, "残疾人证"),
    GuideCard(16, "导游证"),
    TaiwanPass(20, "台湾通行证"),
    GangAoPass(21, "港澳通行证"),
    MiddleSchoolAdmissionTicket(22, "中考准考证"),
    HighSchoolAdmissionTicket(23, "高考准考证"),
    HealthCareWorkerCard(15, "医护人员证"),
    ForeignPermanentResidencePermit(24, "外国人永久居留证");

    private final Integer value;
    private final String name;
}
