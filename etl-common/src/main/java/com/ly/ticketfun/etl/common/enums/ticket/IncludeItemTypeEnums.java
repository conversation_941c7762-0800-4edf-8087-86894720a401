package com.ly.ticketfun.etl.common.enums.ticket;

/**
 * <AUTHOR>
 * @date 2025-7-21
 * @note
 */
public enum IncludeItemTypeEnums {
    /**
     * 类型
     * 290150201 票 排序1
     * 290150202 演 排序3
     * 290150203 餐 排序2
     * 290150204 行 排序4
     * 290150205 讲 排序5
     * 290150206 享 排序7
     * 290150207 住 排序6
     * 290150208 游 排序8
     */
    Ticket(290150201, "票", 1),
    Performance(290150202, "演", 3),
    Meal(290150203, "餐", 2),
    Route(290150204, "行", 4),
    Talk(290150205, "讲", 5),
    Enjoy(290150206, "享", 7),
    Hotel(290150207, "住", 6),
    Tour(290150208, "游", 8);
    private final Integer code;
    private final String message;
    private final Integer sortNum;

    IncludeItemTypeEnums(Integer code, String message, Integer sortNum) {
        this.code = code;
        this.message = message;
        this.sortNum = sortNum;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public static IncludeItemTypeEnums getEnum(Integer code) {
        for (IncludeItemTypeEnums value : IncludeItemTypeEnums.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Integer getSortNumByCode(Integer code) {
        for (IncludeItemTypeEnums value : IncludeItemTypeEnums.values()) {
            if (value.code.equals(code)) {
                return value.sortNum;
            }
        }
        return 0;
    }
}
