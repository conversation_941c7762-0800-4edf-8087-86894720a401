package com.ly.ticketfun.etl.common.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.ticketfun.etl.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ConfigClientUtils {

    /**
     * projectName : tcscenery.java.resourcefrontend
     */
    public static final String TCSCENERY_JAVA_RESOURCE_FRONTEND = "tcscenery.java.resourcefrontend";

    public static final String TCWL_NETCORE_RESOURCE_FRONTEND_API = "tcwl.netcore.resource.frontend.api";

    public static final String TCWL_NETCORE_ORDER_CORE_API = "tcwl.netcore.order.core.api";

    public static final String TCSCENERY_JAVA_RESOURCE_API_BACKEND = "tcscenery.java.resource.api.backend";

    /**
     * tcscenery.java.localactivity.external.api 日游项目
     */
    public static final String TCSCENERY_JAVA_LOCAL_ACTIVITY_EXTERNAL_API = "tcscenery.java.localactivity.external.api";

    /**
     * 配置中心获取值key时，如果key不存在,自定义返回值defaultValue
     *
     * @param key          key
     * @param defaultValue defaultValue
     * @return value
     */
    public static String getOrDefault(String projectName, String key, String defaultValue) {
        String value = null;
        try {
            value = ConfigCenterClient.get(projectName, key);
        } catch (Exception e) {
            SkyLogUtils.error(LogOperateTypeEnum.OTHER.getOpType(),"ConfigClientUtils","getOrDefault","获取统一配置失败",e);
        }
        return value != null ? value : defaultValue;
    }

    /**
     * 配置中心获取值key时，如果key不存在,自定义返回值defaultValue；
     * projectName来源与当前项目的配置
     */
    public static String getOrDefault(String key, String defaultValue) {
        String value = null;
        try {
            value = ConfigCenterClient.get(key);
        } catch (Exception e) {
            SkyLogUtils.error(LogOperateTypeEnum.OTHER.getOpType(),"ConfigClientUtils","getOrDefault","获取统一配置失败",e);
        }
        return value != null ? value : defaultValue;
    }

    /**
     * 根据key对应的配置值获取json对应实体
     * @param projectName 统一配置项目名
     * @param key configKey
     * @param defaultValue configDefaultValue
     * @param typeReference jsonTypeReference
     */
    public static <T> T getAndUnmarshal(String projectName, String key, String defaultValue, TypeReference<T> typeReference) {
        String configValue = getOrDefault(projectName, key, defaultValue);
        return JsonUtils.string2GenericAbstract(configValue, typeReference);
    }

}
