package com.ly.ticketfun.etl.common.enums.ticket;


public enum FeeIncludeTypeEnums {
    INCLUDE_FEE(0, "费用包含项目"),
    INCLUDE_COUNT(1, "是否全部可用"),
    INCLUDE_DESC(2, "包含说明"),
    EXCLUDE(3, "费用不包含");

    private Integer type;
    private String desc;

    FeeIncludeTypeEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
