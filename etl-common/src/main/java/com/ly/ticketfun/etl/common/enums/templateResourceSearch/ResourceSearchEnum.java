package com.ly.ticketfun.etl.common.enums.templateResourceSearch;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note
 */
public interface ResourceSearchEnum {

    /**
     * 通用是否有效 1有效，0无效
     */
    @AllArgsConstructor
    @Getter
    enum EffectiveEnum {
        valid("valid", "有效", 1),
        invalid("invalid", "无效", 0);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 确认一级模式
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum SubjectTypeEnum {
        /**
         * 自身
         */
        SELF("self", "自身", 0),

        /**
         * 主景点
         */
        MAIN_ATTRACTION("main_attraction", "主景点", 1),

        /**
         * 子景点
         */
        SUB_ATTRACTION("sub_attraction", "子景点", 2),

        /**
         * 关联景区
         */
        RELATED_SCENIC("related_scenic", "关联景区", 3);

        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 退改状态
     * 0：不可退，1：随时退，2：无损条件退，3：有损条件退
     */
    @AllArgsConstructor
    @Getter
    enum RefundTypeEnum {
        NO_REFUND("no_refund", "不可退", 0),
        ANYTIME_REFUND("anytime_refund", "随时退", 1),
        NO_LOSS_CONDITION_REFUND("no_loss_condition_refund", "无损条件退", 2),
        LOSS_CONDITION_REFUND("loss_condition_refund", "有损条件退", 3);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 资源星级（61201 1A，61202 2A，61203 3A，61204 4A，61205 5A，61206 其它）
     */
    @AllArgsConstructor
    @Getter
    enum ResourceGradeEnum {
        ONE_A("one_a", "1A", 61201),
        TWO_A("two_a", "2A", 61202),
        THREE_A("three_a", "3A", 61203),
        FOUR_A("four_a", "4A", 61204),
        FIVE_A("five_a", "5A", 61205),
        OTHER("other", "其它", 61206);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 景区营业状态（0：未知，1: 营业， 2：不营业）
     */
    @AllArgsConstructor
    @Getter
    enum BusinessStatusEnum {
        UNKNOWN("unknown", "未知", 0),
        OPEN("open", "营业", 1),
        NOT_OPEN("not_open", "不营业", 2);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 位置类型 (0:出发地,1:目的地)
     */
    @AllArgsConstructor
    @Getter
    enum PlaceTypeEnum {
        FROM("from", "出发地", 0),
        TO("to", "目的地", 1);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 订单正负佣金（1：正，0：平，-1：负）
     */
    @AllArgsConstructor
    @Getter
    enum CommissionStatusEnum {
        POSITIVE("positive", "正", 1),
        NEUTRAL("neutral", "平", 0),
        NEGATIVE("negative", "负", -1);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 价格类型
     * 0无外显 1 网络卖价 2门市价 3 文案描述
     */
    @AllArgsConstructor
    @Getter
    enum PriceTypeEnum {
        NO_EXPOSURE("no_exposure", "无外显", 0),
        NET_SALE_PRICE("net_sale_price", "网络卖价", 1),
        MARKET_PRICE("market_price", "门市价", 2),
        TEXT_DESCRIPTION("text_description", "文案描述", 3);
        private final String value;
        private final String name;
        private final Integer ticketValue;


    }

    /**
     * 来源,0人工,1自动
     */
    @AllArgsConstructor
    @Getter
    enum FacilitySourceFromEnum {
        ARTIFICIAL("artificial", "人工",0),
        AUTO("auto", "自动",1);
        private final String value;
        private final String name;
        private final Integer ticketValue;

    }
}
