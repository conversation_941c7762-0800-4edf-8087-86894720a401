package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookConfigEnum;
import com.ly.ticketfun.etl.common.enums.ticket.AdmissionVoucherEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预订凭证枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface BookVoucherEnum {
    /**
     * 凭证发送方
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum VoucherSender {
        TC_SEND("TC_SEND", "同程发送", MainResourceBookConfigEnum.SendVoucherType.TC_SEND.getValue()),
        SUPPLIER_SEND("SUPPLIER_SEND", "供应商发送", MainResourceBookConfigEnum.SendVoucherType.SUPPLIER_SEND.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 凭证使用方法
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum VoucherUsageMethod {
        QRCode("QRCode", "二维码", null, AdmissionVoucherEnums.QRCode.getValue()),
        AdmissionAssistantCode("AdmissionAssistantCode", "入园辅助码", null, AdmissionVoucherEnums.AdmissionAssistantCode.getValue()),
        BookingSuccessSMS("BookingSuccessSMS", "预订成功短信", null, AdmissionVoucherEnums.BookingSuccessSMS.getValue()),
        SecondarySendSMS("SecondarySendSMS", "供应商二次发送的短信", null, AdmissionVoucherEnums.SecondarySendSMS.getValue()),
        PaymentSuccessSMS("PaymentSuccessSMS", "支付成功短信", null, AdmissionVoucherEnums.PaymentSuccessSMS.getValue()),
        NameAndPhone("NameAndPhone", "取票人姓名+手机号", null, AdmissionVoucherEnums.NameAndPhone.getValue()),
        IDCard("IDCard", "身份证", null, AdmissionVoucherEnums.IDCard.getValue()),
        StudentCard("StudentCard", "学生证", null, AdmissionVoucherEnums.StudentCard.getValue()),
        HouseholdRegistrationBook("HouseholdRegistrationBook", "户口本", null, AdmissionVoucherEnums.HouseholdRegistrationBook.getValue()),
        PassPort("PassPort", "护照", null, AdmissionVoucherEnums.PassPort.getValue()),
        ElderlyCard("ElderlyCard", "老年优待证", null, AdmissionVoucherEnums.ElderlyCard.getValue()),
        TeacherCard("TeacherCard", "教师证", null, AdmissionVoucherEnums.TeacherCard.getValue()),
        SoldierCard("SoldierCard", "军人证", null, AdmissionVoucherEnums.SoldierCard.getValue()),
        DisabledCard("DisabledCard", "残疾人证", null, AdmissionVoucherEnums.DisabledCard.getValue()),
        GuideCard("GuideCard", "导游证", null, AdmissionVoucherEnums.GuideCard.getValue()),
        TaiwanPass("TaiwanPass", "台湾通行证", null, AdmissionVoucherEnums.TaiwanPass.getValue()),
        GangAoPass("GangAoPass", "港澳通行证", null, AdmissionVoucherEnums.GangAoPass.getValue()),
        MiddleSchoolAdmissionTicket("MiddleSchoolAdmissionTicket", "中考准考证", null, AdmissionVoucherEnums.MiddleSchoolAdmissionTicket.getValue()),
        HighSchoolAdmissionTicket("HighSchoolAdmissionTicket", "高考准考证", null, AdmissionVoucherEnums.HighSchoolAdmissionTicket.getValue()),
        HealthCareWorkerCard("HealthCareWorkerCard", "医护人员证", null, AdmissionVoucherEnums.HealthCareWorkerCard.getValue()),
        ForeignPermanentResidencePermit("ForeignPermanentResidencePermit", "外国人永久居留证", null, AdmissionVoucherEnums.ForeignPermanentResidencePermit.getValue());
        private final String value;
        private final String name;
        private final String funValue;
        private final Integer ticketValue;
    }
}
