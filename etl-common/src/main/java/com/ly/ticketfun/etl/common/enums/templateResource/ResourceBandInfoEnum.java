package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceCrowdEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import com.ly.ticketfun.etl.common.enums.ticket.AgeDateDimensionEnums;
import com.ly.ticketfun.etl.common.enums.ticket.TargetAudienceEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分类（人群）信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ResourceBandInfoEnum {
    /**
     * 分类（人群）Code
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum BandCode {
        ADULT("ADULT", "成人", MainResourceCrowdEnum.CrowdCode.ADULT.getValue(), TargetAudienceEnums.ADULT.getValue()),
        CHILD("CHILD", "儿童", MainResourceCrowdEnum.CrowdCode.CHILD.getValue(), TargetAudienceEnums.CHILD.getValue()),
        SENIOR("SENIOR", "老人", MainResourceCrowdEnum.CrowdCode.SENIOR.getValue(), TargetAudienceEnums.ELDER.getValue()),
        INFANT("INFANT", "婴儿", MainResourceCrowdEnum.CrowdCode.INFANT.getValue(), null),
        OTHER("OTHER", "其他", MainResourceCrowdEnum.CrowdCode.OTHER.getValue(), TargetAudienceEnums.OTHER.getValue()),
        STANDARD("STANDARD", "标准价", MainResourceCrowdEnum.CrowdCode.STANDARD.getValue(), null),

        /**
         * 门票开始
         */
        STUDENT_FULL_TIME("STUDENT_FULL_TIME", "全日制大中小学生", null, TargetAudienceEnums.STUDENT_FULL_TIME.getValue()),
        STUDENT_FULL_TIME_EXCLUDE_ADULT("STUDENT_FULL_TIME_EXCLUDE_ADULT", "全日制大中小学生（不含成人教育、研究生）", null, TargetAudienceEnums.STUDENT_FULL_TIME_EXCLUDE_ADULT.getValue()),
        COLLEGE_STUDENT("COLLEGE_STUDENT", "大学生", null, TargetAudienceEnums.COLLEGE_STUDENT.getValue()),
        PRIMARY_MIDDLE_HIGH_SCHOOL_STUDENT("PRIMARY_MIDDLE_HIGH_SCHOOL_STUDENT", "小中高学生", null, TargetAudienceEnums.PRIMARY_MIDDLE_HIGH_SCHOOL_STUDENT.getValue()),
        TEACHER("TEACHER", "教师", null, TargetAudienceEnums.TEACHER.getValue()),
        JOURNALIST("JOURNALIST", "记者", null, TargetAudienceEnums.JOURNALIST.getValue()),
        ACTIVE_SERVICE_MILITARY("ACTIVE_SERVICE_MILITARY", "现役军人", null, TargetAudienceEnums.ACTIVE_SERVICE_MILITARY.getValue()),
        VETERAN("VETERAN", "退役军人", null, TargetAudienceEnums.VETERAN.getValue()),
        RETIRED_CADRE("RETIRED_CADRE", "离休干部", null, TargetAudienceEnums.RETIRED_CADRE.getValue()),
        THREE_FAMILY_MEMBERS("THREE_FAMILY_MEMBERS", "三属人员", null, TargetAudienceEnums.THREE_FAMILY_MEMBERS.getValue()),
        MEDICAL_STAFF("MEDICAL_STAFF", "医护人员", null, TargetAudienceEnums.MEDICAL_STAFF.getValue()),
        LOCAL_RESIDENT("LOCAL_RESIDENT", "本地市民", null, TargetAudienceEnums.LOCAL_RESIDENT.getValue()),
        FOREIGNER("FOREIGNER", "外籍人士", null, TargetAudienceEnums.FOREIGNER.getValue()),
        MINOR("MINOR", "未成年", null, TargetAudienceEnums.MINOR.getValue()),
        DISABLED("DISABLED", "残疾人", null, TargetAudienceEnums.DISABLED.getValue()),
        LABOR_MODEL("LABOR_MODEL", "劳动模范", null, TargetAudienceEnums.LABOR_MODEL.getValue()),
        GROUP_TOUR_GUIDE("GROUP_TOUR_GUIDE", "跟团导游", null, TargetAudienceEnums.GROUP_TOUR_GUIDE.getValue()),
        RELIGIOUS_PERSON("RELIGIOUS_PERSON", "宗教人士", null, TargetAudienceEnums.RELIGIOUS_PERSON.getValue()),
        TOUR_GUIDE("TOUR_GUIDE", "导游", null, TargetAudienceEnums.TOUR_GUIDE.getValue()),
        FIRE_RESCUE_TEAM_MEMBER("FIRE_RESCUE_TEAM_MEMBER", "国家综合性消防救援队员", null, TargetAudienceEnums.FIRE_RESCUE_TEAM_MEMBER.getValue()),
        BLOOD_DONATION_HONOR_HOLDER("BLOOD_DONATION_HONOR_HOLDER", "省内无偿献血、无偿捐献造血干细胞及无偿献血终生荣誉奖个人", null, TargetAudienceEnums.BLOOD_DONATION_HONOR_HOLDER.getValue()),
        UNLIMITED("UNLIMITED", "不限人群", null, TargetAudienceEnums.UNLIMITED.getValue()),

        /**
         * 门票产品开始
         */
        TICKET_PRODUCT_ADULT("60301", "成人票", null, null),
        TICKET_PRODUCT_CHILD("60302", "儿童票", null, null),
        TICKET_PRODUCT_SENIOR("60303", "老人票", null, null),
        TICKET_PRODUCT_STUDENT("60304", "学生票", null, null),
        TICKET_PRODUCT_GROUP("60305", "团队票", null, null),
        TICKET_PRODUCT_PREFERENTIAL("60306", "优待票", null, null),
        TICKET_PRODUCT_FAMILY("60307", "家庭票", null, null),
        TICKET_PRODUCT_PARENT_CHILD("60308", "亲子票", null, null),
        TICKET_PRODUCT_FREE("60309", "免费票", null, null),
        TICKET_PRODUCT_UNLIMITED("60310", "不限人群", null, null);


        private final String value;
        private final String name;
        private final String funValue;
        private final String ticketValue;
    }

    /**
     * 限制条件类型
     *
     * <AUTHOR>
     * @date 2024/12/17
     */
    @AllArgsConstructor
    @Getter
    enum RangeType {
        AGE("AGE", "年龄", MainResourceCrowdEnum.RangeType.AGE.getValue()),
        HEIGHT("HEIGHT", "身高", MainResourceCrowdEnum.RangeType.HEIGHT.getValue());
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 生日维度
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Getter
    @AllArgsConstructor
    enum BirthdayDimension {
        DAY("DAY", "日", MainResourceCrowdEnum.CalcCondition1.BIRTHDAY.getValue(), AgeDateDimensionEnums.Age.getCode()),
        MONTH("MONTH", "月", null, AgeDateDimensionEnums.AgeMonth.getCode()),
        YEAR("YEAR", "年份", MainResourceCrowdEnum.CalcCondition1.YEAR.getValue(), AgeDateDimensionEnums.AgeDay.getCode());
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 年龄计算比较维度
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @AllArgsConstructor
    @Getter
    enum AgeCalcCompareDimension {
        USAGE_DATE(TimeNodeEnum.USAGE_DATE, MainResourceCrowdEnum.CalcCondition2.TRAVEL_DATE.getValue()),
        CREATE_DATE(TimeNodeEnum.CREATE_DATE, MainResourceCrowdEnum.CalcCondition2.BOOKING_DATE.getValue()),
        ;

        AgeCalcCompareDimension(TimeNodeEnum usageDate, Integer funValue) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
            this.funValue = funValue;
        }

        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 身份类型
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @AllArgsConstructor
    @Getter
    enum IdentityType {
        STUDENT("STUDENT", "学生", "包含研究生、硕士、博士", 701),
        STUDENT_EXCLUDE_POSTGRADUATE("STUDENT_EXCLUDE_POSTGRADUATE", "学生", "不包含研究生、硕士、博士", 702),
        ;

        private final String value;
        private final String name;
        private final String desc;
        private final Integer ticketValue;

        public static String getValue(Integer type) {
            IdentityType[] typeEnums = values();
            for (IdentityType typeEnum : typeEnums) {
                if (typeEnum.getTicketValue().equals(type)) {
                    return typeEnum.getValue();
                }
            }
            return "";
        }
    }
}
