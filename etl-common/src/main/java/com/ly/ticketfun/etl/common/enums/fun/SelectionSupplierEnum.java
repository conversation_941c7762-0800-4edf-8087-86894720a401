package com.ly.ticketfun.etl.common.enums.fun;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 渠道来源
 */
@AllArgsConstructor
@Getter
public enum SelectionSupplierEnum {
    // CTRIP_TRAVEL_HOLDING_LIMITED(45131l, "CTRIP TRAVEL HOLDING （HONG KONG） LIMITED"),
    K<PERSON><PERSON>(45875L, "深圳雄狮天下旅行社有限公司"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(39002L, "广州三毛信息科技有限公司"),
    TEST(45131L, "测试供应商"),
    CTRIP_WAN_CHENG(48268L, "万程（上海）旅行社有限公司"),
    CTRIP_TRAVEL_HOLDING_LIMITED(47676L, "CTRIP TRAVEL HOLDING （HONG KONG） LIMITED"),
    ;

    private final Long value;

    private final String name;
}
