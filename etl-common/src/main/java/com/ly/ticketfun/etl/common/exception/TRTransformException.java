package com.ly.ticketfun.etl.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模版商品库转换异常
 *
 * <AUTHOR>
 * @date 2025/09/09
 */
@Getter
public class TRTransformException extends RuntimeException {
    private static final long serialVersionUID = 6810804294312329502L;
    private final TRTransformException.ErrorInfo errorInfo;
    private final String[] extendList;

    public TRTransformException(TRTransformException.ErrorInfo errorInfo, String... extendList) {
        super(errorInfo.getMsg());
        this.errorInfo = errorInfo;
        this.extendList = extendList;
    }

    @AllArgsConstructor
    @Getter
    public enum ErrorInfo {
        RESOURCE_API_FAIL("RESOURCE_API_FAIL", "资源api调用失败,url:{0},params:{1},result:{2}"),

        PRODUCT_TRANSFORM_FAIL("PRODUCT_TRANSFORM_FAIL", "产品转换失败,productId:{0},reason:{1}"),
        PACKAGE_TRANSFORM_FAIL("PACKAGE_TRANSFORM_FAIL", "套餐转换失败,productId:{0},packageId:{1},reason:{2}"),
        SKU_TRANSFORM_FAIL("SKU_TRANSFORM_FAIL", "套餐转换失败,productId:{0},packageId:{1},skuId:{2},reason:{3}"),
        POI_TRANSFORM_FAIL("POI_TRANSFORM_FAIL", "SKU转换失败,poiId:{0},packageId:{1},model:{2}"),
        SALE_CHANNEL_TRANSFORM_FAIL("SALE_CHANNEL_TRANSFORM_FAIL", "销售渠道转换失败,poiId:{0},packageId:{1},model:{2}"),

        PACKAGE_NOT_ON_SALE("PACKAGE_NOT_ON_SALE", "套餐不在售, 产品id:{0}，套餐id:{1}"),

        PRODUCT_ONLINE_FAIL("PRODUCT_ONLINE_FAIL", "产品上架失败,productId:{0}"),
        PACKAGE_ONLINE_FAIL("PACKAGE_ONLINE_FAIL", "资源上架失败"),
        SKU_ONLINE_FAIL("SKU_ONLINE_FAIL", "SKU上架失败"),
        PRODUCT_OFFLINE_FAIL("PRODUCT_OFFLINE_FAIL", "产品下架失败"),
        PACKAGE_OFFLINE_FAIL("PACKAGE_OFFLINE_FAIL", "资源下架失败"),
        SKU_OFFLINE_FAIL("SKU_OFFLINE_FAIL", "SKU下架失败"),

        REQ_PARAMS_ERROR("REQ_PARAMS_ERROR", "入参错误,{0}"),
        RESOURCE_NOT_EXISTS("RESOURCE_NOT_EXISTS", "资源不存在, 资源id:{0}"),
        PACKAGE_NOT_EXISTS("PACKAGE_NOT_EXISTS", "套餐不存在, 产品id:{0}，套餐id:{1}"),
        RESOURCE_NOT_ON_SALE("RESOURCE_INVALID", "资源不在售, 资源id:{0}"),
        ENUM_NOT_MAPPING("ENUM_NOT_MAPPING", "枚举映射错误，key:{0}，value:{1}"),
        TICKET_RESOURCE_NOT_EXISTS("TICKET_RESOURCE_NOT_EXISTS", "门票资源不存在:{0}");
        private final String code;
        private final String msg;
    }
}
