package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品逻辑扩展枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface LogicExtendEnum {
    /**
     * 产品key
     *
     * <AUTHOR>
     * @date 2025/09/12
     */
    @AllArgsConstructor
    @Getter
    enum ProductKey {
        ORDER_CREATE_VOLUME("ORDER_CREATE_VOLUME", "创单量"),
        ORDER_CREATE_VOLUME_15D("ORDER_CREATE_VOLUME_15D", "近15天创单量"),
        ORDER_CREATE_VOLUME_30D("ORDER_CREATE_VOLUME_30D", "近30天创单量"),
        ORDER_CREATE_VOLUME_180D("ORDER_CREATE_VOLUME_180D", "近180天创单量"),

        ORDER_PAY_VOLUME_15D("ORDER_PAY_VOLUME_15D", "近15天付款单量"),
        ORDER_PAY_VOLUME_30D("ORDER_PAY_VOLUME_30D", "近30天付款单量"),
        ORDER_PAY_VOLUME_180D("ORDER_PAY_VOLUME_180D", "近180天付款单量"),

        ORDER_PAY_UNIT_VOLUME("ORDER_PAY_UNIT_VOLUME", "付款单销售份数"),

        COMMENT_SCORE("COMMENT_SCORE", "点评分"),
        COMMENT_COUNT("COMMENT_COUNT", "点评总数"),
        COMMENT_GOOD_COUNT("COMMENT_GOOD_COUNT", "好评数"),
        COMMENT_MIDDLE_COUNT("COMMENT_MIDDLE_COUNT", "中评数"),
        COMMENT_BAD_COUNT("COMMENT_BAD_COUNT", "差评数"),
        COMMENT_SUMMARY("COMMENT_SUMMARY", "点评AI一句话"),

        BOOK4_RECOMMEND_COUNT("BOOK4_RECOMMEND_COUNT", "Book4骨架屏政策展示个数");
        private final String value;
        private final String name;
    }

    /**
     * 套餐key
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum PackageKey {
        ORDER_CREATE_VOLUME("ORDER_CREATE_VOLUME", "创单量"),
        ORDER_CREATE_VOLUME_15D("ORDER_CREATE_VOLUME_15D", "近15天创单量"),
        ORDER_CREATE_VOLUME_30D("ORDER_CREATE_VOLUME_30D", "近30天创单量"),
        ORDER_CREATE_VOLUME_180D("ORDER_CREATE_VOLUME_180D", "近180天创单量"),

        /**
         * 付款单量
         */
        ORDER_PAY_VOLUME("ORDER_PAY_VOLUME", "付款单量"),
        ORDER_PAY_VOLUME_15D("ORDER_PAY_VOLUME_15D", "近15天付款单量"),
        ORDER_PAY_VOLUME_30D("ORDER_PAY_VOLUME_30D", "近30天付款单量"),
        /**
         * 60天付款单量
         */
        ORDER_PAY_VOLUME_60D("ORDER_PAY_VOLUME_60D", "近60天付款单量"),
        /**
         * 90天付款单量
         */
        ORDER_PAY_VOLUME_90D("ORDER_PAY_VOLUME_90D", "近90天付款单量"),

        ORDER_PAY_VOLUME_180D("ORDER_PAY_VOLUME_180D", "近180天付款单量"),

        ORDER_PAY_UNIT_VOLUME("ORDER_PAY_UNIT_VOLUME", "付款单销售份数"),

        ORDER_PAY_EFFECTIVE_RATE("ORDER_PAY_EFFECTIVE_RATE", "付款单有效率"),
        /**
         * 付款单有效量
         */
        ORDER_PAY_EFFECTIVE_VOLUME("ORDER_PAY_EFFECTIVE_VOLUME", "付款单有效量"),

        /**
         * 0内部场次 1外部场次 2520222 多场次标识 2520226纯期票联票
         */
        Package_Special_Sign("Package_Special_Sign", "套餐特殊标识"),

        /**
         * 是否存在问卷
         */
        HAS_QUESTIONNAIRE("hasQuestionnaire", "是否存在问卷"),


        /**
         * 取件方式
         */
        TAKE_TYPE("takeType", "取件方式"),
        /**
         * 自取城市
         */
        SELF_PICKUP_CITY("selfPickupCity", "自取城市"),
        /**
         * 发货城市
         */
        DELIVERY_CITY("deliveryCity", "发货城市"),
        /**
         * 待机时长
         */
        STANDBY_DURATION("standbyDuration", "待机时长"),
        ;
        private final String value;
        private final String name;
    }


    /**
     * 搜索逻辑扩展枚举
     */
    @AllArgsConstructor
    @Getter
    enum ResourceSearchKeyEnum {
        PV_180("pv180", "多少个用户浏览过"),
        UV_ORDER_365("uvOrder365", "多少个用户下单过"),
        CLICK_UV_7("clickUv7", "点击7天UV"),
        EXPOSURE_UV_7("exposureUv7", "曝光7天UV"),
        CLICK_UV_YOY_7("clickUvYoy7", "曝光去年同期7天UV"),
        CLICK_UV_30("clickUv30", "点击30天UV"),
        EXPOSURE_UV_30("exposureUv30", "曝光30天UV"),
        CVR_30("cvr30", "30天CVR"),
        CVR_YOY_30("cvrYoy30", "去年同期30天CVR"),
        ORDER_OFFICIAL_7("orderOfficial7", "自营平台7天单量"),
        ORDER_OFFICIAL_180("orderOfficial180", "自营平台180天单量"),
        ORDER_OFFICIAL_365("orderOfficial365", "自营平台365天单量"),
        ORDER_7("order7", "全渠道7天单量"),
        ORDER_30("order30", "全渠道30天单量"),
        ORDER_180("order180", "全渠道180天单量"),
        ORDER_365("order365", "全渠道365天单量"),
        PAY_UNIT_COUNT("payUnitCount", "全渠道付款单销售份数"),
        HOTNESS_SCORE("hotnessScore", "热度得分"),
        COMPETITOR_ORDER("competitorOrder", "竞品单量"),
        COMMISSION_STATUS_30("commissionStatus30", "30 天内订单正负佣金"),
        COMMENT_SCORE("commentScore", "点评分"),
        COMMENT_COUNT("commentCount", "点评总数"),
        COMMENT_GOOD_COUNT("commentGoodCount", "好评数"),
        COMMENT_MIDDLE_COUNT("commentMiddleCount", "中评数"),
        COMMENT_BAD_COUNT("commentBadCount", "差评数"),
        COMMENT_GOOD_RATE("commentGoodRate", "好评率"),
        COMMENT_BAD_RATE180("commentBadRate180", "差评率（总）");
        private final String value;
        private final String name;
    }
}
