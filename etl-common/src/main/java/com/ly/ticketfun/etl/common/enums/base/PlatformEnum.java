package com.ly.ticketfun.etl.common.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 平台
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@AllArgsConstructor
@Getter
public enum PlatformEnum {
    TC_LX_XCX("852", "同程旅行-小程序"),
    TC_LX_APP("431", "同程旅行-APP"),
    TC_LX_H5("432", "同程旅行-M站"),
    HOPE_GOO_XCX("20004", "HopeGoo-小程序"),
    HOPE_GOO_APP("20000", "HopeGoo-APP"),
    HOPE_GOO_PC("20002", "HopeGoo-PC"),
    HOPE_GOO_H5("20003", "HopeGoo-M站"),
    ;

    public static final List<PlatformEnum> TC_LX_PLATFORMS = Arrays.asList(TC_LX_XCX, TC_LX_APP, TC_LX_H5);
    public static final List<PlatformEnum> HOPE_GOO_PLATFORMS = Arrays.asList(HOPE_GOO_XCX, HOPE_GOO_APP, HOPE_GOO_PC, HOPE_GOO_H5);

    private final String value;
    private final String name;
}
