package com.ly.ticketfun.etl.common.enums.es;

import com.ly.localactivity.model.enums.common.LocaleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * es枚举
 *
 * <AUTHOR>
 * @date 2025/09/08
 */
public interface ESEnum {

    @AllArgsConstructor
    @Getter
    enum IndexSuffix {
        zh_cn("zhcn", "简体中文", LocaleEnum.ZH_CN),
        zh_hk("zhhk", "繁体中文（香港）", LocaleEnum.ZH_HK),
        en_xx("enxx", "英文", LocaleEnum.EN_US),
        ;
        private final String value;
        private final String name;
        private final LocaleEnum locale;
    }
}
