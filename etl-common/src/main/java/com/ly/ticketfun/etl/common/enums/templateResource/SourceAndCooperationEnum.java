package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 合作来源枚举
 */
public interface SourceAndCooperationEnum {

    /**
     * 来源枚举
     */
    @AllArgsConstructor
    @Getter
    enum SourceTypeEnum {
        /**
         * 自签平台
         */
        SELF_SIGNED("Self_Signed", "自签", 1),

        /**
         * 携程平台
         */
        CTRIP("Ctrip", "携程", 2),

        /**
         * 艺龙平台
         */
        ELONG("Elong", "艺龙", 4),

        /**
         * Switch平台
         */
        SWITCH("Switch", "Switch", 8),

        /**
         * 开放平台(景区)
         */
        OPEN_PLATFORM("Open_Platform", "开放平台", 9),

        /**
         * 新开放平台
         */
        NEW_OPEN_PLATFORM("New_Open_Platform", "新开放平台", 10),

        /**
         * 电子导览
         */
        ELECTRONIC_GUIDE("Electronic_Guide", "电子导览", 11),

        /**
         * 美团平台
         */
        MEITUAN("Meituan", "美团", 12),

        /**
         * 客路平台
         */
        KLOOK("Klook", "客路", 13);
        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

    /**
     * 合作类型枚举
     */
    @AllArgsConstructor
    @Getter
    enum CooperationTypeEnum {
        /**
         * 自营
         */
        SELF_OPERATED("SELF_OPERATED", "自营", 72901),

        /**
         * 三方
         */
        THIRD_PARTY("THIRD_PARTY", "三方", 72902),

        /**
         * 携程
         */
        CTRIP("CTRIP", "携程", 72903),

        /**
         * 无合作
         */
        NO_PARTNERSHIP("NO_PARTNERSHIP", "无合作", 72904),

        /**
         * 自营三方
         */
        SELF_OPERATED_THIRD_PARTY("SELF_OPERATED_THIRD_PARTY", "自营三方", 72905),

        /**
         * 美团
         */
        MEITUAN("MEITUAN", "美团", 72906),

        /**
         * 日游玩乐
         */
        DAILY_ENTERTAINMENT("DAILY_ENTERTAINMENT", "日游玩乐", 72907),

        /**
         * 客路
         */
        KLOOK("KLOOK", "客路", 72908);

        private final String value;
        private final String name;
        private final Integer ticketValue;
    }

}
