package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.Getter;

@Getter
public enum RefundDateTypeEnums {
    /**
     * 1: '游玩日期前',
     * 2: '游玩日期当天',
     * 3: '游玩日期后',
     * 4: '下单日期当天',
     * 5: '下单日期后',
     * 6: '场次开始时间前',
     * 7: '场次开始时间后',
     * 8: '场次结束时间前',
     * 9: '场次结束时间后',
     * 10: '指定日期'
     */
    NoLimit(0, "无限制"),
    BeforeTravelDate(1, "游玩日期前"),
    TravelDate(2, "游玩日期当天"),
    AfterTravelDate(3, "游玩日期后"),
    OrderDate(4, "下单日期当天"),
    AfterOrderDate(5, "下单日期后"),
    SessionBeginBefore(6, "场次开始时间前"),
    SessionBeginAfter(7, "场次开始时间后"),
    SessionEndBefore(8, "场次结束时间前"),
    SessionEndAfter(9, "场次结束时间后"),
    SpecifyDate(10, "指定日期");

    private final Integer code;
    private final String message;

    RefundDateTypeEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getName(int code) {
        for (RefundDateTypeEnums c : RefundDateTypeEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }
}
