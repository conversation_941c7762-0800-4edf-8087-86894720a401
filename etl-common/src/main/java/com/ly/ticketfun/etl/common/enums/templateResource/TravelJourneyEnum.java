package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceGiftEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源赠品枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface TravelJourneyDetailEnum {

    @AllArgsConstructor
    @Getter
    enum PaidServiceType {
        SUPPORT("SUPPORT", "超出指定区域提供", 1),
        NOT_SUPPORT("NOT_SUPPORT", "超出指定区域不提供", 2),
        INCLUDE("INCLUDE", "费用包含", 3),
        SELF("SELF", "需自费", 4),// 费用自理
        FREE("FREE", "无需门票", 5),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;

    }

    @AllArgsConstructor
    @Getter
    enum VisitType {
        OUTSIDE_VISIT("OUTSIDE_VISIT", "不入内参观", 1),//外观
        CAR_VISIT("CAR_VISIT", "乘车参观", 2),//车观
        BOAT_VISIT("BOAT_VISIT", "乘船参观", 3),//船观
        INSIDE_VISIT("INSIDE_VISIT", "入内参观", 4),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }

    /**
     * POI预约类型
     */
    @AllArgsConstructor
    @Getter
    enum AdvanceReservation {
        //预约类型：0.无需提前预约10.商家代预约20.客人自行预约 21.客人提前预约后 ，商家购买门票
//    NO_BOOK(0, "无需提前预约"),
        SUPPLIER_BOOK("SUPPLIER_BOOK", "商家代预约", 10),
        CUSTOMER_SELF_BOOK("CUSTOMER_SELF_BOOK", "需客人自行预约", 20),
        CUSTOMER_BOOK_SUPPLIER_PAY("CUSTOMER_BOOK_SUPPLIER_PAY", "需客人提前预约后 ，商家购买门票", 21),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }

    @AllArgsConstructor
    @Getter
    enum CostUnitEnum {
        PERSON("PERSON", "人", 1),
        SPOT("SPOT", "份", 2),
        SHEET("SHEET", "张", 3),
        TIME("TIME", "次", 4),
        SUITE("SUITE", "套", 5),
        VEHICLE("VEHICLE", "辆", 6),
        SHIP("SHIP", "艘", 7),
        HOUR("HOUR", "小时", 8),
        ;


        private final String value;
        private final String name;
        private final Integer funValue;

    }


    @AllArgsConstructor
    @Getter
    enum IncludeExplain {
        // 讲解类型
        INCLUDE_EXPLAIN("INCLUDE_EXPLAIN", "含讲解", 1),
        EXCLUDE_EXPLAIN("EXCLUDE_EXPLAIN", "不含讲解", 0),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;

    }
}
