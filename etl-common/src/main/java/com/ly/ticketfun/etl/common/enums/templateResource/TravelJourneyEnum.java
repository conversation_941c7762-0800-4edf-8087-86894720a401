package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.TravelJourneyDetailEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源赠品枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface TravelJourneyEnum {

    @Getter
    @AllArgsConstructor
    enum FirstType {
        GATHER("GATHER", "集合", TravelJourneyDetailEnum.FirstType.GATHER.getValue()),
        TRAFFIC("TRAFFIC", "交通", TravelJourneyDetailEnum.FirstType.TRAFFIC.getValue()),
        TOUR_ACTIVITY("TOUR_ACTIVITY", "游玩活动", TravelJourneyDetailEnum.FirstType.TOUR_ACTIVITY.getValue()),
        VICTUALS("VICTUALS", "餐饮", TravelJourneyDetailEnum.FirstType.VICTUALS.getValue()),
        ENDING("ENDING", "结束旅程", TravelJourneyDetailEnum.FirstType.ENDING.getValue()),
        SHOPPING("SHOPPING", "购物", TravelJourneyDetailEnum.FirstType.SHOPPING.getValue()),
        BACK("BACK", "返程", TravelJourneyDetailEnum.FirstType.BACK.getValue()),
        BACK_HOTEL("BACK_HOTEL", "返回酒店", TravelJourneyDetailEnum.FirstType.BACK_HOTEL.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    @AllArgsConstructor
    @Getter
    enum SecondType {
        GATHER_POINT("GATHER_POINT", "集合点", FirstType.GATHER.getValue(), TravelJourneyDetailEnum.SecondType.GATHER_POINT.getValue()),
        PICKUP("PICKUP", "上门接", FirstType.GATHER.getValue(), TravelJourneyDetailEnum.SecondType.PICKUP.getValue()),

        CAR("CAR", "车", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.CAR.getValue()),
        BUS("BUS", "巴士", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.BUS.getValue()),
        WALK("WALK", "步行", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.WALK.getValue()),
        METRO("METRO", "地铁", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.METRO.getValue()),
        SHIP("SHIP", "船/各类船只", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.SHIP.getValue()),
        ROPEWAY("ROPEWAY", "索道", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.ROPEWAY.getValue()),
        CABLE_CAR("CABLE_CAR", "缆车", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.CABLE_CAR.getValue()),
        TRAIN("TRAIN", "火车", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.TRAIN.getValue()),
        CYCLING("CYCLING", "骑行(自行车/电瓶车/摩托车)", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.CYCLING.getValue()),
        WATER_BUS("WATER_BUS", "水上巴士", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.WATER_BUS.getValue()),
        SEAPLANE("SEAPLANE", "水上飞机", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.SEAPLANE.getValue()),
        FERRY("FERRY", "轮渡/摆渡", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.FERRY.getValue()),
        HELICOPTER("HELICOPTER", "直升机", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.HELICOPTER.getValue()),
        FLIVVER("FLIVVER", "小飞机", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.FLIVVER.getValue()),
        SCENIC_TRAFFIC("SCENIC_TRAFFIC", "景区小交通", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.SCENIC_TRAFFIC.getValue()),
        SPEEDBOAT("SPEEDBOAT", "快艇/皮划艇/游艇", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.SecondType.SPEEDBOAT.getValue()),

        ACTIVITY_SCENE("ACTIVITY_SCENE", "活动景点", FirstType.TOUR_ACTIVITY.getValue(), TravelJourneyDetailEnum.SecondType.ACTIVITY_SCENE.getValue()),
        FREE_ACTIVITY("FREE_ACTIVITY", "自由活动", FirstType.TOUR_ACTIVITY.getValue(), TravelJourneyDetailEnum.SecondType.FREE_ACTIVITY.getValue()),

        BREAKFAST("BREAKFAST", "早餐", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.SecondType.BREAKFAST.getValue()),
        LUNCH("LUNCH", "午餐", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.SecondType.LUNCH.getValue()),
        DINNER("DINNER", "晚餐", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.SecondType.DINNER.getValue()),
        AFTERNOON_TEA("AFTERNOON_TEA", "下午茶", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.SecondType.AFTERNOON_TEA.getValue()),
        MORNING_TEA("MORNING_TEA", "早茶", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.SecondType.MORNING_TEA.getValue()),

        DISSOLVE_SITE("DISSOLVE_SITE", "就地散团", FirstType.BACK.getValue(), TravelJourneyDetailEnum.SecondType.DISSOLVE_SITE.getValue()),
        BACK_DISSOLVE("BACK_DISSOLVE", "返程散团", FirstType.BACK.getValue(), TravelJourneyDetailEnum.SecondType.BACK_DISSOLVE.getValue()),
        SEND_BACK("SEND_BACK", "返程送回", FirstType.BACK.getValue(), TravelJourneyDetailEnum.SecondType.SEND_BACK.getValue()),

        BACK_HOTEL_DISSOLVE("BACK_HOTEL_DISSOLVE", "酒店散团", FirstType.BACK_HOTEL.getValue(), TravelJourneyDetailEnum.SecondType.BACK_HOTEL_DISSOLVE.getValue()),
        BACK_HOTEL_BACK("BACK_HOTEL_BACK", "返程酒店", FirstType.BACK_HOTEL.getValue(), TravelJourneyDetailEnum.SecondType.BACK_HOTEL_BACK.getValue()),
        ;

        private final String value;
        private final String name;
        private final String parentValue;
        private final Integer funValue;
    }

    @AllArgsConstructor
    @Getter
    enum ThirdType {
        CHINESE("CHINESE", "中式", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.CHINESE.getValue()),
        WESTERN("WESTERN", "西式", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.WESTERN.getValue()),
        CHAFING_DISH("CHAFING_DISH", "火锅", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.CHAFING_DISH.getValue()),
        BARBECUE("BARBECUE", "烧烤", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.BARBECUE.getValue()),
        SNACK("SNACK", "小吃", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.SNACK.getValue()),
        SELF("SELF", "自助", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.SELF.getValue()),
        LOCAL("LOCAL", "当地特色餐食", FirstType.VICTUALS.getValue(), TravelJourneyDetailEnum.ThirdType.LOCAL.getValue()),

        GO("GO", "前往", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.ThirdType.GO.getValue()),
        BACK("BACK", "返回", FirstType.TRAFFIC.getValue(), TravelJourneyDetailEnum.ThirdType.BACK.getValue()),

        CAR("CAR", "车", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.CAR.getValue()),
        BUS("BUS", "巴士", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.BUS.getValue()),
        WALK("WALK", "步行", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.WALK.getValue()),
        METRO("METRO", "地铁", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.METRO.getValue()),
        SHIP("SHIP", "船/各类船只", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.SHIP.getValue()),
        ROPEWAY("ROPEWAY", "索道", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.ROPEWAY.getValue()),
        CABLE_CAR("CABLE_CAR", "缆车", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.CABLE_CAR.getValue()),
        TRAIN("TRAIN", "火车", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.TRAIN.getValue()),
        CYCLING("CYCLING", "骑行(自行车/电瓶车/摩托车)", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.CYCLING.getValue()),
        WATER_BUS("WATER_BUS", "水上巴士", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.WATER_BUS.getValue()),
        SEAPLANE("SEAPLANE", "水上飞机", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.SEAPLANE.getValue()),
        FERRY("FERRY", "轮渡/摆渡", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.FERRY.getValue()),
        HELICOPTER("HELICOPTER", "直升机", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.HELICOPTER.getValue()),
        FLIVVER("FLIVVER", "小飞机", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.FLIVVER.getValue()),
        SCENIC_TRAFFIC("SCENIC_TRAFFIC", "景区小交通", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.SCENIC_TRAFFIC.getValue()),
        SPEEDBOAT("SPEEDBOAT", "快艇/皮划艇/游艇", FirstType.BACK.getValue(), TravelJourneyDetailEnum.ThirdType.SPEEDBOAT.getValue()),
        ;

        private final String value;
        private final String name;
        private final String parentValue;
        private final Integer funValue;
    }

    @AllArgsConstructor
    @Getter
    enum PaidServiceType {
        SUPPORT("SUPPORT", "超出指定区域提供", 1),
        NOT_SUPPORT("NOT_SUPPORT", "超出指定区域不提供", 2),
        INCLUDE("INCLUDE", "费用包含", 3),
        SELF("SELF", "需自费", 4),// 费用自理
        FREE("FREE", "无需门票", 5),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;

    }

    @AllArgsConstructor
    @Getter
    enum VisitType {
        OUTSIDE_VISIT("OUTSIDE_VISIT", "不入内参观", 1),//外观
        CAR_VISIT("CAR_VISIT", "乘车参观", 2),//车观
        BOAT_VISIT("BOAT_VISIT", "乘船参观", 3),//船观
        INSIDE_VISIT("INSIDE_VISIT", "入内参观", 4),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }

    /**
     * POI预约类型
     */
    @AllArgsConstructor
    @Getter
    enum AdvanceReservation {
        //预约类型：0.无需提前预约10.商家代预约20.客人自行预约 21.客人提前预约后 ，商家购买门票
//    NO_BOOK(0, "无需提前预约"),
        SUPPLIER_BOOK("SUPPLIER_BOOK", "商家代预约", 10),
        CUSTOMER_SELF_BOOK("CUSTOMER_SELF_BOOK", "需客人自行预约", 20),
        CUSTOMER_BOOK_SUPPLIER_PAY("CUSTOMER_BOOK_SUPPLIER_PAY", "需客人提前预约后 ，商家购买门票", 21),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }

    @AllArgsConstructor
    @Getter
    enum CostUnitEnum {
        PERSON("PERSON", "人", TravelJourneyDetailEnum.CostUnit.PERSON.getValue()),
        PORTION("PORTION", "份", TravelJourneyDetailEnum.CostUnit.PORTION.getValue()),
        SHEET("SHEET", "张", 3),
        TIME("TIME", "次", 4),
        SUITE("SUITE", "套", 5),
        VEHICLE("VEHICLE", "辆", 6),
        SHIP("SHIP", "艘", 7),
        HOUR("HOUR", "小时", 8),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;
    }


    @AllArgsConstructor
    @Getter
    enum IncludeExplain {
        // 讲解类型
        INCLUDE_EXPLAIN("INCLUDE_EXPLAIN", "含讲解", 1),
        EXCLUDE_EXPLAIN("EXCLUDE_EXPLAIN", "不含讲解", 0),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;

    }
}
