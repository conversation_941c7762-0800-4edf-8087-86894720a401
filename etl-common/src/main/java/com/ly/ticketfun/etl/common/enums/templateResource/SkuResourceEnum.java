package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.SkuPriceEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * sku资源枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface SkuResourceEnum {
    /**
     * 单独售卖类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum SaleAloneType {
        CAN_SALE_ALONE("CAN_SALE_ALONE", "可单独购买", com.ly.localactivity.model.enums.tczbactivityresource.SkuResourceEnum.SaleAloneType.CAN_BUY_ALONE.getValue()),
        CAN_NOT_SALE_ALONE("CAN_NOT_SALE_ALONE", "不可单独购买", com.ly.localactivity.model.enums.tczbactivityresource.SkuResourceEnum.SaleAloneType.CAN_NOT_BUY_ALONE.getValue()),
        ONLY_CAN_SALE_ALONE("ONLY_CAN_SALE_ALONE", "仅可单独购买", com.ly.localactivity.model.enums.tczbactivityresource.SkuResourceEnum.SaleAloneType.ONLY_CAN_BUY_ALONE.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 库存类型
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @AllArgsConstructor
    @Getter
    enum StockType {
        LIMITED_STOCK("LIMITED_STOCK", "有限库存", SkuPriceEnum.StockType.LIMITED.getValue()),
        NO_LIMITED_STOCK("NO_LIMITED_STOCK", "无限库存",SkuPriceEnum.StockType.NO_LIMIT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }
}
