package com.ly.ticketfun.etl.common.utils;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.TimeZone;

@Slf4j
public class JsonUtils {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        mapper.getFactory().disable(JsonFactory.Feature.INTERN_FIELD_NAMES);
    }

    public static String toJson(Object object) {
        ObjectMapper om = new ObjectMapper();
        try {
            return om.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> T fromJson(String json, Class<T> tClass) throws IOException {
        if (json == null || json.isEmpty()) {
            return null;
        }
        ObjectMapper om = new ObjectMapper();
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return om.readValue(json, tClass);
    }

    public static <T> List<T> fromJsonList(String json) throws IOException {
        if (json == null || json.isEmpty()) {
            return null;
        }
        ObjectMapper om = new ObjectMapper();
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return om.readValue(json, new TypeReference<List<T>>() {
        });
    }

    /**
     * string转object 用于转为集合对象
     */
    public static <T> T string2Obj(String json, Class<?> collectionClass, Class<?>... elementClasses) throws Exception {
        if (json == null || json.isEmpty()) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
        return objectMapper.readValue(json, javaType);

    }

    /**
     * string 转 泛型抽象类
     *
     * @param json          json
     * @param typeReference 泛型抽象类
     * @return 返回泛型抽象类
     */
    public static <T> T string2GenericAbstract(String json, TypeReference<T> typeReference) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return mapper.readValue(json, typeReference);
        } catch (Exception e) {
        }
        return null;
    }

    public static <T> T string2GenericAbstractWarn(String json, TypeReference<T> typeReference, String f1, String f2) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return mapper.readValue(json, typeReference);
        } catch (Exception e) {
        }
        return null;
    }
}
