package com.ly.ticketfun.etl.common.enums.templateMarketing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
public interface MarketingEnum {

    /**
     *  价格促销类型
     */
    @AllArgsConstructor
    @Getter
    enum priceType{
        //价格促销类型 24301 折扣, 24302 金额
        DISCOUNT("DISCOUNT","折扣","24301"),
        AMOUNT("AMOUNT","金额","24302");

        private final String value;
        private final String name;
        private final String ticketValue;
    }

    /**
     *  扣减规则
     */
    @AllArgsConstructor
    @Getter
    enum reduceRuleTypeEnum{
        ORDER_PRICE("ORDER_PRICE","按订单价格","24102"),
        TICKET_NUM("TICKET_NUM","按票数","24101");
        private final String value;
        private final String name;
        private final String ticketValue;

    }

    /**
     *  计算促销金额类型
     */
    @AllArgsConstructor
    @Getter
    enum calculateAmountTypeEnum{
        ONLINE_PRICE("ONLINE_PRICE","网上价参与计算","0"),
        AGREEMENT_PRICE("AGREEMENT_PRICE","协议价参与计算","1");
        private final String value;
        private final String name;
        private final String ticketValue;

    }

    /**
     *  佣金规则
     */
    @AllArgsConstructor
    @Getter
    enum commissionRulesEnum{
        COMMISSION("COMMISSION","正佣","1"),
        NEGATIVE_COMMISSION("NEGATIVE_COMMISSION","负佣","2"),
        NEUTRAL_COMMISSION("NEUTRAL_COMMISSION","平佣","3"),
        COMMISSION_NEUTRAL_COMMISSION("COMMISSION_NEUTRAL_COMMISSION","正平佣","4"),
        NEUTRAL_NEGATIVE_COMMISSION("NEUTRAL_NEGATIVE_COMMISSION","平负佣","5"),
        ALL_COMMISSION("ALL_COMMISSION","正平负佣","6");
        private final String value;
        private final String name;
        private final String ticketValue;

    }

    /**
     * 会员类型
     */
    @AllArgsConstructor
    @Getter
    enum memberTypeEnum{
        NEW_MEMBER("NEW_MEMBER","新会员","1"),
        OLD_MEMBER("OLD_MEMBER","老会员","2"),
        NEW_OLD_MEMBER("NEW_OLD_MEMBER","新老会员","3");
        private final String value;
        private final String name;
        private final String ticketValue;
    }

    /**
     * 今日订是否可用
     */
    @AllArgsConstructor
    @Getter
    enum todayCanUseEnum{
        YES("YES","可用","1"),
        NO("NO","不可用","2");
        private final String value;
        private final String name;
        private final String ticketValue;
    }

    /**
     * 促销计算方式
     * 立减 折扣 满减阶梯 折扣阶梯 红包
     */
    @AllArgsConstructor
    @Getter
    enum promotionCalculateTypeEnum {
        LINE_REDUCE("LINE_REDUCE","立减"),
        DISCOUNT("DISCOUNT","折扣"),
        FULL_REDUCE("FULL_REDUCE","满减阶梯"),
        DISCOUNT_STEP("DISCOUNT_STEP","折扣阶梯"),
        RED_PACKET("RED_PACKET","红包");

        private final String value;
        private final String name;


    }
}
