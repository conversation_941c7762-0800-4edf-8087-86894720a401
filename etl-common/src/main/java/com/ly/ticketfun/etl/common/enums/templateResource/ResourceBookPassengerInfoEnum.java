package com.ly.ticketfun.etl.common.enums.templateResource;

import cn.hutool.core.util.StrUtil;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookFillInfoEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.QuestionTypeInfoEnum;
import com.ly.ticketfun.etl.common.enums.ticket.TravellerNeedTypeEnums;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 资源预订出游人信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceBookPassengerInfoEnum {
    /**
     * 问题类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionType {
        BASIC("BASIC", "基础问题", QuestionTypeInfoEnum.Type.BASIC.getValue()),
        CREDENTIALS("CREDENTIALS", "证件类问题", QuestionTypeInfoEnum.Type.CREDENTIALS.getValue()),
        ADDITIONAL("ADDITIONAL", "附加问题", QuestionTypeInfoEnum.Type.ADDITIONAL.getValue()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 出游人必填类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum PassengerRequiredType {
        NO_REQUIRED("NO_REQUIRED", "无需", MainResourceBookFillInfoEnum.TravellerInfoNeedType.NO_NEED.getValue(), TravellerNeedTypeEnums.UN_NEED.getCode()),
        REQUIRED_ONE("REQUIRED_ONE", "需要一个", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ONE_TRAVELLER.getValue(), TravellerNeedTypeEnums.One.getCode()),
        REQUIRED_ALL("REQUIRED_ALL", "需要所有", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ALL_TRAVELLER.getValue(), TravellerNeedTypeEnums.ALL.getCode()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 数据类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DataType {
        DATETIME("DATETIME", "日期及时间", "格式：yyyy-MM-dd HH:mm:ss", "DATETIME"),
        DATE("DATE", "日期", "格式：yyyy-MM-dd", "DATE"),
        TIME("TIME", "时间", "格式：HH:mm", "TIME"),
        TEXT("TEXT", "时间", "", "TEXT"),
        NUMBER("NUMBER", "数字", "", "NUMBER"),
        BOOLEAN("BOOLEAN", "布尔", "", "BOOLEAN"),
        SINGLE_ENUM("SINGLE_ENUM", "单选", "", "ENUM"),
        MULTIPLE_ENUM("MULTIPLE_ENUM", "多选", "", "ENUM"),
        ADDRESS("ADDRESS", "地址", "", "ADDRESS"),
        ;
        private final String value;
        private final String name;
        private final String description;
        private final String funValue;


        /**
         *
         *
         * @param funValue    玩乐dataType值
         * @param selectValue 选择类型
         * @return {@link DataType }
         */
        public static DataType funValue2DataType(String funValue, String selectValue) {
            if (Objects.equals(funValue, "ENUM")) {
                if (Objects.equals(selectValue, "SINGLE")) {
                    return DataType.SINGLE_ENUM;
                } else if (Objects.equals(selectValue, "MULTIPLE")) {
                    return DataType.MULTIPLE_ENUM;
                }
            }
            for (DataType dataType : DataType.values()) {
                if (dataType.getFunValue().equals(funValue)) {
                    return dataType;
                }
            }
            throw new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, "DataType", StrUtil.isBlank(selectValue) ? funValue : (funValue + '_' + selectValue));
        }
    }

    /**
     * 问题
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionInfo {
        // BASIC 基础问题 (B_ prefix)
        B_WEIGHT("B_WEIGHT", "体重（kg）", QuestionType.BASIC, "Weight"),
        B_SHOE_SIZE("B_SHOE_SIZE", "鞋码（欧码）", QuestionType.BASIC, "ShoeSize"),
        B_FIRST_NAME("B_FIRST_NAME", "英语名", QuestionType.BASIC, "FirstName"),
        B_LAST_NAME("B_LAST_NAME", "英语姓", QuestionType.BASIC, "LastName"),
        B_CARD_ID_VALIDITY("B_CARD_ID_VALIDITY", "证件有效期", QuestionType.BASIC, "CardIdValidity"),
        B_GENDER("B_GENDER", "性别", QuestionType.BASIC, "Gender"),
        B_COUNTRY_OR_DISTRICT("B_COUNTRY_OR_DISTRICT", "国家/地区", QuestionType.BASIC, "CountryOrDistrict"),
        B_BIRTHDAY("B_BIRTHDAY", "出生日期", QuestionType.BASIC, "Birthday"),
        B_HEIGHT("B_HEIGHT", "身高（cm）", QuestionType.BASIC, "Height"),
        B_GLASSES_DEGREE("B_GLASSES_DEGREE", "眼镜度数（度）", QuestionType.BASIC, "GlassesDegree"),
        B_HM_ENDORSEMENT_TYPE("B_HM_ENDORSEMENT_TYPE", "港澳签注类型", QuestionType.BASIC, "HMVisaType"),
        B_PROVINCE_OF_ISSUE("B_PROVINCE_OF_ISSUE", "签发地（省份）", QuestionType.BASIC, "PlaceOfIssue"),
        B_PROVINCE_OF_BIRTH("B_PROVINCE_OF_Birth", "出生地（省份）", QuestionType.BASIC, "BirthPlace"),
        B_CLIENT_ID("B_CLIENT_ID", "出行人ID", QuestionType.BASIC, "ClientId"),
        B_CHINA_NAME("B_CHINA_NAME", "中文姓名", QuestionType.BASIC, "ChinaName"),
        B_TEL_PHONE("B_TEL_PHONE", "联系电话", QuestionType.BASIC, "TelPhone"),
        B_TEL_PHONE_AREA_CODE("B_TEL_PHONE_AREA_CODE", "电话区号", QuestionType.BASIC, "TelPhoneAreaCode"),
        B_TEL_PHONE_INTERNATIONAL("B_TEL_PHONE_INTERNATIONAL", "国际电话", QuestionType.BASIC, "TelPhoneInternational"),

        // CREDENTIALS 证件类问题 (C_ prefix)
        C_PASSPORT("C_PASSPORT", "护照", QuestionType.CREDENTIALS, "Passport"),
        C_BACK_HOME_CARD("C_BACK_HOME_CARD", "回乡证", QuestionType.CREDENTIALS, "BackHomeCard"),
        C_TW_ID_CARD("C_TW_ID_CARD", "台胞证", QuestionType.CREDENTIALS, "TWIdCard"),
        C_DRIVE_CARD("C_DRIVE_CARD", "驾驶证", QuestionType.CREDENTIALS, "DriveCard"),
        C_ID_CARD("C_ID_CARD", "身份证", QuestionType.CREDENTIALS, "IdCard"),
        C_STUDENT_CARD("C_STUDENT_CARD", "学生证", QuestionType.CREDENTIALS, "StudentCard"),
        C_MILITARY_CARD("C_MILITARY_CARD", "军官证", QuestionType.CREDENTIALS, "MilitaryCard"),
        C_HM_PASS("C_HM_PASS", "港澳通行证", QuestionType.CREDENTIALS, "HMPass"),
        C_TW_PASS("C_TW_PASS", "台湾通行证", QuestionType.CREDENTIALS, "TWPass"),
        C_PERMANENT_RESIDENCE_CARD_FOREIGNERS("C_PERMANENT_RESIDENCE_CARD_FOREIGNERS", "外国人永久居留身份证", QuestionType.CREDENTIALS, "ForeigenersCard"),

        // ADDITIONAL 附加问题 (A_ prefix)
        A_ON_LOCATION("A_ON_LOCATION", "上车点", QuestionType.ADDITIONAL, "OnLocation"),
        A_OFF_LOCATION("A_OFF_LOCATION", "下车点", QuestionType.ADDITIONAL, "OffLocation"),
        A_MEETING_POINT("A_MEETING_POINT", "集合点", QuestionType.ADDITIONAL, "MeetingPoint"),
        A_SERVICE_LANGUAGE("A_SERVICE_LANGUAGE", "服务语言", QuestionType.ADDITIONAL, "ServiceLanguage"),
        A_MEETING_TIME("A_MEETING_TIME", "集合时间", QuestionType.ADDITIONAL, "MeetingTime"),
        A_DEPARTURE_TIME("A_DEPARTURE_TIME", "出发时间", QuestionType.ADDITIONAL, "DepartureTime"),
        A_ACTIVITY("A_ACTIVITY", "活动项目", QuestionType.ADDITIONAL, "Activity"),
        A_TO_TRAIN("A_TO_TRAIN", "车次（去程）", QuestionType.ADDITIONAL, "ToTrain"),
        A_TRAIN_NUMBER("A_TRAIN_NUMBER", "车次（回程）", QuestionType.ADDITIONAL, "TrainNumber"),
        A_GIVEN_ACTIVITY("A_GIVEN_ACTIVITY", "赠送项目", QuestionType.ADDITIONAL, "GivenActivity"),
        A_HOTEL_TEL_PHONE("A_HOTEL_TEL_PHONE", "酒店电话", QuestionType.ADDITIONAL, "HotelTelPhone"),
        A_ARRIVAL_DATE("A_ARRIVAL_DATE", "到达日期", QuestionType.ADDITIONAL, "ArrivalDate"),
        A_FLIGHT_NUMBER("A_FLIGHT_NUMBER", "航班号", QuestionType.ADDITIONAL, "FlightNumber"),
        A_LUGGAGE_NUM_SIZE("A_LUGGAGE_NUM_SIZE", "行李数量及尺寸", QuestionType.ADDITIONAL, "LuggageNumSize"),
        A_FLIGHT_TIME("A_FLIGHT_TIME", "航班起飞时间", QuestionType.ADDITIONAL, "FlightTime"),
        A_FLIGHT_END_TIME("A_FLIGHT_END_TIME", "航班抵达时间", QuestionType.ADDITIONAL, "FlightEndTime"),
        A_FLIGHT_DATE("A_FLIGHT_DATE", "航班起飞日期", QuestionType.ADDITIONAL, "FlightDate"),
        A_FLIGHT_END_DATE("A_FLIGHT_END_DATE", "航班抵达日期", QuestionType.ADDITIONAL, "FlightEndDate"),
        A_PASSENGER_NUM("A_PASSENGER_NUM", "出行人数", QuestionType.ADDITIONAL, "PassengerNum"),
        A_BUS_LINE("A_BUS_LINE", "包车预计路线", QuestionType.ADDITIONAL, "BusLine"),
        A_HOTEL_C_NAME("A_HOTEL_C_NAME", "出发酒店名（中文）", QuestionType.ADDITIONAL, "HotelCName"),
        A_DEP_HOTEL_ENG_NAME("A_DEP_HOTEL_ENG_NAME", "出发酒店名（英文）", QuestionType.ADDITIONAL, "DepHotelEngName"),
        A_HOTEL_ADDRESS("A_HOTEL_ADDRESS", "出发酒店地址", QuestionType.ADDITIONAL, "HotelAddress"),
        A_D_HOTEL_C_NAME("A_D_HOTEL_C_NAME", "到达酒店名（中文）", QuestionType.ADDITIONAL, "DHotelCName"),
        A_ARR_HOTEL_ENG_NAME("A_ARR_HOTEL_ENG_NAME", "到达酒店名（英文）", QuestionType.ADDITIONAL, "ArrHotelEngName"),
        A_D_HOTEL_ADDRESS("A_D_HOTEL_ADDRESS", "到达酒店地址", QuestionType.ADDITIONAL, "DHotelAddress"),
        A_CHILD_SAFE_NUM("A_CHILD_SAFE_NUM", "儿童安全座椅", QuestionType.ADDITIONAL, "ChildSafeNum"),
        A_RETURN_FLIGHT("A_RETURN_FLIGHT", "回程航班号", QuestionType.ADDITIONAL, "ReturnFlight"),
        A_FREE_BABY_COUNT("A_FREE_BABY_COUNT", "免费婴儿人数", QuestionType.ADDITIONAL, "FreeBabyCount"),
        A_DEPART_FLIGHT("A_DEPART_FLIGHT", "去程航班号", QuestionType.ADDITIONAL, "DepartFlight"),
        A_WE_CHAT("A_WE_CHAT", "微信号", QuestionType.ADDITIONAL, "WeChat"),
        A_DEP_HOTEL_ENG_ADDRESS("A_DEP_HOTEL_ENG_ADDRESS", "出发酒店地址（英文）", QuestionType.ADDITIONAL, "DepHotelEngAddress"),
        A_ARR_HOTEL_ENG_ADDRESS("A_ARR_HOTEL_ENG_ADDRESS", "到达酒店地址（英文）", QuestionType.ADDITIONAL, "ArrHotelEngAddress"),
        A_ARRIVAL_TIME("A_ARRIVAL_TIME", "到达时间", QuestionType.ADDITIONAL, "ArrivalTime"),
        A_TRAIN("A_TRAIN", "车次", QuestionType.ADDITIONAL, "Train"),
        A_OLD_INFO("A_OLD_INFO", "老人信息", QuestionType.ADDITIONAL, "OldInfo"),
        A_CHILDREN_INFO("A_CHILDREN_INFO", "儿童信息", QuestionType.ADDITIONAL, "ChildrenInfo"),
        A_HOME("A_HOME", "常住地", QuestionType.ADDITIONAL, "Home"),
        A_CERTIFICATE("A_CERTIFICATE", "所持都内在住证明书（需携带到现场核验）", QuestionType.ADDITIONAL, "Certificate"),
        A_PICKUP("A_PICKUP", "接送点", QuestionType.ADDITIONAL, "Pickup"),
        A_TRANSPORTATION_USED("A_TRANSPORTATION_USED", "乘坐的交通工具", QuestionType.ADDITIONAL, "TransportationUsed"),
        A_FLIGHT_NAME("A_FLIGHT_NAME", "航班名称", QuestionType.ADDITIONAL, "FlightName"),
        A_SHIP_NUMBER("A_SHIP_NUMBER", "船次", QuestionType.ADDITIONAL, "ShipNumber"),
        A_TRAIN_SUPPLIER_NAME("A_TRAIN_SUPPLIER_NAME", "火车供应商名称", QuestionType.ADDITIONAL, "TrainSupplierName"),
        A_PICKUP_POINT("A_PICKUP_POINT", "取件点", QuestionType.ADDITIONAL, "PickupPoint"),
        A_DELIVERY_ADDRESS("A_DELIVERY_ADDRESS", "配送地址", QuestionType.ADDITIONAL, "DeliveryAddress"),
        A_EXPRESS_TYPE("A_EXPRESS_TYPE", "快递类型", QuestionType.ADDITIONAL, "ExpressType"),
        A_START_USE_CARD_DATE("A_START_USE_CARD_DATE", "开始用卡日期", QuestionType.ADDITIONAL, "StartUseCardDate"),
        A_ICCID("A_ICCID", "ICCID", QuestionType.ADDITIONAL, "ICCID"),
        ;

        private final String value;
        private final String name;
        private final QuestionType questionType;
        private final String funValue;
    }
}
