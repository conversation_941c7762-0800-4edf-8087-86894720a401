package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookFillInfoEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.QuestionTypeInfoEnum;
import com.ly.ticketfun.etl.common.enums.ticket.TravellerNeedTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源预订出游人信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceBookPassengerInfoEnum {
    /**
     * 问题类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionType {
        BASIC("BASIC", "基础问题", QuestionTypeInfoEnum.Type.BASIC.getValue()),
        CREDENTIALS("CREDENTIALS", "证件类问题", QuestionTypeInfoEnum.Type.CREDENTIALS.getValue()),
        ADDITIONAL("ADDITIONAL", "附加问题", QuestionTypeInfoEnum.Type.ADDITIONAL.getValue()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 出游人必填类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum PassengerRequiredType {
        NO_REQUIRED("NO_REQUIRED", "无需", MainResourceBookFillInfoEnum.TravellerInfoNeedType.NO_NEED.getValue(), TravellerNeedTypeEnums.UN_NEED.getCode()),
        REQUIRED_ONE("REQUIRED_ONE", "需要一个", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ONE_TRAVELLER.getValue(), TravellerNeedTypeEnums.One.getCode()),
        REQUIRED_ALL("REQUIRED_ALL", "需要所有", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ALL_TRAVELLER.getValue(), TravellerNeedTypeEnums.ALL.getCode()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 数据类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DataType {
        DATETIME("DATETIME", "日期及时间", "格式：yyyy-MM-dd HH:mm:ss"),
        DATE("DATE", "日期", "格式：yyyy-MM-dd"),
        TIME("TIME", "时间", "格式：HH:mm"),
        TEXT("TEXT", "时间", ""),
        NUMBER("NUMBER", "数字", ""),
        BOOLEAN("BOOLEAN", "布尔", ""),
        SINGLE_ENUM("ENUM", "单选", ""),
        MULTIPLE_ENUM("ENUM", "多选", ""),
        ADDRESS("ADDRESS", "地址", ""),
        ;
        private final String value;
        private final String name;
        private final String description;
    }

    /**
     * 问题
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionInfo {
        B_CN_NAME("B_CN_NAME", "中文姓名", QuestionType.BASIC, "ChinaName"),
        B_EN_FIRST_NAME("B_EN_FIRST_NAME", "英语名", QuestionType.BASIC, "FirstName"),
        B_EN_LAST_NAME("B_EN_LAST_NAME", "英语姓", QuestionType.BASIC, "LastName"),
        B_TEL_PHONE("B_TEL_PHONE", "联系电话", QuestionType.BASIC, "TelPhone"),
        B_TEL_PHONE_INTERNATIONAL("B_TEL_PHONE_INTERNATIONAL", "国际联系电话", QuestionType.BASIC, ""),
        B_TEL_PHONE_AREA_CODE("B_TEL_PHONE_AREA_CODE", "电话区号", QuestionType.BASIC, "TelPhoneAreaCode"),
        B_HEIGHT("B_HEIGHT", "身高（cm）", QuestionType.BASIC, "Height"),
        B_GENDER("B_GENDER", "性别", QuestionType.BASIC, "Gender"),
        B_Birthday("B_BIRTHDAY", "出生日期", QuestionType.BASIC, "BirthDay"),
        B_COUNTRY_OR_DISTRICT("B_COUNTRY_OR_DISTRICT", "国家/地区", QuestionType.BASIC, "CountryOrDistrict"),
        B_WEIGHT("B_WEIGHT", "体重（kg）", QuestionType.BASIC, "Weight"),
        B_SHOE_SIZE_EU("B_SHOE_SIZE_EU", "鞋码（欧码）", QuestionType.BASIC, "ShoeSize"),
        B_CERTIFICATE_VALIDITY_PERIOD("B_CERTIFICATE_VALIDITY_PERIOD", "证件有效期", QuestionType.BASIC, "CardIdValidity"),
        B_GLASSES_DEGREE("B_GLASSES_DEGREE", "眼镜度数（度）", QuestionType.BASIC, "GlassesDegree"),
        B_HM_ENDORSEMENT_TYPE("B_HM_ENDORSEMENT_TYPE", "港澳签注类型", QuestionType.BASIC, "HMVisaType"),
        B_PROVINCE_OF_ISSUE("B_PROVINCE_OF_ISSUE", "签发地（省份）", QuestionType.BASIC, "PlaceOfIssue"),
        B_PROVINCE_OF_BIRTH("B_PROVINCE_OF_Birth", "出生地（省份）", QuestionType.BASIC, "BirthPlace"),
        B_CLIENT_ID("B_CLIENT_ID", "出行人ID", QuestionType.BASIC, "ClientId"),

        C_ID_CARD("C_ID_CARD", "身份证", QuestionType.CREDENTIALS, "IdCard"),
        C_STUDENT_CARD("C_STUDENT_CARD", "学生证", QuestionType.CREDENTIALS, "StudentCard"),
        C_HM_PASS("C_HM_PASS", "港澳通行证", QuestionType.CREDENTIALS, "HMPass"),
        C_TW_PASS("C_TW_PASS", "台湾通行证", QuestionType.CREDENTIALS, "TWPass"),
        C_PASSPORT("C_PASSPORT", "护照", QuestionType.CREDENTIALS, ""),
        C_DRIVE_CARD("C_DRIVE_CARD", "驾驶证", QuestionType.CREDENTIALS, "DriveCard"),
        C_TW_ID_CARD("C_TW_ID_CARD", "台胞证", QuestionType.CREDENTIALS, "TWIdCard"),
        C_BACK_HOME_CARD("C_BACK_HOME_CARD", "回乡证", QuestionType.CREDENTIALS, "BackHomeCard"),
        C_PERMANENT_RESIDENCE_CARD_FOREIGNERS("C_PERMANENT_RESIDENCE_CARD_FOREIGNERS", "外国人永久居留身份证", QuestionType.CREDENTIALS, "ForeigenersCard"),
        C_MILITARY_CARD("C_MILITARY_CARD", "军官证", QuestionType.CREDENTIALS, "SoldierCard"),

        A_ON_LOCATION("A_ON_LOCATION", "上车点", QuestionType.ADDITIONAL, "OnLocation"),
        A_OFF_LOCATION("A_OFF_LOCATION", "下车点", QuestionType.ADDITIONAL, "OffLocation"),

        ;
        private final String value;
        private final String name;
        private final QuestionType questionType;
        private final String funValue;
    }
}
