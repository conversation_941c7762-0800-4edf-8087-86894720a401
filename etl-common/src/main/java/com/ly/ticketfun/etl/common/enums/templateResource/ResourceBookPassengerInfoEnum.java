package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookFillInfoEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.QuestionTypeInfoEnum;
import com.ly.ticketfun.etl.common.enums.ticket.TravellerNeedTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源预订出游人信息枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface ResourceBookPassengerInfoEnum {
    /**
     * 问题类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionType {
        BASIC("BASIC", "基础问题", QuestionTypeInfoEnum.Type.BASIC.getValue()),
        CREDENTIALS("CREDENTIALS", "证件类问题", QuestionTypeInfoEnum.Type.CREDENTIALS.getValue()),
        ADDITIONAL("ADDITIONAL", "附加问题", QuestionTypeInfoEnum.Type.ADDITIONAL.getValue()),
        ;
        private final String value;
        private final String name;
        private final String funValue;
    }

    /**
     * 出游人必填类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum PassengerRequiredType {
        NO_REQUIRED("NO_REQUIRED", "无需", MainResourceBookFillInfoEnum.TravellerInfoNeedType.NO_NEED.getValue(), TravellerNeedTypeEnums.UN_NEED.getCode()),
        REQUIRED_ONE("REQUIRED_ONE", "需要一个", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ONE_TRAVELLER.getValue(), TravellerNeedTypeEnums.One.getCode()),
        REQUIRED_ALL("REQUIRED_ALL", "需要所有", MainResourceBookFillInfoEnum.TravellerInfoNeedType.ALL_TRAVELLER.getValue(), TravellerNeedTypeEnums.ALL.getCode()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 数据类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum DataType {
        DATETIME("DATETIME", "日期及时间", "格式：yyyy-MM-dd HH:mm:ss"),
        DATE("DATE", "日期", "格式：yyyy-MM-dd"),
        TIME("TIME", "时间", "格式：HH:mm"),
        TEXT("TEXT", "时间", ""),
        NUMBER("NUMBER", "数字", ""),
        BOOLEAN("BOOLEAN", "布尔", ""),
        SINGLE_ENUM("ENUM", "单选", ""),
        MULTIPLE_ENUM("ENUM", "多选", ""),
        ADDRESS("ADDRESS", "地址", ""),
        ;
        private final String value;
        private final String name;
        private final String description;
    }

    /**
     * 问题
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum QuestionInfo {
        // BASIC 基础问题 (B_ prefix)
        B_WEIGHT("B_WEIGHT", "体重（kg）", QuestionType.BASIC, "Weight", 9991),
        B_SHOE_SIZE("B_SHOE_SIZE", "鞋码（欧码）", QuestionType.BASIC, "ShoeSize", 9990),
        B_FIRST_NAME("B_FIRST_NAME", "英语名", QuestionType.BASIC, "FirstName", 9996),
        B_LAST_NAME("B_LAST_NAME", "英语姓", QuestionType.BASIC, "LastName", 9995),
        B_CARD_ID_VALIDITY("B_CARD_ID_VALIDITY", "证件有效期", QuestionType.BASIC, "CardIdValidity", 9989),
        B_GENDER("B_GENDER", "性别", QuestionType.BASIC, "Gender", 9994),
        B_COUNTRY_OR_DISTRICT("B_COUNTRY_OR_DISTRICT", "国家/地区", QuestionType.BASIC, "CountryOrDistrict", 9992),
        B_BIRTHDAY("B_BIRTHDAY", "出生日期", QuestionType.BASIC, "Birthday", 9993),
        B_HEIGHT("B_HEIGHT", "身高（cm）", QuestionType.BASIC, "Height", 9997),
        B_GLASSES_DEGREE("B_GLASSES_DEGREE", "眼镜度数（度）", QuestionType.BASIC, "GlassesDegree", 9988),
        B_CLIENT_ID("B_CLIENT_ID", "出行人ID", QuestionType.BASIC, "ClientId", 9984),
        B_CHINA_NAME("B_CHINA_NAME", "中文姓名", QuestionType.BASIC, "ChinaName", 10000),
        B_BIRTH_PLACE("B_BIRTH_PLACE", "出生地(省份)", QuestionType.BASIC, "BirthPlace", 9985),
        B_HM_VISA_TYPE("B_HM_VISA_TYPE", "港澳签注类型", QuestionType.BASIC, "HMVisaType", 9987),
        B_PLACE_OF_ISSUE("B_PLACE_OF_ISSUE", "签发地（省份）", QuestionType.BASIC, "PlaceOfIssue", 9986),
        B_TEL_PHONE("B_TEL_PHONE", "联系电话", QuestionType.BASIC, "TelPhone", 9999),
        B_TEL_PHONE_AREA_CODE("B_TEL_PHONE_AREA_CODE", "电话区号", QuestionType.BASIC, "TelPhoneAreaCode", 9998),

        // CREDENTIALS 证件类问题 (C_ prefix)
        C_PASSPORT("C_PASSPORT", "护照", QuestionType.CREDENTIALS, "Passport", 7996),
        C_BACK_HOME_CARD("C_BACK_HOME_CARD", "回乡证", QuestionType.CREDENTIALS, "BackHomeCard", 7993),
        C_TW_ID_CARD("C_TW_ID_CARD", "台胞证", QuestionType.CREDENTIALS, "TWIdCard", 7994),
        C_DRIVE_CARD("C_DRIVE_CARD", "驾驶证", QuestionType.CREDENTIALS, "DriveCard", 7995),
        C_FOREIGNERS_CARD("C_FOREIGNERS_CARD", "外国人永久居住证", QuestionType.CREDENTIALS, "ForeigenersCard", 7992),
        C_ID_CARD("C_ID_CARD", "身份证", QuestionType.CREDENTIALS, "IdCard", 8000),
        C_STUDENT_CARD("C_STUDENT_CARD", "学生证", QuestionType.CREDENTIALS, "StudentCard", 7999),
        C_MILITARY_CARD("C_MILITARY_CARD", "军官证", QuestionType.CREDENTIALS, "MilitaryCard", 7991),
        C_HM_PASS("C_HM_PASS", "港澳通行证", QuestionType.CREDENTIALS, "HMPass", 7998),
        C_TW_PASS("C_TW_PASS", "台湾通行证", QuestionType.CREDENTIALS, "TWPass", 7997),

        // ADDITIONAL 附加问题 (A_ prefix)
        A_ON_LOCATION("A_ON_LOCATION", "上车点", QuestionType.ADDITIONAL, "OnLocation", 0),
        A_OFF_LOCATION("A_OFF_LOCATION", "下车点", QuestionType.ADDITIONAL, "OffLocation", 1),
        A_MEETING_POINT("A_MEETING_POINT", "集合点", QuestionType.ADDITIONAL, "MeetingPoint", 2),
        A_SERVICE_LANGUAGE("A_SERVICE_LANGUAGE", "服务语言", QuestionType.ADDITIONAL, "ServiceLanguage", 3),
        A_MEETING_TIME("A_MEETING_TIME", "集合时间", QuestionType.ADDITIONAL, "MeetingTime", 4),
        A_DEPARTURE_TIME("A_DEPARTURE_TIME", "出发时间", QuestionType.ADDITIONAL, "DepartureTime", 5),
        A_ACTIVITY("A_ACTIVITY", "活动项目", QuestionType.ADDITIONAL, "Activity", 6),
        A_TO_TRAIN("A_TO_TRAIN", "车次（去程）", QuestionType.ADDITIONAL, "ToTrain", 7),
        A_TRAIN_NUMBER("A_TRAIN_NUMBER", "车次（回程）", QuestionType.ADDITIONAL, "TrainNumber", 8),
        A_GIVEN_ACTIVITY("A_GIVEN_ACTIVITY", "赠送项目", QuestionType.ADDITIONAL, "GivenActivity", 9),
        A_HOTEL_TEL_PHONE("A_HOTEL_TEL_PHONE", "酒店电话", QuestionType.ADDITIONAL, "HotelTelPhone", 10),
        A_ARRIVAL_DATE("A_ARRIVAL_DATE", "到达日期", QuestionType.ADDITIONAL, "ArrivalDate", 11),
        A_FLIGHT_NUMBER("A_FLIGHT_NUMBER", "航班号", QuestionType.ADDITIONAL, "FlightNumber", 12),
        A_LUGGAGE_NUM_SIZE("A_LUGGAGE_NUM_SIZE", "行李数量及尺寸", QuestionType.ADDITIONAL, "LuggageNumSize", 13),
        A_FLIGHT_TIME("A_FLIGHT_TIME", "航班起飞时间", QuestionType.ADDITIONAL, "FlightTime", 14),
        A_FLIGHT_END_TIME("A_FLIGHT_END_TIME", "航班抵达时间", QuestionType.ADDITIONAL, "FlightEndTime", 15),
        A_FLIGHT_DATE("A_FLIGHT_DATE", "航班起飞日期", QuestionType.ADDITIONAL, "FlightDate", 16),
        A_FLIGHT_END_DATE("A_FLIGHT_END_DATE", "航班抵达日期", QuestionType.ADDITIONAL, "FlightEndDate", 17),
        A_PASSENGER_NUM("A_PASSENGER_NUM", "出行人数", QuestionType.ADDITIONAL, "PassengerNum", 18),
        A_BUS_LINE("A_BUS_LINE", "包车预计路线", QuestionType.ADDITIONAL, "BusLine", 19),
        A_HOTEL_C_NAME("A_HOTEL_C_NAME", "出发酒店名（中文）", QuestionType.ADDITIONAL, "HotelCName", 20),
        A_DEP_HOTEL_ENG_NAME("A_DEP_HOTEL_ENG_NAME", "出发酒店名（英文）", QuestionType.ADDITIONAL, "DepHotelEngName", 21),
        A_HOTEL_ADDRESS("A_HOTEL_ADDRESS", "出发酒店地址", QuestionType.ADDITIONAL, "HotelAddress", 22),
        A_D_HOTEL_C_NAME("A_D_HOTEL_C_NAME", "到达酒店名（中文）", QuestionType.ADDITIONAL, "DHotelCName", 23),
        A_ARR_HOTEL_ENG_NAME("A_ARR_HOTEL_ENG_NAME", "到达酒店名（英文）", QuestionType.ADDITIONAL, "ArrHotelEngName", 24),
        A_D_HOTEL_ADDRESS("A_D_HOTEL_ADDRESS", "到达酒店地址", QuestionType.ADDITIONAL, "DHotelAddress", 25),
        A_CHILD_SAFE_NUM("A_CHILD_SAFE_NUM", "儿童安全座椅", QuestionType.ADDITIONAL, "ChildSafeNum", 26),
        A_RETURN_FLIGHT("A_RETURN_FLIGHT", "回程航班号", QuestionType.ADDITIONAL, "ReturnFlight", 27),
        A_FREE_BABY_COUNT("A_FREE_BABY_COUNT", "免费婴儿人数", QuestionType.ADDITIONAL, "FreeBabyCount", 28),
        A_DEPART_FLIGHT("A_DEPART_FLIGHT", "去程航班号", QuestionType.ADDITIONAL, "DepartFlight", 29),
        A_WE_CHAT("A_WE_CHAT", "微信号", QuestionType.ADDITIONAL, "WeChat", 30),
        A_DEP_HOTEL_ENG_ADDRESS("A_DEP_HOTEL_ENG_ADDRESS", "出发酒店地址（英文）", QuestionType.ADDITIONAL, "DepHotelEngAddress", 31),
        A_ARR_HOTEL_ENG_ADDRESS("A_ARR_HOTEL_ENG_ADDRESS", "到达酒店地址（英文）", QuestionType.ADDITIONAL, "ArrHotelEngAddress", 32),
        A_ARRIVAL_TIME("A_ARRIVAL_TIME", "到达时间", QuestionType.ADDITIONAL, "ArrivalTime", 33),
        A_TRAIN("A_TRAIN", "车次", QuestionType.ADDITIONAL, "Train", 34),
        A_OLD_INFO("A_OLD_INFO", "老人信息", QuestionType.ADDITIONAL, "OldInfo", 35),
        A_CHILDREN_INFO("A_CHILDREN_INFO", "儿童信息", QuestionType.ADDITIONAL, "ChildrenInfo", 36),
        A_HOME("A_HOME", "常住地", QuestionType.ADDITIONAL, "Home", 37),
        A_CERTIFICATE("A_CERTIFICATE", "所持都内在住证明书（需携带到现场核验）", QuestionType.ADDITIONAL, "Certificate", 38),
        A_PICKUP("A_PICKUP", "接送点", QuestionType.ADDITIONAL, "Pickup", 39),
        A_TRANSPORTATION_USED("A_TRANSPORTATION_USED", "乘坐的交通工具", QuestionType.ADDITIONAL, "TransportationUsed", 40),
        A_FLIGHT_NAME("A_FLIGHT_NAME", "航班名称", QuestionType.ADDITIONAL, "FlightName", 41),
        A_SHIP_NUMBER("A_SHIP_NUMBER", "船次", QuestionType.ADDITIONAL, "ShipNumber", 42),
        A_TRAIN_SUPPLIER_NAME("A_TRAIN_SUPPLIER_NAME", "火车供应商名称", QuestionType.ADDITIONAL, "TrainSupplierName", 43),
        A_PICKUP_POINT("A_PICKUP_POINT", "取件点", QuestionType.ADDITIONAL, "PickupPoint", 44),
        A_DELIVERY_ADDRESS("A_DELIVERY_ADDRESS", "配送地址", QuestionType.ADDITIONAL, "DeliveryAddress", 45),
        A_EXPRESS_TYPE("A_EXPRESS_TYPE", "快递类型", QuestionType.ADDITIONAL, "ExpressType", 46),
        A_START_USE_CARD_DATE("A_START_USE_CARD_DATE", "开始用卡日期", QuestionType.ADDITIONAL, "StartUseCardDate", 47),
        A_ICCID("A_ICCID", "ICCID", QuestionType.ADDITIONAL, "ICCID", 48),
        ;

        private final String value;
        private final String name;
        private final QuestionType questionType;
        private final String funValue;
        private final Integer sortOrder;
    }
}
