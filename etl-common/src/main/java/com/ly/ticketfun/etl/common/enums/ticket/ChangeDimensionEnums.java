package com.ly.ticketfun.etl.common.enums.ticket;

import lombok.Getter;

@Getter
public enum ChangeDimensionEnums {
    /**
     * 0游玩日期	1出游人身份证	2游玩日期+出游人身份证
     */
    TRAVEL_DATE(0, "游玩日期"),
    ID(1, "出游人身份证"),
    TRAVEL_DATE_ID(2, "游玩日期+出游人身份证");

    private final Integer code;
    private final String message;

    ChangeDimensionEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getName(int code) {
        for (ChangeDimensionEnums c : ChangeDimensionEnums.values()) {
            if (c.getCode() == code) {
                return c.message;
            }
        }
        return null;
    }
}
