package com.ly.ticketfun.etl.common.enums.templateResource;

import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.CancelChangeRuleEnum;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import com.ly.ticketfun.etl.common.enums.ticket.ChangeDimensionEnums;
import com.ly.ticketfun.etl.common.enums.ticket.RefundTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款政策枚举
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
public interface RefundPolicyEnum {
    /**
     * 退款类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum RefundType {
        FREE("FREE", "未使用随时退", CancelChangeRuleEnum.RuleType.ANY_TIME_WHEN_UN_USE.getValue(), RefundTypeEnums.AnyTimeRefund.getCode()),
        LOSS("LOSS", "有损退", CancelChangeRuleEnum.RuleType.BASE_RULE.getValue(), RefundTypeEnums.ConditionRefund.getCode()),
        REFUSE("REFUSE", "不可退", CancelChangeRuleEnum.RuleType.CAN_NOT.getValue(), RefundTypeEnums.NoRefund.getCode()),
        CONDITION("CUSTOM", "自定义", CancelChangeRuleEnum.RuleType.CUSTOM.getValue(), null),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    @AllArgsConstructor
    @Getter
    enum ChangeType {
        UN("UN", "不支持改", null, 0),
        CONDITION("CONDITION", "条件改", null, 1),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    @AllArgsConstructor
    @Getter
    enum DetailType {
        REFUND("REFUND", "退"),
        CHANGE("CHANGE", "改"),
        ;
        private final String value;
        private final String name;
    }

    @AllArgsConstructor
    @Getter
    enum ChangeDimensionType {
        TRAVEL_DATE("TRAVEL_DATE", "游玩日期", null, ChangeDimensionEnums.TRAVEL_DATE.getCode()),
        ID("ID", "出游人身份证", null, ChangeDimensionEnums.ID.getCode()),
        TRAVEL_DATE_ID("TRAVEL_DATE_ID", "游玩日期+出游人身份证", null, ChangeDimensionEnums.TRAVEL_DATE_ID.getCode()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 是否部分退
     */
    @AllArgsConstructor
    @Getter
    enum PartialRefund {
        //0不支持1按出游人2按二维码
        YES("YES", "支持部分退", WhetherEnum.YES.getValue(), null),
        NO("NO", "不支持部分退", WhetherEnum.NO.getValue(), 0),
        TRAVELLER("TRAVELLER", "按出游人部分退", null, 1),
        CODE("CODE", "按二维码部分退", null, 2),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
        private final Integer ticketValue;
    }

    /**
     * 日期限制类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum TimeLimitType {
        NO_TIME_LIMIT("NO_TIME_LIMIT", "无时间限制", CancelChangeRuleEnum.DateType.NO_TIME_LIMIT.getValue()),
        TIME_LIMIT("TIME_LIMIT", "有时间限制", CancelChangeRuleEnum.DateType.TIME_LIMIT.getValue()),
        ;
        private final String value;
        private final String name;
        private final Integer funValue;
    }

    /**
     * 比较时间类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum CompareTimeType {
        TRAVEL_DATE(TimeNodeEnum.USAGE_DATE),
        SPECIFY_DATE(TimeNodeEnum.SPECIFY_DATE),
        NO_DATE_LIMIT(TimeNodeEnum.NO_DATE_LIMIT),
        CREATE_DATE(TimeNodeEnum.CREATE_DATE),
        SESSION_BEGIN(TimeNodeEnum.SESSION_BEGIN),
        SESSION_END(TimeNodeEnum.SESSION_END),

        /**
         * 有效期
         */
        VALIDITY_DATE_TIME(TimeNodeEnum.VALIDITY_DATE_TIME),

        /**
         * 邮寄
         */
        POSTAGE(TimeNodeEnum.POSTAGE),
        /**
         * 自取
         */
        SELF_PICKUP(TimeNodeEnum.SELF_PICKUP),

        /**
         * 自取激活
         */
        SELF_PICKUP_ACTIVE(TimeNodeEnum.SELF_PICKUP_ACTIVE),
        /**
         * 邮寄激活
         */
        POSTAGE_ACTIVE(TimeNodeEnum.POSTAGE_ACTIVE),
        /**
         * 订单确认
         */
        ORDER_CONFIRM(TimeNodeEnum.ORDER_CONFIRM),
        ;

        CompareTimeType(TimeNodeEnum usageDate) {
            this.value = usageDate.getValue();
            this.name = usageDate.getName();
        }

        private final String value;
        private final String name;
    }

    /**
     * 有损数值类型
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @AllArgsConstructor
    @Getter
    enum CostType {
        PERCENTAGE("PERCENTAGE", "百分比", CancelChangeRuleEnum.FeeType.PERCENTAGE.getValue()),
        PERCENTAGE_SALE("PERCENTAGE_SALE", "销售价百分比", CancelChangeRuleEnum.FeeType.PERCENTAGE.getValue()),
        PERCENTAGE_MARKET("PERCENTAGE_MARKET", "门市价百分比", CancelChangeRuleEnum.FeeType.PERCENTAGE.getValue()),
        CONSTANT("CONSTANT", "资源固定值", CancelChangeRuleEnum.FeeType.CONSTANT.getValue()),
        UNIT_CONSTANT("UNIT_CONSTANT", "每份（资源）固定值", CancelChangeRuleEnum.FeeType.UNIT_CONSTANT.getValue()),
        TEXT_DESCRIPTION("TEXT_DESCRIPTION", "文字描述", CancelChangeRuleEnum.FeeType.TEXT_DESCRIPTION.getValue()),
        BASE_SUPPLIER_RETURN("BASE_SUPPLIER_RETURN", "基于供应商返回", CancelChangeRuleEnum.FeeType.BASE_SUPPLIER_RETURN.getValue()),
        ;

        private final String value;
        private final String name;
        private final Integer funValue;

    }
}
