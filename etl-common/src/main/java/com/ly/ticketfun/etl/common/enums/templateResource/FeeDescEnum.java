package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FeeDescEnum {

    INCLUDE("INCLUDE", "包含项目", null, 0),
    NUM("NUM", "项目数量", null, 1),
    DESC("DESC", "包含说明", null, 2),
    EXCLUDE("EXCLUDE", "费用不含", null, 3),
    SCENERY_HOTEL_PIC("SCENERY_HOTEL_PIC", "景酒-主图", null, null),
    SCENERY_HOTEL_BOOKING("SCENERY_HOTEL_BOOKING", "景酒-预约", null, null),
    SCENERY_HOTEL_HOTEL("SCENERY_HOTEL_HOTEL", "景酒-酒店", null, null),
    SCENERY_HOTEL_HOTEL_NUM("SCENERY_HOTEL_HOTEL_NUM", "景酒-酒店数量", null, null),
    SCENERY_HOTEL_SCENERY("SCENERY_HOTEL_SCENERY", "景酒-景区", null, null),
    SCENERY_HOTEL_SCENERY_NUM("SCENERY_HOTEL_SCENERY_NUM", "景酒-景区数量", null, null),
    SCENERY_HOTEL_RIGHT("SCENERY_HOTEL_RIGHT", "景酒-权益", null, null),
    SCENERY_HOTEL_DESC("SCENERY_HOTEL_DESC", "景酒-补充说明", null, null),
    ;

    private final String code;
    private final String message;
    private final String funValue;
    private final Integer ticketValue;
}
