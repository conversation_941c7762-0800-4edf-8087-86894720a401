package com.ly.ticketfun.etl.common.enums.base;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 货币枚举
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@AllArgsConstructor
@Getter
public enum CurrencyEnum {
    CNY("CNY", "人民币"),
    USD("USD", "美元"),
    EUR("EUR", "欧元"),
    HKD("HKD", "港币"),
    JPY("JPY", "日元"),
    GBP("GBP", "英镑"),
    AUD("AUD", "澳元"),
    CAD("CAD", "加元"),
    IDR("IDR", "印尼币"),
    INR("INR", "印度币"),
    MYR("MYR", "马来西亚币"),
    NZD("NZD", "新西兰币"),
    PHP("PHP", "菲律宾币"),
    SGD("SGD", "新加坡币"),
    THB("THB", "泰铢"),
    TWD("TWD", "台币");
    private final String value;
    private final String name;
}
