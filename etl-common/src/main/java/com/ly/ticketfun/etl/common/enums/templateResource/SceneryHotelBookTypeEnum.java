package com.ly.ticketfun.etl.common.enums.templateResource;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SceneryHotelBookTypeEnum {
    //是否需要二次预约 0不需要 1在线预约 2电话预约 3短信链接预约
    UN("UN", "不需要", null, 0),
    ONLINE("ONLINE", "在线预约", null, 1),
    TEL("TEL", "电话预约", null, 2),
    MSG("MSG", "短信", null, 3),
    ;

    private final String code;
    private final String message;
    private final String funValue;
    private final Integer ticketValue;
}
