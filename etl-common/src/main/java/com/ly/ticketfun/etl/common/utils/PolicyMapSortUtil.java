package com.ly.ticketfun.etl.common.utils;

import java.util.*;

public class PolicyMapSortUtil<T> {
    public Map<Long, List<T>> sortMapLimit(Map<Long, List<T>> groupMap, Comparator comparator) {
        ArrayList<Map.Entry<Long, List<T>>> entries = new ArrayList<>(groupMap.entrySet());
        entries.sort(comparator);
        LinkedHashMap<Long, List<T>> result = new LinkedHashMap<>();
        for (Map.Entry<Long, List<T>> entry : entries) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }
}
