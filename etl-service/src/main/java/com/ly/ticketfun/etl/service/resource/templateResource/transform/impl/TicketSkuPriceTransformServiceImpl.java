package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SkuResourceEnum;
import com.ly.ticketfun.etl.common.enums.ticket.StockTypeEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyBaseInfoService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketPriceStockService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketResourceService;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyInventory;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketPriceStockAgg;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketResourceAgg;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.SkuResourcePriceDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.common.transform.TransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service("ticketSkuPriceTransformService")
public class TicketSkuPriceTransformServiceImpl extends TransformBaseServiceImpl
        implements ITRTransformService<TicketFunTemplateResourceSkuInfo> {

    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;

    @Resource
    private ITicketPriceStockService ticketPriceStockService;

    @Resource
    private ITicketResourceService ticketResourceService;

    @Override
    public TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformData(
            TicketFunResourceChangeRo changeRo,
            MQEnum.TicketFunResourceChangeCategory changeCategory
    ) {
        try {
            Long resourceId = changeRo.getPoiId();
            Long policyId = NumberUtils.toLong(changeRo.getPackageId(), 0);

            if (resourceId == null || resourceId <= 0) {
                throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "景区id为空");
            }
            if (policyId <= 0) {
                throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "政策id为空");
            }

            // 获取政策基础信息
            PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryByResourceIdAndPolicyId(resourceId, policyId);
            if (policyBaseInfo == null) {
                throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_NOT_EXISTS,
                        String.valueOf(resourceId), String.valueOf(policyId));
            }

            // 获取景区基础信息
            TicketResourceAgg ticketResourceAgg = ticketResourceService.fetchResource(resourceId);
            GlobalRegionDateDto globalRegionDateDto = ticketResourceAgg.getGlobalRegionDate();

            // 获取价格库存信息
            TicketPriceStockAgg ticketPriceStockAgg = ticketPriceStockService.fetchPriceStock(
                    resourceId,
                    policyId,
                    Integer.valueOf(globalRegionDateDto.getTimeZone())
            );

            return new TRTransformResultRo<>(
                    transformEntity(policyBaseInfo, ticketPriceStockAgg, globalRegionDateDto)
            );
        } catch (TRTransformException e) {
            return new TRTransformResultRo<>(e.getErrorInfo(), e.getExtendList());
        }
    }

    /**
     * 转换实体
     *
     * @param policyBaseInfo      政策基础信息
     * @param ticketPriceStockAgg 价格库存信息
     * @param globalRegionDateDto 时区信息
     * @return sku信息
     */
    private TicketFunTemplateResourceSkuInfo transformEntity(
            PolicyBaseInfo policyBaseInfo,
            TicketPriceStockAgg ticketPriceStockAgg,
            GlobalRegionDateDto globalRegionDateDto
    ) {
        String resourceId = String.valueOf(policyBaseInfo.getResourceId());
        String policyId = String.valueOf(policyBaseInfo.getPolicyId());

        TicketFunTemplateResourceSkuInfo skuInfo = new TicketFunTemplateResourceSkuInfo();
        skuInfo.setPoiId(resourceId);
        skuInfo.setPackageId(policyId);
        skuInfo.setSkuId(policyId);

        // 转换币种信息
        transformCurrency(policyBaseInfo, skuInfo);
        // 转换价格列表
        transformPriceList(policyBaseInfo, ticketPriceStockAgg, skuInfo, globalRegionDateDto);

        return skuInfo;
    }

    /**
     * 转换价格列表
     *
     * @param policyBaseInfo      政策基础信息
     * @param ticketPriceStockAgg 价格库存信息
     * @param skuInfo             sku信息
     */
    private void transformPriceList(PolicyBaseInfo policyBaseInfo,
                                    TicketPriceStockAgg ticketPriceStockAgg,
                                    TicketFunTemplateResourceSkuInfo skuInfo,
                                    GlobalRegionDateDto globalRegionDateDto
    ) {
        try {
            List<PolicyPriceCalendar> policyPriceList = ticketPriceStockAgg.getPriceList();
            List<PolicyInventory> policyInventoryList = ticketPriceStockAgg.getInventoryList();

            List<SkuResourcePriceDto.PriceCalendarDto> priceList = new ArrayList<>();

            for (PolicyPriceCalendar policyPriceCalendar : policyPriceList) {
                SkuResourcePriceDto.PriceCalendarDto priceCalendarDto = new SkuResourcePriceDto.PriceCalendarDto();

                priceCalendarDto.setPriceDate(
                        policyPriceCalendar.getTravelDate()
                                .toEpochSecond(ZoneOffset.ofHours(Integer.parseInt(globalRegionDateDto.getTimeZone())))
                );
                priceCalendarDto.setSalePrice(policyPriceCalendar.getSaleAmount());
                priceCalendarDto.setNetPrice(policyPriceCalendar.getContractAmount());
                priceCalendarDto.setMarketPrice(policyPriceCalendar.getAmount());

                policyInventoryList.stream()
                        .filter(i -> Objects.equals(i.getId(), policyPriceCalendar.getInventoryId()))
                        .findFirst()
                        .ifPresent(inventoryInfo -> {
                            if (Objects.equals(inventoryInfo.getType(), StockTypeEnum.NO_LIMIT_STOCK.getCode())) {
                                priceCalendarDto.setStockType(SkuResourceEnum.StockType.NO_LIMITED_STOCK.getValue());
                            } else if (Objects.equals(inventoryInfo.getType(), StockTypeEnum.TOTAL_STOCK.getCode())
                                    || Objects.equals(inventoryInfo.getType(), StockTypeEnum.DAY_STOCK.getCode())
                            ) {
                                priceCalendarDto.setStockType(SkuResourceEnum.StockType.LIMITED_STOCK.getValue());
                                priceCalendarDto.setStockQuantity(
                                        inventoryInfo.getCount() - inventoryInfo.getUsedCount()
                                );
                            }
                        });
            }

            skuInfo.setPriceList(priceList);
        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.POI_TRANSFORM_FAIL,
                    resourceId, policyId, "priceList", e.getMessage());
        }
    }

    /**
     * 转换币种信息
     *
     * @param policyBaseInfo 政策基础信息
     * @param skuInfo        sku信息
     */
    private void transformCurrency(PolicyBaseInfo policyBaseInfo, TicketFunTemplateResourceSkuInfo skuInfo) {
        try {
            skuInfo.setNetPriceCurrency(policyBaseInfo.getBalancePriceCurrencyCode());
            skuInfo.setSalePriceCurrency(policyBaseInfo.getSalePriceCurrencyCode());
            skuInfo.setMarketPriceCurrency(policyBaseInfo.getSalePriceCurrencyCode());

        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.POI_TRANSFORM_FAIL,
                    resourceId, policyId, "currency", e.getMessage());
        }
    }
}
