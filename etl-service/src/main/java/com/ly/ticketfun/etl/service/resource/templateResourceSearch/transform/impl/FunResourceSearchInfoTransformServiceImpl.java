package com.ly.ticketfun.etl.service.resource.templateResourceSearch.transform.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.ly.localactivity.model.domain.tczbactivityresource.*;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.CancelChangeRuleAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.ResourceImageAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.ResourceTagAgg;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.CancelChangeRuleEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceImageEnum;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import com.ly.ticketfun.etl.common.utils.BitmaskUtils;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResourceSearch.TicketFunTemplateResourceSearchInfo;
import com.ly.ticketfun.etl.service.resource.common.transform.FunBaseTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResourceSearch.transform.IFunResourceSearchInfoTransformService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *
 *
 * <AUTHOR>
 * @date 2025/9/15
 */
@Service
public class FunResourceSearchInfoTransformServiceImpl extends FunBaseTransformServiceImpl implements IFunResourceSearchInfoTransformService {


    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    public static final String PROD_TONG_CHENG_ZI_YING = "PROD_TongChengZiYing";
    public static final int DEFAULT_CANCEL_CHANGE_TIME = -20;

    /**
     * 转换数据
     *
     * @param resourceId 资源id
     * @return {@link TRTransformResultRo }<{@link TicketFunTemplateResourceSearchInfo }>
     */
    public TRTransformResultRo<TicketFunTemplateResourceSearchInfo> transformData(String resourceId) {
        //todo..查询数据
        MainResourceAgg mainResourceAgg = ticketFunInnerApiService.queryFunMainResourceAgg(resourceId, true, false);
        //todo..转换数据
        TicketFunTemplateResourceSearchInfo resourceSearchInfo = matchData(mainResourceAgg);
        return

    }

    private TicketFunTemplateResourceSearchInfo matchData(MainResourceAgg mainResourceAgg) {
        TicketFunTemplateResourceSearchInfo resourceSearchInfo = new TicketFunTemplateResourceSearchInfo();
        // -------------- 匹配基础数据--------------
        matchBaseInfo(mainResourceAgg.getMainResource(),
                mainResourceAgg.getDetail(),
                mainResourceAgg.getCategoryList(),
                mainResourceAgg.getRecommandReason(),
                mainResourceAgg.getImageList(),
                mainResourceAgg.getScore(),
                mainResourceAgg.getResourceTagList(),
                mainResourceAgg.getCancelChangeRuleAgg(),
                mainResourceAgg.getMainResourceGiftList(),
                resourceSearchInfo);
        // -------------- 匹配别名主题数据--------------
//        matchSubjectAliases(mainResourceAgg.getTravelJourneyAggList(), resourceSearchInfo);


        return null;

    }

    private void matchBaseInfo(MainResource mainResource,
                               MainResourceDetail detail,
                               List<ResourceCategory> categories,
                               MainResourceRecommandReason recommendReason,
                               List<ResourceImageAgg> imageList, MainResourceScore score,
                               List<ResourceTagAgg> resourceTagList,
                               CancelChangeRuleAgg cancelChangeRuleAgg,
                               List<MainResourceGift> mainResourceGiftList, TicketFunTemplateResourceSearchInfo resourceSearchInfo
    ) {

        resourceSearchInfo.setResourceId(mainResource.getSerialId());
        resourceSearchInfo.setResourceName(mainResource.getTitle());
        resourceSearchInfo.setResourceSubName(mainResource.getSubTitle());
        resourceSearchInfo.setResourceHeadImage(mainResource.getMainImage());
        resourceSearchInfo.setEffectiveTime(LocalDateTimeUtil.toEpochMilli(mainResource.getAuditTime()));
        resourceSearchInfo.setValidStatus(mainResource.getDataFlag());

        resourceSearchInfo.setUpdateTime(LocalDateTimeUtil.toEpochMilli(detail.getUpdateDate()));
        resourceSearchInfo.setTimeZone(detail.getTimeZone());
        resourceSearchInfo.setDatesCanSales(detail.getDateCanSales());
        resourceSearchInfo.setTestStatus(detail.getIsTest());
        resourceSearchInfo.setCanBookStatus(detail.getIsCanAdvance());

        //处理对客暂时
        List<Integer> showStatus = BitmaskUtils.decodeBitmaskToOptions(detail.getShowStatus());
        resourceSearchInfo.setShowStatus(showStatus.stream().anyMatch(status -> MainResourceEnum.ShowStatus.SHOW_TO_CUSTOMER.getValue().equals(status)) ? WhetherEnum.YES.getValue() : WhetherEnum.NO.getValue());


        //品类
        ResourceCategoryDto resourceCategoryDto = parseCategoryFun().apply(categories);
        resourceSearchInfo.setFirstCategoryId(String.valueOf(resourceCategoryDto.getCategoryId()));
        resourceSearchInfo.setFirstCategoryName(resourceCategoryDto.getCategoryName());
        Optional.ofNullable(resourceCategoryDto.getSubCategoryId()).map(String::valueOf).ifPresent(resourceSearchInfo::setSecondCategoryId);
        Optional.ofNullable(resourceCategoryDto.getSubCategoryName()).ifPresent(resourceSearchInfo::setSecondCategoryName);


        resourceSearchInfo.setTravelDays(detail.getJourneyDays());

        //资源一句话描述
        resourceSearchInfo.setResourceSummary(recommendReason.getReasonA());

        //匹配视频
        ResourceImage video = imageList.stream().map(ResourceImageAgg::getResourceImage)
                .filter(resourceImage -> ResourceImageEnum.FileType.VIDEO_INTRODUCE.equals(funEnumMapping(ResourceImageEnum.FileType.class, resourceImage.getType(), true)))
                .findFirst().orElse(new ResourceImage());
        resourceSearchInfo.setResourceVideo(video.getImageUrl());
        resourceSearchInfo.setResourceVideo360(video.getCompressUrl());
        resourceSearchInfo.setResourceVideoCover(video.getCoverUrl());

        //评分相关
//        resourceSearchInfo.setCommentScore(score.getCommentScore());
        resourceSearchInfo.setMaintenanceScore(score.getContentScore());

        //PROD_TongChengZiYing
        //解析是否自营
        resourceSearchInfo.setCooperationType(WhetherEnum.NO.getValue());
        Optional.ofNullable(resourceTagList).orElse(new ArrayList<>())
                .stream()
                .filter(resourceTagAgg -> PROD_TONG_CHENG_ZI_YING.equals(resourceTagAgg.getResourceTagConfig().getTagCode()))
                .findFirst()
                .ifPresent(resourceTagAgg -> resourceSearchInfo.setCooperationType(WhetherEnum.YES.getValue()));

        //默认1
        resourceSearchInfo.setPriceStatus(NumberUtils.INTEGER_ONE);
        resourceSearchInfo.setMblStatus(ResourceSearchEnum.MBLStatus.L.getValue());
        resourceSearchInfo.setUseStatus(WhetherEnum.NO.getValue());

        CancelChangeRule changeRule = cancelChangeRuleAgg.getRule();
        //解析退款规则
        CancelChangeRuleEnum.RuleType ruleType = enumMapping(CancelChangeRuleEnum.RuleType.class, changeRule.getRuleType());
        if (CancelChangeRuleEnum.RuleType.ANY_TIME_WHEN_UN_USE.equals(ruleType)) {
            resourceSearchInfo.setRefundType(ResourceSearchEnum.RefundTypeEnum.ANYTIME_REFUND.getValue());
        } else if (CancelChangeRuleEnum.RuleType.CAN_NOT.equals(ruleType)) {
            resourceSearchInfo.setRefundType(ResourceSearchEnum.RefundTypeEnum.NO_REFUND.getValue());
        } else if (CancelChangeRuleEnum.RuleType.BASE_RULE.equals(ruleType)) {
            if (detail.getCancelChangeTimes() != DEFAULT_CANCEL_CHANGE_TIME) {
                resourceSearchInfo.setRefundType(ResourceSearchEnum.RefundTypeEnum.NO_LOSS_CONDITION_REFUND.getValue());
            } else if (detail.getCancelChangeTimes() == DEFAULT_CANCEL_CHANGE_TIME) {
                resourceSearchInfo.setRefundType(ResourceSearchEnum.RefundTypeEnum.LOSS_CONDITION_REFUND.getValue());
            }
        } else {
            resourceSearchInfo.setRefundType(ResourceSearchEnum.RefundTypeEnum.NO_REFUND.getValue());
        }
        //默认0
        resourceSearchInfo.setRefundTime(WhetherEnum.NO.getValue());

        resourceSearchInfo.setHadGift(CollectionUtils.isNotEmpty(mainResourceGiftList) && mainResourceGiftList.stream().anyMatch(gift -> gift.getValidityEndTime().isAfter(LocalDateTime.now())) ? WhetherEnum.YES.getValue() : WhetherEnum.NO.getValue());

        //todo 产品地址
        //todo 是否需要预约


    }


}