package com.ly.ticketfun.etl.service.resource.common.transform;

import com.ly.localactivity.model.domain.tczbactivityresource.MainResourceCity;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResourceCrowd;
import com.ly.localactivity.model.domain.tczbactivityresource.ResourceCategory;
import com.ly.localactivity.model.enums.common.RegionEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.ResourceCategoryEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBandInfoEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBookPassengerInfoEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.domain.templateResource.dto.CountryAreaDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceBandInfoDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import org.apache.commons.lang.math.NumberUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * IFunCommonTransformService
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
public abstract class FunBaseTransformServiceImpl extends TransformBaseServiceImpl {

    /**
     * 主资源crowd2资源带信息dto
     *
     * @return {@link Function }<{@link MainResourceCrowd }, {@link ResourceBandInfoDto }>
     */
    protected Function<MainResourceCrowd, ResourceBandInfoDto> mainResourceCrowd2ResourceBandInfoDto() {
        return resourceCrowd -> {
            ResourceBandInfoDto resourceBandInfoDto = new ResourceBandInfoDto();

            ResourceBandInfoEnum.BandCode bandCode = funEnumMapping(ResourceBandInfoEnum.BandCode.class, resourceCrowd.getCode());
            resourceBandInfoDto.setBandCode(bandCode.getValue());
            resourceBandInfoDto.setBandTitle(bandCode.getName());
            resourceBandInfoDto.setPersonQuantity(NumberUtils.INTEGER_ONE);
            resourceBandInfoDto.setPassengerRequiredType(ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ALL.getValue());

            ResourceBandInfoEnum.RangeType rangeType = funEnumMapping(ResourceBandInfoEnum.RangeType.class, resourceCrowd.getRangeType());

            ResourceBandInfoDto.BandLimitRuleDto bandLimitRuleDto = new ResourceBandInfoDto.BandLimitRuleDto();

            List<ResourceBandInfoDto.BandLimitRuleDto> bandLimitRuleList = new ArrayList<>(1);

            if (ResourceBandInfoEnum.RangeType.AGE.equals(rangeType)) {
                ResourceBandInfoDto.AgeLimitRuleDto ageLimitRuleDto = new ResourceBandInfoDto.AgeLimitRuleDto();
                ageLimitRuleDto.setCanBook(WhetherEnum.YES.getValue());
                ResourceBandInfoEnum.BirthdayDimension birthdayDimension = funEnumMapping(ResourceBandInfoEnum.BirthdayDimension.class, resourceCrowd.getCalcCondition1());
                ageLimitRuleDto.setAgeCalcBirthdayDimension(birthdayDimension.getValue());
                ResourceBandInfoEnum.AgeCalcCompareDimension ageCalcCompareDimension = funEnumMapping(ResourceBandInfoEnum.AgeCalcCompareDimension.class, resourceCrowd.getCalcCondition2());
                ageLimitRuleDto.setAgeCalcCompareDimension(ageCalcCompareDimension.getValue());
                ResourceBandInfoDto.AgeRangeDto ageRangeDto = new ResourceBandInfoDto.AgeRangeDto();
                ageRangeDto.setBeginAge(resourceCrowd.getMin().intValue());
                ageRangeDto.setBeginInclude(resourceCrowd.getIsIncludeMin());
                ageRangeDto.setEndAge(resourceCrowd.getMax().intValue());
                ageRangeDto.setEndInclude(resourceCrowd.getIsIncludeMax());
                ageLimitRuleDto.setAgeRangeList(Collections.singletonList(ageRangeDto));
                bandLimitRuleDto.getAgeLimitRuleList().add(ageLimitRuleDto);
                bandLimitRuleList.add(bandLimitRuleDto);
            } else if (ResourceBandInfoEnum.RangeType.HEIGHT.equals(rangeType)) {
                ResourceBandInfoDto.HeightLimitRuleDto heightLimitRuleDto = new ResourceBandInfoDto.HeightLimitRuleDto();
                heightLimitRuleDto.setCanBook(WhetherEnum.YES.getValue());
                heightLimitRuleDto.setBeginHeight(resourceCrowd.getMin().movePointRight(2).intValue());
                heightLimitRuleDto.setEndInclude(resourceCrowd.getIsIncludeMin());
                heightLimitRuleDto.setEndHeight(resourceCrowd.getMax().movePointRight(2).intValue());
                heightLimitRuleDto.setEndInclude(resourceCrowd.getIsIncludeMax());
                bandLimitRuleDto.getHeightLimitRuleList().add(heightLimitRuleDto);
                bandLimitRuleList.add(bandLimitRuleDto);
            }
            resourceBandInfoDto.setBandLimitRuleList(bandLimitRuleList);
            return resourceBandInfoDto;
        };
    }


    /**
     * 解析品类
     *
     * @return {@link Function}<{@link List}<{@link ResourceCategory}>, {@link ResourceCategoryDto}>
     */
    protected Function<List<ResourceCategory>, ResourceCategoryDto> parseCategoryFun() {
        return categories -> {
            ResourceCategory firstCategory = categories.stream().filter(category -> ResourceCategoryEnum.CategoryType.FIRST_CATEGORY.getValue().equals(category.getCategoryType())).findFirst().orElseThrow(() -> new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, "玩乐一级品类", "null"));
            //处理品类
            ResourceCategoryDto resourceCategoryDto = new ResourceCategoryDto();
            resourceCategoryDto.setCategoryId(Long.valueOf(firstCategory.getCategoryId()));
            resourceCategoryDto.setCategoryName(firstCategory.getCategoryName());
            categories.stream().filter(category -> ResourceCategoryEnum.CategoryType.SECOND_CATEGORY.getValue().equals(category.getCategoryType()))
                    .findFirst()
                    .ifPresent(secondCategory -> {
                                resourceCategoryDto.setSubCategoryId(Long.valueOf(secondCategory.getCategoryId()));
                                resourceCategoryDto.setSubCategoryName(secondCategory.getCategoryName());
                            }
                    );
            return resourceCategoryDto;
        };
    }


    /**
     * 构建单条 MainResourceCity 的层级链，返回最底层有效节点（其 parentCountryArea 向上串联）。
     * 优先级：county > city > province > country > continent。
     */
    protected CountryAreaDto buildCountryAreaChain(MainResourceCity c) {
        if (c == null) {
            return null;
        }
        // 构建各层节点（允许中间层缺失）
        CountryAreaDto continent = createAreaNode(RegionEnum.Level.CONTINENT, c.getContinetId(), c.getContinetName());
        CountryAreaDto country = createAreaNode(RegionEnum.Level.COUNTRY, c.getCountryId(), c.getCountryName());
        CountryAreaDto province = createAreaNode(RegionEnum.Level.PROVINCE, c.getProvinceId(), c.getProvinceName());
        CountryAreaDto city = createAreaNode(RegionEnum.Level.CITY, c.getCityId(), c.getCityName());
        CountryAreaDto county = createAreaNode(RegionEnum.Level.COUNTY, c.getCountyId(), c.getCountyName());

        CountryAreaDto[] levels = {continent, country, province, city, county};
        // 上一层非空节点（更高层）
        CountryAreaDto lastNonNull = null;
        for (CountryAreaDto levelNode : levels) {
            if (levelNode == null) {
                continue;
            }
            if (lastNonNull != null) {
                levelNode.setParentCountryArea(lastNonNull);
            }
            lastNonNull = levelNode;
        }
        // 从后往前找到最深层非空节点返回
        for (int i = levels.length - 1; i >= 0; i--) {
            if (levels[i] != null) {
                return levels[i];
            }
        }
        return null;
    }

    /**
     * 创建单个层级节点；若 id 无效则返回 null。
     */
    protected CountryAreaDto createAreaNode(RegionEnum.Level levelEnum, Long id, String name) {
        if (id == null || id <= 0) {
            return null;
        }
        CountryAreaDto dto = new CountryAreaDto();
        dto.setType(levelEnum.getValue());
        dto.setCountryAreaId(id);
        dto.setName(name);
        return dto;
    }


}
