package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.domain.tczbactivityresource.*;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.*;
import com.ly.localactivity.model.enums.common.*;
import com.ly.localactivity.model.enums.resource.ClauseEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceEnum;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.*;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 玩乐资源转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class FunProductTransformServiceImpl extends FunBaseTransformFunctionServiceImpl implements ITRTransformService<TicketFunTemplateResourceProductInfo> {
    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 支持
     *
     * @param resourceType   资源类型
     * @param changeCategory 变更类别
     * @return {@link Boolean}
     */
    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PRODUCT_CHANGE);
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    @Override
    public TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformData(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
        if (StringUtils.isEmpty(changeRo.getProductId())) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id为空");
        }
        // -------------- 数据查询 --------------
        MainResourceAgg mainResourceAgg = ticketFunInnerApiService.queryFunMainResourceAgg(changeRo.getProductId(), false, false);
        // -------------- 数据校验 --------------
        if (mainResourceAgg == null) {
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS, changeRo.getProductId());
        }
        if (mainResourceAgg.getMainResource().getDataFlag().equals(DataFlagEnum.INVALID.getValue())
                || mainResourceAgg.getMainResource().getIsDelete().equals(DeleteFlagEnum.DELETED.getValue())
                || !mainResourceAgg.getMainResource().getAuditStatus().equals(MainResourceEnum.AuditStatus.AUDIT_PASS.getValue())
                || mainResourceAgg.getDetail().getDataFlag().equals(DataFlagEnum.INVALID.getValue())
                || mainResourceAgg.getDetail().getIsCanAdvance().equals(WhetherEnum.NO.getValue())) {
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_ON_SALE, changeRo.getProductId());
        }
        // -------------- 数据匹配 --------------
        return matchData(mainResourceAgg);
    }

    /**
     * 匹配数据
     *
     * @param mainResourceAgg 主资源汇总
     * @return {@link TRTransformResultRo}
     */
    private TRTransformResultRo<TicketFunTemplateResourceProductInfo> matchData(MainResourceAgg mainResourceAgg) {
        try {
            TicketFunTemplateResourceProductInfo productResourceDto = new TicketFunTemplateResourceProductInfo();
            // -------------- 数据匹配：MainResource --------------
            matchByMainResource(mainResourceAgg.getMainResource(), productResourceDto);
            // -------------- 数据匹配：MainResourceDetail --------------
            matchByMainResourceDetail(mainResourceAgg.getDetail(), productResourceDto);
            //匹配品类
            ResourceCategoryDto resourceCategoryDto = parseCategoryFun().apply(mainResourceAgg.getCategoryList());
            productResourceDto.setCategory(resourceCategoryDto);
            // -------------- 匹配产品出发点目的地 --------------
            matchProductDepAndDest(mainResourceAgg.getCityList(), productResourceDto);
            // -------------- 数据匹配：产品文件列表 --------------
            matchProductFileList(mainResourceAgg.getImageList(), productResourceDto);
            // -------------- 匹配产品标签 --------------
            matchProductLabels(mainResourceAgg.getResourceTagList(), productResourceDto);
            // -------------- 匹配产品亮点和图文详情--------------
            matchByMainResourceRecommendReason(mainResourceAgg.getRecommandReason(), productResourceDto);
            // -------------- 匹配产品销售属性列表--------------
            matchProductSalePropertyList(mainResourceAgg.getSetmealAttributeAggList(), productResourceDto);
            // -------------- 匹配产品人群信息--------------
            matchProductBandInfo(mainResourceAgg.getCrowdList(), productResourceDto);
            // -------------- 匹配产品条款列表--------------
            matchProductClauseList(mainResourceAgg.getResourceClauseAggList(), productResourceDto);
            // -------------- 匹配产品销售情况汇总列表--------------
            matchProductSalesSummaryList(mainResourceAgg.getScore(), productResourceDto);
            // -------------- 匹配产品逻辑扩展列表--------------
            matchProductLogicExtendList(mainResourceAgg.getLogicExtend(), productResourceDto);
            return new TRTransformResultRo<>(productResourceDto);
        } catch (TRTransformException ex) {
            return new TRTransformResultRo<>(ex.getErrorInfo(), ex.getExtendList());
        }
    }

    /**
     * 匹配产品逻辑扩展列表
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setLogicExtendList(List) logicExtendList}：逻辑扩展列表</li>
     * </ul>
     *
     * @param logicExtend        逻辑扩展
     * @param productResourceDto 产品资源dto
     */
    private void matchProductLogicExtendList(MainResourceLogicExtend logicExtend, TicketFunTemplateResourceProductInfo productResourceDto) {
        if (logicExtend == null) {
            LogUtils.info(LogOperateTypeEnum.OTHER, "逻辑扩展为空", 0, productResourceDto.getProductId(), "product");
            return;
        }
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品逻辑扩展列表", 0, productResourceDto.getProductId(), "product");
        List<LogicExtendDto> logicExtendList = new ArrayList<>();
        //取件方式
        Optional.ofNullable(logicExtend.getTakeTypes())
                .filter(StrUtil::isNotBlank)
                .map(taskTypes -> Arrays.stream(taskTypes.split(StrUtil.COMMA))
                        .distinct()
                        .filter(StrUtil::isNotBlank)
                        .map(taskType -> new LogicExtendDto(LogicExtendEnum.PackageKey.TAKE_TYPE.getValue(), taskType))
                        .collect(Collectors.toList())
                )
                .ifPresent(logicExtendList::addAll);
        // 自取城市
        Optional.ofNullable(logicExtend.getTakeCityIds())
                .filter(StrUtil::isNotBlank)
                .map(takeCityIds -> Arrays.stream(takeCityIds.split(StrUtil.COMMA))
                        .distinct()
                        .filter(StrUtil::isNotBlank)
                        .map(takeCityId -> new LogicExtendDto(LogicExtendEnum.PackageKey.SELF_PICKUP_CITY.getValue(), takeCityId))
                        .collect(Collectors.toList())
                )
                .ifPresent(logicExtendList::addAll);
        // 发货城市
        Optional.ofNullable(logicExtend.getExpressSendCityIds())
                .filter(StrUtil::isNotBlank)
                .map(dispatchCityIds -> Arrays.stream(dispatchCityIds.split(StrUtil.COMMA))
                        .distinct()
                        .filter(StrUtil::isNotBlank)
                        .map(dispatchCityId -> new LogicExtendDto(LogicExtendEnum.PackageKey.DELIVERY_CITY.getValue(), dispatchCityId))
                        .collect(Collectors.toList())
                )
                .ifPresent(logicExtendList::addAll);
        // 待机时长
        Optional.ofNullable(logicExtend.getStandyTime())
                .filter(standbyDuration -> standbyDuration > 0)
                .map(standbyDuration -> new LogicExtendDto(LogicExtendEnum.PackageKey.STANDBY_DURATION.getValue(), standbyDuration))
                .ifPresent(logicExtendList::add);
        if (CollectionUtils.isEmpty(productResourceDto.getLogicExtendList())) {
            productResourceDto.setLogicExtendList(new ArrayList<>());
        }
        productResourceDto.getLogicExtendList().addAll(logicExtendList);
        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品逻辑扩展列表结束,结果: {}", JSON.toJSONString(logicExtendList)), System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");

    }

    /**
     * 匹配产品销售汇总列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setLogicExtendList(List)}：包含评论分、评论数、好评/中评/差评数、订单创建/支付量等。</li>
     * </ul>
     *
     * @param score              主资源销售分数对象
     * @param productResourceDto 产品资源dto
     */
    private void matchProductSalesSummaryList(MainResourceScore score, ProductResourceDto productResourceDto) {
        if (score == null) {
            return;
        }

        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品销售情况汇总列表", 0, productResourceDto.getProductId(), "product");
        LogicExtendDto commentSource = new LogicExtendDto(LogicExtendEnum.ProductKey.COMMENT_SCORE.getValue(), score.getCommentsScore());
        LogicExtendDto commentCount = new LogicExtendDto(LogicExtendEnum.ProductKey.COMMENT_COUNT.getValue(), score.getCommentsCount());
        LogicExtendDto commentGoodCount = new LogicExtendDto(LogicExtendEnum.ProductKey.COMMENT_GOOD_COUNT.getValue(), score.getGoodCommentsCount());
        LogicExtendDto commentMiddleCount = new LogicExtendDto(LogicExtendEnum.ProductKey.COMMENT_MIDDLE_COUNT.getValue(), score.getMiddleCommentsCount());
        LogicExtendDto commentBadCount = new LogicExtendDto(LogicExtendEnum.ProductKey.COMMENT_BAD_COUNT.getValue(), score.getBadCommentsCount());
        LogicExtendDto orderCreateVolume15 = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_CREATE_VOLUME_15D.getValue(), score.getSaleCount1());
        LogicExtendDto orderCreateVolume30 = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_CREATE_VOLUME_30D.getValue(), score.getSaleCount2());
        LogicExtendDto orderPayVolume15 = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_PAY_VOLUME_15D.getValue(), score.getPayCount1());
        LogicExtendDto orderPayVolume30 = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_PAY_VOLUME_30D.getValue(), score.getPayCount2());
        LogicExtendDto orderPayVolume180 = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_PAY_VOLUME_180D.getValue(), score.getPayCount3());
        LogicExtendDto orderPayUnitVolume = new LogicExtendDto(LogicExtendEnum.ProductKey.ORDER_PAY_UNIT_VOLUME.getValue(), score.getPayUnitCount());
        List<LogicExtendDto> salesSummaryList = Arrays.asList(commentSource, commentCount, commentGoodCount, commentMiddleCount, commentBadCount, orderCreateVolume15, orderCreateVolume30, orderPayVolume15, orderPayVolume30, orderPayVolume180, orderPayUnitVolume);
        if (CollectionUtils.isEmpty(productResourceDto.getLogicExtendList())) {
            productResourceDto.setLogicExtendList(new ArrayList<>());
        }
        productResourceDto.getLogicExtendList().addAll(salesSummaryList);
        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品销售情况汇总列表结束,结果: {}", JSON.toJSONString(salesSummaryList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 匹配产品条款列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setClauseList(List) clauseList}：按条款类型分组，组装条款及条款项。</li>
     * </ul>
     *
     * @param resourceClauseAggList resource子句agg列表
     * @param productResourceDto    产品资源dto
     */
    private void matchProductClauseList(List<MainResourceClauseAgg> resourceClauseAggList, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品条款列表", 0, productResourceDto.getProductId(), "product");

        List<ClauseTypeDto> clauseTypeDtoList = resourceClauseAggList.stream().collect(Collectors.groupingBy(resourceClauseAgg -> resourceClauseAgg.getClause().getTypeCode()))
                .entrySet()
                .stream()
                .map(entry -> {
                    String clauseType = entry.getKey();
                    List<MainResourceClauseAgg> mainResourceClauses = entry.getValue();
                    ClauseTypeDto clauseTypeDto = new ClauseTypeDto();
                    clauseTypeDto.setClauseTypeCode(clauseType);
                    clauseTypeDto.setClauseAvailableCount(mainResourceClauses.size());
                    ClauseEnum.TypeCodeEnum typeCodeEnum = enumMapping(ClauseEnum.TypeCodeEnum.class, clauseType);
                    clauseTypeDto.setTitle(typeCodeEnum.getName());
                    List<ClauseTypeDto.ClauseDto> clauses = mainResourceClauses.stream().map(mainResourceClause -> {
                        ClauseTypeDto.ClauseDto clauseDto = new ClauseTypeDto.ClauseDto();
                        clauseDto.setClauseCode(mainResourceClause.getClause().getClauseCode());
                        clauseDto.setTitle(mainResourceClause.getClause().getClauseName());
                        List<ClauseTypeDto.ClauseItemDto> clauseItems = mainResourceClause.getClauseItemList().stream().map(mainResourceClauseItem -> {
                            ClauseTypeDto.ClauseItemDto clauseItemDto = new ClauseTypeDto.ClauseItemDto();
                            clauseItemDto.setClauseItemCode(mainResourceClauseItem.getCode());
                            clauseItemDto.setTitle(mainResourceClauseItem.getName());
                            clauseItemDto.setContent(mainResourceClauseItem.getDescription());
                            return clauseItemDto;
                        }).collect(Collectors.toList());
                        clauseDto.setClauseItemList(clauseItems);
                        return clauseDto;
                    }).collect(Collectors.toList());
                    clauseTypeDto.setClauseList(clauses);
                    return clauseTypeDto;
                }).collect(Collectors.toList());
        productResourceDto.setClauseList(clauseTypeDtoList);

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品条款列表结束,结果: {}", JSON.toJSONString(clauseTypeDtoList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 匹配产品人群信息。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setBandInfoList(List) bandInfoList}：人群信息及限制规则。</li>
     * </ul>
     *
     * @param crowdList          人群列表
     * @param productResourceDto 产品资源dto
     */
    private void matchProductBandInfo(List<MainResourceCrowd> crowdList, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品人群信息", 0, productResourceDto.getProductId(), "product");

        List<ResourceBandInfoDto> bandInfoList = crowdList.stream()
                .sorted(Comparator.comparing(MainResourceCrowd::getSort).reversed())
                .map(mainResourceCrowd2ResourceBandInfoDto()).collect(Collectors.toList());
        productResourceDto.setBandInfoList(bandInfoList);

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品人群信息结束,结果: {}", JSON.toJSONString(bandInfoList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }


    /**
     * 匹配产品销售属性列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setSalePropertyList(List) salePropertyList}：销售属性集合。</li>
     * </ul>
     *
     * @param setmealAttributeAggList 套餐属性聚合列表
     * @param productResourceDto      产品资源dto
     */
    private void matchProductSalePropertyList(List<SetmealAttributeAgg> setmealAttributeAggList, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品销售属性列表", 0, productResourceDto.getProductId(), "product");

        List<ProductSalePropertyDto> productSalePropertyList = setmealAttributeAggList.stream().map(setmealAttributeAgg -> {
            SetmealAttribute setmealAttribute = setmealAttributeAgg.getSetmealAttribute();
            ProductSalePropertyDto productSaleProperty = new ProductSalePropertyDto();
            productSaleProperty.setPropertyId(setmealAttribute.getId());
            productSaleProperty.setPropertyName(setmealAttribute.getAttributeName());
            productSaleProperty.setPropertyCode(setmealAttribute.getCode());
            return productSaleProperty;
        }).collect(Collectors.toList());

        productResourceDto.setSalePropertyList(productSalePropertyList);
        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品销售属性列表结束,结果: {}", JSON.toJSONString(productSalePropertyList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 匹配产品标签。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setProductLabels(List) productLabels}：标签树结构。</li>
     * </ul>
     *
     * @param resourceTagList    标签关系列表
     * @param productResourceDto 产品资源dto
     */
    private void matchProductLabels(List<ResourceTagAgg> resourceTagList, ProductResourceDto productResourceDto) {
        if (CollectionUtils.isEmpty(resourceTagList)) {
            LogUtils.info(LogOperateTypeEnum.OTHER, "标签关系列表为空", 0, productResourceDto.getProductId(), "product");
            return;
        }

        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品标签", 0, productResourceDto.getProductId(), "product");

        Map<Long, List<ResourceTagAgg>> resourceTagMapByParentId = resourceTagList.stream()
                .collect(Collectors.groupingBy(resourceTagAgg -> resourceTagAgg.getResourceTagConfig().getParentTagId()));

        List<ResourceLabelDto> resourceLabelList = resourceTagList.stream().map(ResourceTagAgg::getParentTagConfig).distinct().map(resourceTagConfig -> {
            ResourceLabelDto resourceLabelDto = new ResourceLabelDto();
            ResourceLabelEnum.Type typeEnum = funEnumMapping(ResourceLabelEnum.Type.class, resourceTagConfig.getType());
            resourceLabelDto.setType(typeEnum.getValue());
            resourceLabelDto.setCode(resourceTagConfig.getTagCode());
            resourceLabelDto.setName(resourceTagConfig.getTagName());
            List<ResourceTagAgg> tagAggList = resourceTagMapByParentId.getOrDefault(resourceTagConfig.getId(), new ArrayList<>());

            List<ResourceLabelDto> subResourceTagList = tagAggList.stream().map(tagAgg -> {
                ResourceLabelDto childLabelDto = new ResourceLabelDto();
                ResourceTagConfig subResourceTagConfig = tagAgg.getResourceTagConfig();
                ResourceTagRelation resourceTagRelation = tagAgg.getResourceTagRelation();
                childLabelDto.setCode(subResourceTagConfig.getTagCode());
                childLabelDto.setName(subResourceTagConfig.getTagName());
                childLabelDto.setContent(resourceTagRelation.getTagContent());
                childLabelDto.setSort(resourceTagRelation.getTagSort());
                return childLabelDto;
            }).collect(Collectors.toList());
            resourceLabelDto.setChildList(subResourceTagList);
            return resourceLabelDto;
        }).collect(Collectors.toList());
        productResourceDto.setProductLabels(resourceLabelList);

//        List<ResourceLabelDto> productLabelList = buildProductLabelTree(resourceTagMapByParentId, NumberUtils.LONG_ZERO);
//        productResourceDto.setProductLabels(productLabelList);

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品标签结束,结果: {}", JSON.toJSONString(resourceLabelList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 递归构建 FileLabelDto 树结构
     *
     * @param resourceTagMapByParentId 父ID分组的标签map
     * @param parentId                 当前父ID
     * @return List<FileLabelDto>
     */
    private List<ResourceLabelDto> buildProductLabelTree(Map<Long, List<ResourceTagAgg>> resourceTagMapByParentId, Long parentId) {
        List<ResourceTagAgg> resourceTagList = resourceTagMapByParentId.get(parentId);
        if (CollectionUtils.isEmpty(resourceTagList)) {
            return null;
        }
        return resourceTagList.stream().map(resourceTagAgg -> {
            ResourceLabelDto resourceLabelDto = new ResourceLabelDto();
            ResourceTagConfig resourceTagConfig = resourceTagAgg.getResourceTagConfig();
            ResourceTagRelation resourceTagRelation = resourceTagAgg.getResourceTagRelation();
            ResourceLabelEnum.Type typeEnum = funEnumMapping(ResourceLabelEnum.Type.class, resourceTagConfig.getType());
            resourceLabelDto.setType(typeEnum.getValue());
            resourceLabelDto.setCode(resourceTagConfig.getTagCode());
            resourceLabelDto.setName(resourceTagConfig.getTagName());
            resourceLabelDto.setContent(resourceTagRelation.getTagContent());
            resourceLabelDto.setSort(resourceTagRelation.getTagSort());
            //TODO:标签展示平台

            // 递归设置子标签
            List<ResourceLabelDto> childList = buildProductLabelTree(resourceTagMapByParentId, resourceTagConfig.getId());
            if (CollectionUtils.isNotEmpty(childList)) {
                resourceLabelDto.setChildList(childList);
            }
            return resourceLabelDto;
        }).collect(Collectors.toList());
    }


    /**
     * 匹配主资源推荐信息并填充到 ProductResourceDto。
     * <p>
     * 赋值字段:
     * <ul>
     *   <li>{@link ProductResourceDto#setDescription(String) description}: 取 {@link MainResourceRecommandReason#getGraphicDesc()} 作为图文详情。</li>
     *   <li>{@link ProductResourceDto#setSalePointList(List) salePointList}: 由 A/B/C 三条推荐理由生成的亮点列表。</li>
     * </ul>
     * 亮点生成规则:
     * <ol>
     *   <li>从 {@code reasonC -> reasonB -> reasonA} 顺序遍历。</li>
     *   <li>对每个非空理由生成一个 {@link ResourceSalePointDto}，其 {@code name} 与 {@code content} 均为该理由内容。</li>
     *   <li>按遍历顺序分配递增 {@code sort}（1 开始）。这样若下游以 sort 降序展示，可形成 A → B → C 的倒序效果。</li>
     *   <li>若全部理由均为空白，不设置 {@code salePointList}（保持 null）。</li>
     * </ol>
     * 前置条件: {@code recommendReason} 不应为 null；否则将触发 NPE。
     * 不会修改 {@link ProductResourceDto} 的其它字段。
     *
     * @param recommendReason    主资源推荐原因实体（包含 A/B/C 文案及图文详情）
     * @param productResourceDto 目标产品 DTO
     */
    private void matchByMainResourceRecommendReason(MainResourceRecommandReason recommendReason, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品亮点和图文详情", 0, productResourceDto.getProductId(), "product");
        //图文详情
        productResourceDto.setDescription(recommendReason.getGraphicDesc());
        //亮点
        AtomicReference<Integer> sort = new AtomicReference<>(1);
        //C-A 排序值 1->3  实现A-C倒叙
        List<ResourceSalePointDto> salePointList = Stream.of(recommendReason.getReasonC(), recommendReason.getReasonB(), recommendReason.getReasonA())
                .filter(StrUtil::isNotBlank)
                .map(reason -> {
                    ResourceSalePointDto salePointDto = new ResourceSalePointDto();
                    salePointDto.setName(reason);
                    salePointDto.setContent(reason);
                    salePointDto.setSort(sort.getAndSet(sort.get() + 1));
                    return salePointDto;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(salePointList)) {
            productResourceDto.setSalePointList(salePointList);
        }

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品亮点和图文详情结束,结果: salePointList={}, description={}",
                        JSON.toJSONString(salePointList), productResourceDto.getDescription()),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 将扁平的 MainResourceCity 列表转换为出发地(departureList) 与 目的地(destinationList) 的链式结构（通过 parentCountryArea 逐级向上追溯）。
     * <p>
     * 赋值字段:
     * <ul>
     *   <li>{@link ProductResourceDto#getDepartureList() departureList}</li>
     *   <li>{@link ProductResourceDto#getDestinationList() destinationList}</li>
     * </ul>
     * 每个元素为当前最底层（优先级: 区县 > 城市 > 省 > 国家 > 洲）的 {@link CountryAreaDto}，其父级通过 {@code parentCountryArea} 链接上一层，形成链式结构直至洲。
     * 忽略 id 为空或 <=0 的层级；跳层时自动衔接下一有效父级。
     * </p>
     *
     * @param cityList           资源地信息集合
     * @param productResourceDto 目标 DTO
     */
    private void matchProductDepAndDest(List<MainResourceCity> cityList, ProductResourceDto productResourceDto) {
        if (CollectionUtils.isEmpty(cityList)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_TRANSFORM_FAIL, productResourceDto.getProductId(), "cityList is empty");
        }
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品出发点目的地", 0, productResourceDto.getProductId(), "product");

        Map<Integer, List<MainResourceCity>> byType = cityList.stream().collect(Collectors.groupingBy(MainResourceCity::getType));
        // 出发地
        List<CountryAreaDto> departureList = Optional.ofNullable(byType.get(RegionEnum.LeaveDestinationType.DEPARTURE_CITY.getValue()))
                .map(list -> list.stream().map(this::buildCountryAreaChain).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(departureList)) {
            productResourceDto.setDepartureList(departureList);
        }
        // 目的地
        List<CountryAreaDto> destinationList = Optional.ofNullable(byType.get(RegionEnum.LeaveDestinationType.DESTINATION_CITY.getValue()))
                .map(list -> list.stream().map(this::buildCountryAreaChain).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(destinationList)) {
            productResourceDto.setDestinationList(destinationList);
        }

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品出发点目的地结束,结果: departureList={}, destinationList={}",
                        JSON.toJSONString(departureList), JSON.toJSONString(destinationList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 构建单条 MainResourceCity 的层级链，返回最底层有效节点（其 parentCountryArea 向上串联）。
     * 优先级：county > city > province > country > continent。
     */
    private CountryAreaDto buildCountryAreaChain(MainResourceCity c) {
        if (c == null) {
            return null;
        }
        // 构建各层节点（允许中间层缺失）
        CountryAreaDto continent = createAreaNode(RegionEnum.Level.CONTINENT, c.getContinetId(), c.getContinetName());
        CountryAreaDto country = createAreaNode(RegionEnum.Level.COUNTRY, c.getCountryId(), c.getCountryName());
        CountryAreaDto province = createAreaNode(RegionEnum.Level.PROVINCE, c.getProvinceId(), c.getProvinceName());
        CountryAreaDto city = createAreaNode(RegionEnum.Level.CITY, c.getCityId(), c.getCityName());
        CountryAreaDto county = createAreaNode(RegionEnum.Level.COUNTY, c.getCountyId(), c.getCountyName());

        CountryAreaDto[] levels = {continent, country, province, city, county};
        // 上一层非空节点（更高层）
        CountryAreaDto lastNonNull = null;
        for (CountryAreaDto levelNode : levels) {
            if (levelNode == null) {
                continue;
            }
            if (lastNonNull != null) {
                levelNode.setParentCountryArea(lastNonNull);
            }
            lastNonNull = levelNode;
        }
        // 从后往前找到最深层非空节点返回
        for (int i = levels.length - 1; i >= 0; i--) {
            if (levels[i] != null) {
                return levels[i];
            }
        }
        return null;
    }

    /**
     * 创建单个层级节点；若 id 无效则返回 null。
     */
    private CountryAreaDto createAreaNode(RegionEnum.Level levelEnum, Long id, String name) {
        if (id == null || id <= 0) {
            return null;
        }
        CountryAreaDto dto = new CountryAreaDto();
        dto.setType(levelEnum.getValue());
        dto.setCountryAreaId(id);
        dto.setName(name);
        return dto;
    }

    /**
     * 根据主资源对象填充 {@link ProductResourceDto} 的核心展示字段。
     * <p>
     * 已赋值字段:
     * <ul>
     *   <li>{@link ProductResourceDto#getProductId() productId}</li>
     *   <li>{@link ProductResourceDto#getName() name}</li>
     *   <li>{@link ProductResourceDto#getSubName() subName}</li>
     *   <li>{@link ProductResourceDto#getHeadImageUrl() headImageUrl}</li>
     * </ul>
     * <p>
     * 未对其它字段进行修改。
     *
     * @param mainResource       主要资源
     * @param productResourceDto 目标 DTO
     */
    private void matchByMainResource(MainResource mainResource, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配主资源信息", 0, productResourceDto.getProductId(), "product");

        productResourceDto.setProductId(mainResource.getSerialId());
        productResourceDto.setName(mainResource.getTitle());
        productResourceDto.setSubName(mainResource.getSubTitle());
        productResourceDto.setHeadImageUrl(mainResource.getMainImage());
        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配主资源信息结束,结果: productId={}, name={},",
                        productResourceDto.getProductId(), productResourceDto.getName()),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }


    /**
     * 根据主资源详细信息补充 {@link ProductResourceDto} 的扩展字段。
     * <p>
     * 已赋值字段:
     * <ul>
     *   <li>{@link ProductResourceDto#getJourneyDays() journeyDays}：来自 {@code MainResourceDetail.journeyDays}，按逗号切分，过滤空白</li>
     *   <li>{@link ProductResourceDto#getPoiIdList() poiIdList}：来自 {@code MainResourceDetail.tcPoiIds}，按逗号切分，过滤非数字并转为 Long</li>
     *   <li>{@link ProductResourceDto#getTimeZone() timeZone} 时区</li>
     *   <li>{@link ProductResourceDto#getSaleStatus() saleStatus}：对应是否可预售标识</li>
     *   <li>{@link ProductResourceDto#getProductSort() productSort}</li>
     *   <li>{@link ProductResourceDto#getSaleTimeLimit() saleTimeLimit}：固定默认 {@link WhetherEnum#NO}</li>
     * </ul>
     * <p>
     *
     * @param mainResourceDetail 主要资源详细信息
     * @param productResourceDto 目标 DTO
     */
    private void matchByMainResourceDetail(MainResourceDetail mainResourceDetail, ProductResourceDto productResourceDto) {
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配主资源详细信息", 0, productResourceDto.getProductId(), "product");

        //行程天数
        Optional.ofNullable(mainResourceDetail.getJourneyDays())
                .map(days -> StrUtil.split(mainResourceDetail.getJourneyDays(), StrUtil.COMMA)
                        .stream()
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList())
                )
                .ifPresent(productResourceDto::setJourneyDays);
        //poi
        Optional.ofNullable(mainResourceDetail.getTcPoiIds())
                .map(poiIds -> StrUtil.split(mainResourceDetail.getTcPoiIds(), StrUtil.COMMA)
                        .stream()
                        .filter(StrUtil::isNumeric)
                        .map(Long::valueOf)
                        .collect(Collectors.toList())
                )
                .ifPresent(productResourceDto::setPoiIdList);

        productResourceDto.setTimeZone(mainResourceDetail.getTimeZone());
        productResourceDto.setSaleStatus(WhetherEnum.YES.getValue().equals(mainResourceDetail.getIsCanAdvance()) ? ProductResourceEnum.SaleStatus.CAN_SALE.getValue() : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue());
        productResourceDto.setProductSort(mainResourceDetail.getSort());
        //默认否
        productResourceDto.setSaleTimeLimit(WhetherEnum.NO.getValue());

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配主资源详细信息结束,结果: journeyDays={}, poiIdList={}, timeZone={}, saleStatus={}",
                        JSON.toJSONString(productResourceDto.getJourneyDays()), JSON.toJSONString(productResourceDto.getPoiIdList()),
                        productResourceDto.getTimeZone(), productResourceDto.getSaleStatus()),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 匹配产品文件列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link ProductResourceDto#setFileList(List) fileList}：文件及其标签树。</li>
     * </ul>
     *
     * @param imageList          图像列表
     * @param productResourceDto 产品资源dto
     */
    private void matchProductFileList(List<ResourceImageAgg> imageList, ProductResourceDto productResourceDto) {
        if (CollectionUtils.isEmpty(imageList)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_TRANSFORM_FAIL, productResourceDto.getProductId(), "imageList is empty");
        }
        long startTime = System.currentTimeMillis();
        LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配产品文件列表", 0, productResourceDto.getProductId(), "product");

        List<ResourceFileDto> resourceFileList = imageList.stream().map(resourceImageAgg -> {
                            ResourceFileDto resourceFileDto = new ResourceFileDto();
                            ResourceImage resourceImage = resourceImageAgg.getResourceImage();

                            resourceFileDto.setFileId(resourceImage.getImageSerialid());
                            resourceFileDto.setFileName(resourceImage.getImageName());
                            resourceFileDto.setFileUrl(resourceImage.getImageUrl());
                            resourceFileDto.setSort(resourceImage.getSort());
                            ResourceImageEnum.FileType fileTypeEnum = funEnumMapping(ResourceImageEnum.FileType.class, resourceImage.getType(), true);
                            resourceFileDto.setFileType(fileTypeEnum.getValue());
                            if (ResourceImageEnum.FileType.IMAGE_INTRODUCE.equals(fileTypeEnum)) {
                                ResourceImageEnum.HeadImageType headImageType = funEnumMapping(ResourceImageEnum.HeadImageType.class, resourceImage.getIsMainImage(), true);
                                resourceFileDto.setHeadImageType(headImageType.getValue());
                            } else if (ResourceImageEnum.FileType.VIDEO_INTRODUCE.equals(fileTypeEnum)) {
                                resourceFileDto.setFileCoverUrl(resourceImage.getCoverUrl());
                                resourceFileDto.setFileCompressUrl(resourceImage.getCompressUrl());
                                resourceFileDto.setFileCoverName(resourceImage.getCoverName());
                            }

                            //补充图片标签
                            Optional.ofNullable(resourceImageAgg.getFileTagConfigs())
                                    .filter(CollectionUtils::isNotEmpty)
                                    .ifPresent(fileTagConfigs -> {
                                                Map<Long, List<FileTagConfig>> fileTagMapByParentId = fileTagConfigs.stream().collect(Collectors.groupingBy(FileTagConfig::getParentTagId));
                                                List<ResourceFileDto.FileLabelDto> fileLabelDtoList = buildFileLabelDtoTree(fileTagMapByParentId, NumberUtils.LONG_ZERO);
                                                resourceFileDto.setFileLabelList(fileLabelDtoList);
                                            }
                                    );
                            return resourceFileDto;
                        }
                ).sorted(Comparator.comparing(ResourceFileDto::getSort).reversed())
                .collect(Collectors.toList());

        productResourceDto.setFileList(resourceFileList);

        LogUtils.info(LogOperateTypeEnum.OTHER, StrUtil.format("匹配产品文件列表结束,结果: {}", JSON.toJSONString(resourceFileList)),
                System.currentTimeMillis() - startTime, productResourceDto.getProductId(), "product");
    }

    /**
     * 递归构建 FileLabelDto 树结构
     *
     * @param fileTagMapByParentId 父ID分组的标签map
     * @param parentId             当前父ID
     * @return List<FileLabelDto>
     */
    private List<ResourceFileDto.FileLabelDto> buildFileLabelDtoTree(Map<Long, List<FileTagConfig>> fileTagMapByParentId, Long parentId) {
        List<FileTagConfig> tagConfigs = fileTagMapByParentId.get(parentId);
        if (CollectionUtils.isEmpty(tagConfigs)) {
            return null;
        }
        return tagConfigs.stream().map(tagConfig -> {
            ResourceFileDto.FileLabelDto dto = new ResourceFileDto.FileLabelDto();
            dto.setCode(tagConfig.getTagCode());
            dto.setName(tagConfig.getTagName());
            dto.setRemark(tagConfig.getTagRemark());
            dto.setSort(tagConfig.getSort());
            // 递归设置子标签
            List<ResourceFileDto.FileLabelDto> childList = buildFileLabelDtoTree(fileTagMapByParentId, tagConfig.getId());
            if (CollectionUtils.isNotEmpty(childList)) {
                dto.setChildList(childList);
            }
            return dto;
        }).collect(Collectors.toList());
    }

}
