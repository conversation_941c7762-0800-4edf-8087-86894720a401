package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.localactivity.model.domain.tczbactivityresource.MainResourceCrowd;
import com.ly.localactivity.model.domain.tczbactivityresource.ResourceCategory;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.ResourceCategoryEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBandInfoEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBookPassengerInfoEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceBandInfoDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import org.apache.commons.lang.math.NumberUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * IFunCommonTransformService
 *
 * <AUTHOR>
 * @date 2025/9/10
 */
public abstract class FunBaseTransformFunctionServiceImpl extends TRTransformBaseServiceImpl {

    /**
     * 主资源crowd2资源带信息dto
     *
     * @return {@link Function }<{@link MainResourceCrowd }, {@link ResourceBandInfoDto }>
     */
    protected Function<MainResourceCrowd, ResourceBandInfoDto> mainResourceCrowd2ResourceBandInfoDto() {
        return resourceCrowd -> {
            ResourceBandInfoDto resourceBandInfoDto = new ResourceBandInfoDto();

            ResourceBandInfoEnum.BandCode bandCode = funEnumMapping(ResourceBandInfoEnum.BandCode.class, resourceCrowd.getCode());
            resourceBandInfoDto.setBandCode(bandCode.getValue());
            resourceBandInfoDto.setBandTitle(bandCode.getName());
            resourceBandInfoDto.setPersonQuantity(NumberUtils.INTEGER_ONE);

            if (ResourceBandInfoEnum.BandCode.STANDARD.equals(bandCode)) {
                return resourceBandInfoDto;
            }

            //todo.. PassengerRequiredType
            resourceBandInfoDto.setPassengerRequiredType(ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ONE.getValue());

            ResourceBandInfoEnum.RangeType rangeType = funEnumMapping(ResourceBandInfoEnum.RangeType.class, resourceCrowd.getRangeType());

            ResourceBandInfoDto.BandLimitRuleDto bandLimitRuleDto = new ResourceBandInfoDto.BandLimitRuleDto();

            if (ResourceBandInfoEnum.RangeType.AGE.equals(rangeType)) {
                ResourceBandInfoDto.AgeLimitRuleDto ageLimitRuleDto = new ResourceBandInfoDto.AgeLimitRuleDto();
                ageLimitRuleDto.setCanBook(WhetherEnum.YES.getValue());

                ResourceBandInfoEnum.BirthdayDimension birthdayDimension = funEnumMapping(ResourceBandInfoEnum.BirthdayDimension.class, resourceCrowd.getCalcCondition1());
                ageLimitRuleDto.setAgeCalcBirthdayDimension(birthdayDimension.getValue());
                ResourceBandInfoEnum.AgeCalcCompareDimension ageCalcCompareDimension = funEnumMapping(ResourceBandInfoEnum.AgeCalcCompareDimension.class, resourceCrowd.getCalcCondition2());
                ageLimitRuleDto.setAgeCalcCompareDimension(ageCalcCompareDimension.getValue());

                ResourceBandInfoDto.AgeRangeDto ageRangeDto = new ResourceBandInfoDto.AgeRangeDto();
                ageRangeDto.setBeginAge(resourceCrowd.getMin().intValue());
                ageRangeDto.setBeginInclude(resourceCrowd.getIsIncludeMin());
                ageRangeDto.setEndAge(resourceCrowd.getMax().intValue());
                ageRangeDto.setEndInclude(resourceCrowd.getIsIncludeMax());
                ageLimitRuleDto.setAgeRangeList(Collections.singletonList(ageRangeDto));
                bandLimitRuleDto.getAgeLimitRuleList().add(ageLimitRuleDto);
            } else if (ResourceBandInfoEnum.RangeType.HEIGHT.equals(rangeType)) {
                ResourceBandInfoDto.HeightLimitRuleDto heightLimitRuleDto = new ResourceBandInfoDto.HeightLimitRuleDto();
                heightLimitRuleDto.setCanBook(WhetherEnum.YES.getValue());
                heightLimitRuleDto.setBeginHeight(resourceCrowd.getMin().movePointRight(2).intValue());
                heightLimitRuleDto.setEndInclude(resourceCrowd.getIsIncludeMin());
                heightLimitRuleDto.setEndHeight(resourceCrowd.getMax().movePointRight(2).intValue());
                heightLimitRuleDto.setEndInclude(resourceCrowd.getIsIncludeMax());
                bandLimitRuleDto.getHeightLimitRuleList().add(heightLimitRuleDto);
            }
            return resourceBandInfoDto;
        };
    }


    protected Function<List<ResourceCategory>, ResourceCategoryDto> parseCategoryFun() {
        return categories -> {
            ResourceCategory firstCategory = categories.stream().filter(category -> ResourceCategoryEnum.CategoryType.FIRST_CATEGORY.getValue().equals(category.getCategoryType())).findFirst().orElseThrow(() -> new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, "玩乐一级品类", "null"));
            //处理品类
            ResourceCategoryDto resourceCategoryDto = new ResourceCategoryDto();
            resourceCategoryDto.setCategoryId(Long.valueOf(firstCategory.getCategoryId()));
            resourceCategoryDto.setCategoryName(firstCategory.getCategoryName());

            categories.stream().filter(category -> ResourceCategoryEnum.CategoryType.SECOND_CATEGORY.getValue().equals(category.getCategoryType()))
                    .findFirst()
                    .ifPresent(secondCategory -> {
                                resourceCategoryDto.setSubCategoryId(Long.valueOf(secondCategory.getCategoryId()));
                                resourceCategoryDto.setSubCategoryName(secondCategory.getCategoryName());
                            }
                    );
            return resourceCategoryDto;
        };
    }


}
