package com.ly.ticketfun.etl.service.resource.ticket.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceBaseInfoService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceProductService;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.ITagResourceWashDataService;
import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceProduct;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketProductAgg;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.ticketfun.etl.service.resource.ticket.ITicketProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TicketProductServiceImpl implements ITicketProductService {

    @Resource
    private IResourceProductService resourceProductService;

    @Resource
    private IResourceBaseInfoService resourceBaseInfoService;

    @Resource
    private ITagResourceWashDataService tagResourceWashDataService;


    @Override
    public TicketProductAgg fetchProduct(Long poiId, Long productId) {
        String traceId = productId.toString();
        try {
            ResourceProduct resourceProduct = getResourceProduct(poiId, productId);
            ResourceBaseInfo resourceBaseInfo = getResourceBaseInfo(poiId);
            List<TagResourceWashData> tagResourceWashDataList = getTagResourceWashDataList(poiId, productId);
            List<TagDetailBo> tagDetailList = getTagDetailList(tagResourceWashDataList, poiId);
            return TicketProductAgg.builder()
                    .resourceProduct(resourceProduct)
                    .resourceBaseInfo(resourceBaseInfo)
                    .tagResourceWashDataList(tagResourceWashDataList)
                    .tagDetailList(tagDetailList)
                    .build();
        } catch (TRTransformException e) {
            LogUtils.info(LogOperateTypeEnum.QUERY, MessageFormat.format(e.getErrorInfo().getMsg(),e.getExtendList()));
        }
        return null;
    }

    private List<TagDetailBo> getTagDetailList(
            List<TagResourceWashData> tagResourceWashDataList,
            Long poiId) {
        if (CollectionUtils.isEmpty(tagResourceWashDataList)){
            return Collections.emptyList();
        }
        List<Long> tagIdList = tagResourceWashDataList
                .stream()
                .map(TagResourceWashData::getTagId)
                .distinct()
                .collect(Collectors.toList());
        return tagResourceWashDataService.selectTagDetailListByTagIdListAndResourceId(tagIdList, poiId);
    }

    private List<TagResourceWashData> getTagResourceWashDataList(Long poiId, Long productId) {
        return tagResourceWashDataService.queryList(
                new QueryWrapper<TagResourceWashData>()
                        .eq("resource_id", poiId)
                        .eq("product_id", productId)
                        .eq("row_status", 1)
                        .eq("bind_status", 1)
        );
    }

    private ResourceBaseInfo getResourceBaseInfo(Long poiId) {
        ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryOne(
                new QueryWrapper<ResourceBaseInfo>()
                        .eq("RBIId", poiId)
                        .eq("RBIRowStatus", 1)
        );
        if (resourceBaseInfo == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.TICKET_RESOURCE_NOT_EXISTS, "景区基础不存在");
        }
        return resourceBaseInfo;
    }

    private ResourceProduct getResourceProduct(Long poiId, Long productId) {
        ResourceProduct resourceProduct = resourceProductService.queryOne(
                new QueryWrapper<ResourceProduct>()
                        .eq("RPResourceId", poiId)
                        .eq("RPNId", productId)
                        .eq("RPRowStatus", 1)
        );
        if (resourceProduct == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS, "产品信息不存在");
        }
        return resourceProduct;
    }


}
