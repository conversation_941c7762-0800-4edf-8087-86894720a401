package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.ly.dal.util.DateUtil;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.utils.ReplaceUtils;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.LogicExtendEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ProductResourceEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceImageEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceLabelEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketProductAgg;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.ShelfRecommendWash;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.common.transform.TransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.ticket.ITicketProductService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TicketProductTransformServiceImpl extends TransformBaseServiceImpl implements ITRTransformService<TicketFunTemplateResourceProductInfo> {

    @Resource
    private ITicketProductService ticketProductService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformData(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        long productId = NumberUtils.toLong(changeRo.getProductId());
        if (productId <= 0 || changeRo.getPoiId() == null || changeRo.getPoiId() <= 0) {
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR);
        }
        // fetch product agg info
        TicketProductAgg productAgg = fetchProductInfo(changeRo.getPoiId(), productId);
        if (productAgg == null) {
            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
        }
        // transform
        TicketFunTemplateResourceProductInfo templateResourceProductInfo = transformCore(productAgg);
        return new TRTransformResultRo<>(templateResourceProductInfo);
    }

    private TicketFunTemplateResourceProductInfo transformCore(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo productTemplateInfo = getProductSimpleInfo(productAgg);
        // 门票品类
        productTemplateInfo.setCategory(getTicketDefaultCategory(productAgg.getResourceProduct()));
        // 行程天数
        productTemplateInfo.setJourneyDays(getTicketDefaultJourneyDays());
        // 出发地目的地信息
        productTemplateInfo.setDepartureList(Collections.emptyList());
        productTemplateInfo.setDestinationList(Collections.emptyList());
        productTemplateInfo.setPoiIdList(Lists.newArrayList(productAgg.getResourceProduct().getResourceId()));
        productTemplateInfo.setTimeZone(fetchTimeZoneByTcCityId(productAgg.getResourceBaseInfo().getCountryId(), productAgg.getResourceBaseInfo().getCityId()));
        // todo 服务语言
        productTemplateInfo.setServiceLanguageList(Collections.emptyList());
        productTemplateInfo.setHeadImageUrl(productAgg.getResourceProduct().getProductMainImg());
        productTemplateInfo.setVideoUrl("");
        productTemplateInfo.setVideoCompressUrl("");
        productTemplateInfo.setVideoCoverUrl("");
        productTemplateInfo.setDescription("");
        productTemplateInfo.setFileList(getFileList(productAgg.getResourceProduct().getProductMainImg()));
        productTemplateInfo.setSalePointList(getSalePointList(productAgg.getResourceProduct().getHighlights()));
        productTemplateInfo.setProductLabels(getProductLabels(productAgg.getTagResourceWashDataList(), productAgg.getTagDetailList()));
        productTemplateInfo.setSalePropertyList(getSalePropertyList(productAgg.getSalePropertyList()));
        productTemplateInfo.setBandInfoList(getBandInfoList(productAgg.getCrowdList()));
        productTemplateInfo.setClauseList(Collections.emptyList());
        // todo 逻辑字段
        productTemplateInfo.setLogicExtendList(getLogicExtendList(productAgg.getShelfRecommendWashList()));
        productTemplateInfo.setPartakeMinPrice(
                Objects.equals(productAgg.getResourceProduct().getSupplyStatus(), 1/*供给品类*/)
                        ? WhetherEnum.YES.getValue() : WhetherEnum.NO.getValue()
        );
        productTemplateInfo.setSaleStatus(getProductSaleStatus(productAgg.getNewSyncResource(), productAgg.getPolicyBaseInfoList(), productAgg.getResourcePolicyList()));
        // 默认否
        productTemplateInfo.setSaleTimeLimit(WhetherEnum.NO.getValue());
        // 销售时间
        setSaleTime(productTemplateInfo,productAgg.getNewSyncResource(),productAgg.getPolicyBaseInfoList(),productAgg.getResourcePolicyList());
        productTemplateInfo.setProductSort(productAgg.getResourceProduct().getSort());
        productTemplateInfo.setShowLimitList(getShowLimitList(productAgg.getCrowdList(),productAgg.getCrowdConfigList()));
        return productTemplateInfo;
    }

    private List<LogicExtendDto> getLogicExtendList(List<ShelfRecommendWash> shelfRecommendWashList) {
        if (CollectionUtils.isEmpty(shelfRecommendWashList)){
            return Collections.emptyList();
        }
        List<String> crowdRecommendCountList = new ArrayList<>(8);
        for (Map.Entry<Integer, List<ShelfRecommendWash>> entry : shelfRecommendWashList.stream().collect(Collectors.groupingBy(t -> t.getCrowdCategory())).entrySet()) {
            crowdRecommendCountList.add(entry.getKey()+":"+(Math.min(entry.getValue().size(), 4)));
        }
        LogicExtendDto logicExtendDto = new LogicExtendDto();
        // fixme enum不适配
        logicExtendDto.setKey(LogicExtendEnum.ProductKey.BOOK4_RECOMMEND_COUNT.getValue());
        logicExtendDto.setValue1(String.join(";", crowdRecommendCountList));
        return Lists.newArrayList(logicExtendDto);
    }

    private List<ProductResourceDto.ProductShowLimitVo> getShowLimitList(
            List<MarketProductCrowdRelation> crowdList,
            List<MarketProductConditionConfig> crowdConfigList) {
        if (CollectionUtils.isEmpty(crowdList) || CollectionUtils.isEmpty(crowdConfigList)){
            return Collections.emptyList();
        }
        Multimap<Integer/*crowdCategory*/, Long/*crowdUniqueId*/> crowdMultiMap = ArrayListMultimap.create();
        for (MarketProductCrowdRelation marketProductCrowdRelation : crowdList) {
            crowdMultiMap.put(marketProductCrowdRelation.getCrowdCategory().intValue(), marketProductCrowdRelation.getId());
        }
        List<ProductResourceDto.ProductShowLimitVo> showLimitList = Lists.newArrayListWithExpectedSize(crowdConfigList.size());
        for (Map.Entry<String, List<MarketProductConditionConfig>> entry : crowdConfigList
                .stream()
                .collect(Collectors.groupingBy(t -> t.getProductCrowdRelationId() + "|" + t.getApplyCrowd()))
                .entrySet()) {
            List<MarketProductConditionConfig> applyCrowdList = entry.getValue();
            ProductResourceDto.ProductShowLimitVo productShowLimitVo = new ProductResourceDto.ProductShowLimitVo();
            int index = -1;
            int i = 0;
            for (Long crowdRelationId : crowdMultiMap.get(applyCrowdList.get(0).getCrowdCategory())) {
               i++;
               if (crowdRelationId.equals(applyCrowdList.get(0).getProductCrowdRelationId())) {
                   index = i;
                   break;
               }
            }
            if (index == -1){
                continue;
            }
            productShowLimitVo.setClassifyId(applyCrowdList.get(0).getCrowdCategory()*10+index);
            productShowLimitVo.setClassifyCode(applyCrowdList.get(0).getCrowdCategory().toString());
            productShowLimitVo.setSuitCrowd(applyCrowdList.get(0).getApplyCrowd().toString());
            String crowdOuterName = crowdList
                    .stream()
                    .filter(t -> t.getId().equals(applyCrowdList.get(0).getProductCrowdRelationId()))
                    .map(MarketProductCrowdRelation::getCrowdOuterName)
                    .findFirst()
                    .orElse("");
            productShowLimitVo.setTitle(crowdOuterName);
            StringBuilder str = new StringBuilder();
            String applyCrowdName = applyCrowdList.get(0).getApplyCrowdName();

            str.append(String.format("适用条件:%s,", applyCrowdName));
            List<String> shortDesList = new ArrayList<>();
            //获取年龄得限制消息
            List<MarketProductConditionConfig> ageInfoList = applyCrowdList
                    .stream()
                    .filter(s -> s.getTypeId().compareTo(0) == 0)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(ageInfoList)) {
                String desDetailAge = getLimitDetailInfo(ageInfoList, shortDesList);
                str.append(desDetailAge);
            }
            //获取身高得限制消息
            List<MarketProductConditionConfig> tallInfoList = applyCrowdList
                    .stream()
                    .filter(s -> s.getTypeId().compareTo(1) == 0)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tallInfoList)) {
                String desDetailTall = getLimitDetailInfo(tallInfoList, shortDesList);
                str.append(desDetailTall);
            }
            //获取补充信息：
            List<MarketProductConditionConfig> otherInfoList = applyCrowdList
                    .stream()
                    .filter(s -> s.getTypeId().compareTo(-1) == 0)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherInfoList)) {
                String desDetailOther = getLimitDetailInfo(otherInfoList, shortDesList);
                str.append(desDetailOther);
            }
            if (str.length() > 0) {
                String des = str.toString();
                if (des.charAt(des.length() - 1) == ',') {
                    des = des.substring(0, str.length() - 1);
                }
                productShowLimitVo.setDescription(des);
            }
            if (!CollectionUtils.isEmpty(shortDesList)) {
                productShowLimitVo.setShortDescription(shortDesList.get(0));
            }
            showLimitList.add(productShowLimitVo);
        }
        return showLimitList;
    }

    private String getLimitDetailInfo(List<MarketProductConditionConfig> ageInfoList, List<String> shortStr) {
        List<String> desStr = new ArrayList<>();
        List<String> deffrientShortStr = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("0.##");
        for (MarketProductConditionConfig configModel : ageInfoList) {
            configModel.setApplyCrowdName(ReplaceUtils.replaceBracket(configModel.getApplyCrowdName()));
            configModel.setLimitCondition(ReplaceUtils.replaceBracket(configModel.getLimitCondition()));
            configModel.setLimitDesc(ReplaceUtils.replaceBracket(configModel.getLimitDesc()));
            String genDerStr = "";
            if (configModel.getTypeId().compareTo(0) >= 0) {
                //有年龄或身高限制
                if (configModel.getGenderType() != null && configModel.getGenderType().compareTo(0) > 0) {
                    genDerStr = configModel.getGenderType().compareTo(1) == 0 ? "限男性" : "限女性";
                }
                String typeName = configModel.getTypeId().compareTo(0) == 0 ? "年龄" : "身高";
                String unitName = configModel.getTypeId().compareTo(0) == 0 ? "岁" : "米";
                String beginIncludeName = configModel.getBeginInclude().compareTo(0) == 0 ? "(不含)" : "(含)";
                String endIncludeName = configModel.getEndInclude().compareTo(0) == 0 ? "(不含)" : "(含)";
                String beginEndStr = "";
                if (configModel.getBeginValue().compareTo(BigDecimal.ZERO) > 0 && configModel.getEndValue().compareTo(BigDecimal.ZERO) > 0) {
                    beginEndStr = String.format("%s%s-%s%s", df.format(configModel.getBeginValue()) + unitName, beginIncludeName, df.format(configModel.getEndValue()) + unitName, endIncludeName);
                } else if (configModel.getBeginValue().compareTo(BigDecimal.ZERO) <= 0 && configModel.getEndValue().compareTo(BigDecimal.ZERO) > 0) {
                    beginEndStr = String.format("%s%s以下", df.format(configModel.getEndValue()) + unitName, endIncludeName);
                } else if (configModel.getBeginValue().compareTo(BigDecimal.ZERO) > 0 && configModel.getEndValue().compareTo(BigDecimal.ZERO) <= 0) {
                    beginEndStr = String.format("%s%s以上", df.format(configModel.getBeginValue()) + unitName, beginIncludeName);
                }
                String showDes = StringUtils.isNotEmpty(beginEndStr) ? String.format("%s%s", configModel.getApplyCrowdName(), beginEndStr) : configModel.getLimitCondition();
                if (StringUtils.isNotEmpty(showDes)) {
                    if (showDes.charAt(0) == ';') {
                        showDes = showDes.substring(1);
                    }
                    shortStr.add(showDes);
                }
                String detailStr = String.format("%s%s%s%s%s", genDerStr, typeName, beginEndStr, StringUtils.isEmpty(configModel.getLimitCondition()) ? "" : ";" + configModel.getLimitCondition(), StringUtils.isEmpty(configModel.getLimitDesc()) ? "" : ";" + configModel.getLimitDesc());
                if (StringUtils.isNotEmpty(detailStr)) {
                    if (detailStr.charAt(0) == ';') {
                        detailStr = detailStr.substring(1);
                    }
                    desStr.add(detailStr);
                }
            } else {
                if (configModel.getGenderType() != null && configModel.getGenderType().compareTo(0) > 0) {
                    genDerStr = configModel.getGenderType().compareTo(1) == 0 ? "仅限男性" : "仅限女性";
                }
                if (StringUtils.isNotEmpty(genDerStr)) {
                    shortStr.add(genDerStr);
                }
                //只有限制条件等描述
                String detailStr = String.format("%s%s%s%s", genDerStr, ";", StringUtils.isEmpty(configModel.getLimitCondition()) ? "" : configModel.getLimitCondition(), StringUtils.isEmpty(configModel.getLimitDesc()) ? "" : ";" + configModel.getLimitDesc());
                if (StringUtils.isNotEmpty(detailStr)) {
                    if (detailStr.charAt(0) == ';') {
                        detailStr = detailStr.substring(1);
                    }
                    desStr.add(detailStr);
                }
                //23323
                boolean noShowName = true;
                if (configModel.getApplyCrowd() != null && configModel.getApplyCrowd().compareTo(23323) != 0 && configModel.getApplyCrowd().compareTo(23329) != 0) {
                    if (StringUtils.isNotEmpty(configModel.getApplyCrowdName())) {
                        noShowName = false;
                    }
                }
                if (StringUtils.isNotEmpty(configModel.getLimitCondition())) {
                    deffrientShortStr.add(String.format("%s%s", noShowName ? "" : configModel.getApplyCrowdName() + " ", configModel.getLimitCondition()));
                } else {
                    if (StringUtils.isNotEmpty(configModel.getLimitDesc())) {
                        deffrientShortStr.add(String.format("%s%s", noShowName ? "" : configModel.getApplyCrowdName() + " ", configModel.getLimitDesc()));
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(shortStr) && !CollectionUtils.isEmpty(deffrientShortStr)) {
            shortStr.addAll(deffrientShortStr);
        }
        if (!CollectionUtils.isEmpty(desStr)) {
            return String.join(";", desStr);
        }
        return "";
    }

    private void setSaleTime(TicketFunTemplateResourceProductInfo productTemplateInfo,
                             boolean newSyncResource,
                             List<PolicyBaseInfo> policyBaseInfoList,
                             List<ResourcePolicy> resourcePolicyList) {
        // set default sale time
        long defaultTime = DateUtil.string2Date("2000-01-01", DateUtil.DATE_PATTERN_YYYY_MM_DD).getTime();
        productTemplateInfo.setSaleBeginTime(defaultTime);
        productTemplateInfo.setSaleEndTime(defaultTime);
       if (newSyncResource){
            if (CollectionUtils.isEmpty(policyBaseInfoList)){
                return;
            }
            LocalDateTime minSaleBeginTime = policyBaseInfoList.get(0).getSaleBeginTime();
            LocalDateTime maxSaleEndTime = policyBaseInfoList.get(0).getSaleEndTime();
           for (PolicyBaseInfo policyBaseInfo : policyBaseInfoList) {
               if (policyBaseInfo.getSaleBeginTime().isBefore(minSaleBeginTime)){
                   minSaleBeginTime = policyBaseInfo.getSaleBeginTime();
               }
               if (policyBaseInfo.getSaleEndTime().isAfter(maxSaleEndTime)){
                   maxSaleEndTime = policyBaseInfo.getSaleEndTime();
               }
           }
           productTemplateInfo.setSaleBeginTime(LocalDateTimeUtil.toEpochMilli(minSaleBeginTime));
           productTemplateInfo.setSaleEndTime(LocalDateTimeUtil.toEpochMilli(maxSaleEndTime));
       }else{
           if (CollectionUtils.isEmpty(resourcePolicyList)){
               return;
           }
           LocalDateTime minSaleBeginTime = resourcePolicyList.get(0).getSaleBeginTime();
           LocalDateTime maxSaleEndTime = resourcePolicyList.get(0).getSaleEndTime();
           for (ResourcePolicy resourcePolicy : resourcePolicyList) {
               if (resourcePolicy.getSaleBeginTime().isBefore(minSaleBeginTime)){
                   minSaleBeginTime = resourcePolicy.getSaleBeginTime();
               }
               if (resourcePolicy.getSaleEndTime().isAfter(maxSaleEndTime)){
                   maxSaleEndTime = resourcePolicy.getSaleEndTime();
               }
           }
           productTemplateInfo.setSaleBeginTime(LocalDateTimeUtil.toEpochMilli(minSaleBeginTime));
           productTemplateInfo.setSaleEndTime(LocalDateTimeUtil.toEpochMilli(maxSaleEndTime));
       }
    }

    private String getProductSaleStatus(boolean newSyncResource, List<PolicyBaseInfo> policyBaseInfoList, List<ResourcePolicy> resourcePolicyList) {
        boolean containOnline = false;
        if (newSyncResource) {
            containOnline = !policyBaseInfoList.isEmpty();
        } else {
            containOnline = !resourcePolicyList.isEmpty();
        }
        return containOnline ?
                ProductResourceEnum.SaleStatus.CAN_SALE.getValue()
                : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue();
    }

    private List<ResourceBandInfoDto> getBandInfoList(List<MarketProductCrowdRelation> crowdList) {
        if (CollectionUtils.isEmpty(crowdList)) {
            return Collections.emptyList();
        }
        List<ResourceBandInfoDto> bandInfoList = new ArrayList<>();
        for (MarketProductCrowdRelation marketProductCrowdRelation : crowdList) {
            ResourceBandInfoDto bandInfo = new ResourceBandInfoDto();
            bandInfo.setBandId(marketProductCrowdRelation.getId());
            bandInfo.setBandCode(marketProductCrowdRelation.getCrowdCategory().toString());
            bandInfo.setBandTitle(marketProductCrowdRelation.getCrowdOuterName());
            bandInfo.setPersonQuantity(1);
            bandInfo.setPassengerRequiredType(null);
            bandInfo.setBandLimitRuleList(Collections.emptyList());
            bandInfoList.add(bandInfo);
        }
        return bandInfoList;

    }

    private List<ProductSalePropertyDto> getSalePropertyList(List<MarketProductTag> salePropertyList) {
        if (CollectionUtils.isEmpty(salePropertyList)) {
            return Collections.emptyList();
        }
        List<ProductSalePropertyDto> productSalePropertyDtoList = new ArrayList<>(salePropertyList.size());
        for (Map.Entry<Integer, List<MarketProductTag>> entry : salePropertyList
                .stream()
                .collect(Collectors.groupingBy(MarketProductTag::getPropId))
                .entrySet()) {
            MarketProductTag marketProductTag = entry.getValue().get(0);
            ProductSalePropertyDto productSalePropertyDto = new ProductSalePropertyDto();
            productSalePropertyDto.setPropertyId(Long.valueOf(marketProductTag.getPropId()));
            productSalePropertyDto.setPropertyCode(marketProductTag.getPropName());
            productSalePropertyDto.setPropertyName(marketProductTag.getPropName());

            List<ProductSalePropertyDto.ProductSalePropertyValueDto> propertyValueList = new ArrayList<>();
            for (MarketProductTag productTag : entry.getValue()) {
                ProductSalePropertyDto.ProductSalePropertyValueDto productSalePropertyValueDto = new ProductSalePropertyDto.ProductSalePropertyValueDto();
                productSalePropertyValueDto.setPropertyValueId(productTag.getId().toString());
                productSalePropertyValueDto.setPropertyValueName(productTag.getTagContent());
                productSalePropertyValueDto.setSubProperties(Collections.emptyList());
                propertyValueList.add(productSalePropertyValueDto);
            }

            productSalePropertyDto.setPropertyValueList(propertyValueList);
            productSalePropertyDtoList.add(productSalePropertyDto);
        }
        return productSalePropertyDtoList;
    }

    private List<ResourceLabelDto> getProductLabels(
            List<TagResourceWashData> tagResourceWashDataList,
            List<TagDetailBo> tagDetailList) {
        if (CollectionUtils.isEmpty(tagResourceWashDataList) || CollectionUtils.isEmpty(tagDetailList)) {
            return Collections.emptyList();
        }
        List<ResourceLabelDto> resourceLabelDtoList = new ArrayList<>(tagDetailList.size());
        for (Map.Entry<Long, List<TagDetailBo>> entry : tagDetailList.stream().collect(Collectors.groupingBy(TagDetailBo::getTagId)).entrySet()) {
            List<TagDetailBo> value = entry.getValue();
            TagDetailBo tagDetailBo = value.get(0);
            String tagName = value
                    .stream()
                    .filter(t -> Objects.equals(t.getContentCode(), 2))
                    .map(TagDetailBo::getContentValue)
                    .findFirst()
                    .orElse(null);
            if (StringUtils.isEmpty(tagName)) {
                continue;
            }

            List<TagResourceWashData> tagWashDataList = tagResourceWashDataList
                    .stream()
                    .filter(t -> t.getTagId().equals(entry.getKey()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tagWashDataList)) {
                continue;
            }
            ResourceLabelDto resourceLabelDto = getResourceLabelDto(tagDetailBo, tagName, tagWashDataList);
            resourceLabelDtoList.add(resourceLabelDto);
        }
        return resourceLabelDtoList;
    }

    private static ResourceLabelDto getResourceLabelDto(TagDetailBo tagDetailBo, String tagName, List<TagResourceWashData> tagWashDataList) {
        ResourceLabelDto resourceLabelDto = new ResourceLabelDto();
        resourceLabelDto.setType(ResourceLabelEnum.Type.OPERATION_EXE.getValue());
        resourceLabelDto.setCode(tagDetailBo.getTagCode());
        resourceLabelDto.setName(tagDetailBo.getTagName());
        resourceLabelDto.setContent(tagName);
        resourceLabelDto.setSort(tagWashDataList.get(0).getSort());
        resourceLabelDto.setShowPlatformList(Collections.emptyList());
        resourceLabelDto.setChildList(Collections.emptyList());
        return resourceLabelDto;
    }

    private List<ResourceSalePointDto> getSalePointList(String highlights) {
        if (StringUtils.isEmpty(highlights)) {
            return Collections.emptyList();
        }
        ResourceSalePointDto resourceSalePointDto = new ResourceSalePointDto();
        resourceSalePointDto.setSort(1);
        resourceSalePointDto.setName(highlights);
        resourceSalePointDto.setContent(highlights);
        resourceSalePointDto.setImageUrlList(Collections.emptyList());
        return Lists.newArrayList(resourceSalePointDto);

    }

    private List<ResourceFileDto> getFileList(String productMainImg) {
        if (StringUtils.isEmpty(productMainImg)) {
            return Collections.emptyList();
        }
        ResourceFileDto resourceFileDto = new ResourceFileDto();
        resourceFileDto.setFileType(ResourceImageEnum.FileType.IMAGE_INTRODUCE.getValue());
        resourceFileDto.setFileUrl(productMainImg);
        resourceFileDto.setSort(0);
        return Lists.newArrayList(resourceFileDto);
    }

    private Integer fetchTimeZoneByTcCityId(Integer countryId, Integer cityId) {
        GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(countryId, cityId);
        return Optional
                .ofNullable(globalRegionDateDto)
                .map(GlobalRegionDateDto::getTimeZone)
                .map(t -> NumberUtils.toInt(t, 8))
                .orElse(8);
    }

    private List<String> getTicketDefaultJourneyDays() {
        return Lists.newArrayList("1");
    }

    private ResourceCategoryDto getTicketDefaultCategory(ResourceProduct resourceProduct) {
        ResourceCategoryDto resourceCategoryDto = new ResourceCategoryDto();
        resourceCategoryDto.setCategoryId(10L);
        resourceCategoryDto.setCategoryName("门票");
        resourceCategoryDto.setSubCategoryId(Long.valueOf(resourceProduct.getProductClass()));
        resourceCategoryDto.setSubCategoryName(resourceProduct.getProductClassName());
        return resourceCategoryDto;
    }

    private TicketFunTemplateResourceProductInfo getProductSimpleInfo(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo templateResourceProductInfo = new TicketFunTemplateResourceProductInfo();
        templateResourceProductInfo.setProductId(productAgg.getResourceProduct().getId().toString());
        templateResourceProductInfo.setName(productAgg.getResourceProduct().getProductName());
        templateResourceProductInfo.setSubName("");
        return templateResourceProductInfo;
    }

    private TicketProductAgg fetchProductInfo(Long poiId, Long productId) {
        return ticketProductService.fetchProduct(poiId, productId);

    }
}
