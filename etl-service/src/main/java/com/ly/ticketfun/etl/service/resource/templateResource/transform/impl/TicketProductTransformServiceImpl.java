package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.google.common.collect.Lists;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceImageEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceLabelEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketProductAgg;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceFileDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceLabelDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceSalePointDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.ticket.ITicketProductService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TicketProductTransformServiceImpl extends TRTransformBaseServiceImpl implements ITRTransformService<TicketFunTemplateResourceProductInfo> {

    @Resource
    private ITicketProductService ticketProductService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return TemplateResourceConstant.ResourceType.TICKET.equals(resourceType)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PRODUCT_CHANGE);
    }

    @Override
    public TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformData(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // todo wangpeng
//        if (changeRo == null || changeRo.getProductId() == null || changeRo.getProductId() <= 0
//        || changeRo.getPoiId() == null || changeRo.getPoiId() <= 0){
//            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR);
//        }
//        // fetch product agg info
//        TicketProductAgg productAgg = fetchProductInfo(changeRo.getPoiId(),changeRo.getProductId());
//        if (productAgg == null){
//            return new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
//        }
//        // transform
//        TicketFunTemplateResourceProductInfo templateResourceProductInfo = transformCore(productAgg);


        return null;
    }

    private TicketFunTemplateResourceProductInfo transformCore(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo productTemplateInfo = getProductSimpleInfo(productAgg);
        // 门票品类
        productTemplateInfo.setCategory(getTicketDefaultCategory());
        // 行程天数
        productTemplateInfo.setJourneyDays(getTicketDefaultJourneyDays());
        // 出发地目的地信息
        productTemplateInfo.setDepartureList(Collections.emptyList());
        productTemplateInfo.setDestinationList(Collections.emptyList());
        productTemplateInfo.setPoiIdList(Lists.newArrayList(productAgg.getResourceProduct().getResourceId()));
        productTemplateInfo.setTimeZone(fetchTimeZoneByTcCityId(productAgg.getResourceBaseInfo().getCountryId(),productAgg.getResourceBaseInfo().getCityId()));
        // todo 服务语言
        productTemplateInfo.setServiceLanguageList(Collections.emptyList());

        productTemplateInfo.setHeadImageUrl(productAgg.getResourceProduct().getProductMainImg());
        productTemplateInfo.setVideoUrl("");
        productTemplateInfo.setVideoCompressUrl("");
        productTemplateInfo.setVideoCoverUrl("");
        productTemplateInfo.setDescription("");
        productTemplateInfo.setFileList(getFileList(productAgg.getResourceProduct().getProductMainImg()));
        productTemplateInfo.setSalePointList(getSalePointList(productAgg.getResourceProduct().getHighlights()));
        productTemplateInfo.setProductLabels(getProductLabels(productAgg.getTagResourceWashDataList(),productAgg.getTagDetailList()));



        return productTemplateInfo;
    }

    private List<ResourceLabelDto> getProductLabels(
            List<TagResourceWashData> tagResourceWashDataList,
            List<TagDetailBo> tagDetailList) {
        if (CollectionUtils.isEmpty(tagResourceWashDataList) || CollectionUtils.isEmpty(tagDetailList)){
            return Collections.emptyList();
        }
        List<ResourceLabelDto> resourceLabelDtoList = new ArrayList<>(tagDetailList.size());
        for (Map.Entry<Long, List<TagDetailBo>> entry : tagDetailList.stream().collect(Collectors.groupingBy(TagDetailBo::getTagId)).entrySet()) {
            List<TagDetailBo> value = entry.getValue();
            TagDetailBo tagDetailBo = value.get(0);
            String tagName = value
                    .stream()
                    .filter(t -> Objects.equals(t.getContentCode(), 2))
                    .map(t->t.getContentValue())
                    .findFirst()
                    .orElse(null);
            if (StringUtils.isEmpty(tagName)){
                continue;
            }

            List<TagResourceWashData> tagWashDataList = tagResourceWashDataList
                    .stream()
                    .filter(t -> t.getTagId().equals(entry.getKey()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tagWashDataList)){
                continue;
            }
            ResourceLabelDto resourceLabelDto = new ResourceLabelDto();
            resourceLabelDto.setType(ResourceLabelEnum.Type.OPERATION_EXE.getValue());
            resourceLabelDto.setCode(tagDetailBo.getTagCode());
            resourceLabelDto.setName(tagDetailBo.getTagName());
            resourceLabelDto.setContent(tagName);
            resourceLabelDto.setSort(tagWashDataList.get(0).getSort());
            resourceLabelDto.setShowPlatformList(Collections.emptyList());
            resourceLabelDto.setChildList(Collections.emptyList());
            resourceLabelDtoList.add(resourceLabelDto);
        }
        return resourceLabelDtoList;
    }

    private List<ResourceSalePointDto> getSalePointList(String highlights) {
        if (StringUtils.isEmpty(highlights)){
            return Collections.emptyList();
        }
        ResourceSalePointDto resourceSalePointDto = new ResourceSalePointDto();
        resourceSalePointDto.setSort(1);
        resourceSalePointDto.setName(highlights);
        resourceSalePointDto.setContent(highlights);
        resourceSalePointDto.setImageUrlList(Collections.emptyList());
        return Lists.newArrayList(resourceSalePointDto);

    }

    private List<ResourceFileDto> getFileList(String productMainImg) {
        if (StringUtils.isEmpty(productMainImg)){
            return Collections.emptyList();
        }
        ResourceFileDto resourceFileDto = new ResourceFileDto();
        resourceFileDto.setFileType(ResourceImageEnum.FileType.IMAGE_INTRODUCE.getValue());
        resourceFileDto.setFileUrl(productMainImg);
        resourceFileDto.setSort(0);
        return Lists.newArrayList(resourceFileDto);
    }

    private Integer fetchTimeZoneByTcCityId(Integer countryId,Integer cityId) {
        GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(countryId,cityId);
        return Optional
                .ofNullable(globalRegionDateDto)
                .map(GlobalRegionDateDto::getTimeZone)
                .map(t -> NumberUtils.toInt(t, 8))
                .orElse(8);
    }

    private List<String> getTicketDefaultJourneyDays() {
        return Lists.newArrayList("1");
    }

    private ResourceCategoryDto getTicketDefaultCategory() {
        ResourceCategoryDto resourceCategoryDto = new ResourceCategoryDto();
        resourceCategoryDto.setCategoryId(10L);
        resourceCategoryDto.setCategoryName("门票");
        return resourceCategoryDto;
    }

    private TicketFunTemplateResourceProductInfo getProductSimpleInfo(TicketProductAgg productAgg) {
        TicketFunTemplateResourceProductInfo templateResourceProductInfo = new TicketFunTemplateResourceProductInfo();
        templateResourceProductInfo.setProductId(productAgg.getResourceProduct().getId().toString());
        templateResourceProductInfo.setName(productAgg.getResourceProduct().getProductName());
        templateResourceProductInfo.setSubName("");
        return templateResourceProductInfo;
    }

    private TicketProductAgg fetchProductInfo(Long poiId,Long productId) {
        return ticketProductService.fetchProduct(poiId, productId);

    }
}
