package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.domain.tczbactivityresource.*;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.*;
import com.ly.localactivity.model.enums.common.TimeCompareEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.resource.ClauseEnum;
import com.ly.localactivity.model.enums.resource.TimeCompareRuleEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceBookFillInfoEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.SubResourceTakeReturnEnum;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.base.TimeNodeEnum;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.*;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 玩乐套餐转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Slf4j
@Service
public class FunPackageTransformServiceImpl extends FunBaseTransformFunctionServiceImpl implements ITRTransformService<TicketFunTemplateResourcePackageInfo> {

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 判断当前服务是否支持指定的资源类型和变更类别。
     * <p>
     * 该方法用于确定当前套餐转换服务是否能够处理特定的资源变更请求。
     * 支持玩乐类型资源的套餐变更和旅程变更两种场景。
     *
     * @param resourceType   资源类型，必须为 {@link TemplateResourceConstant.ResourceType#FUN}
     * @param changeCategory 变更类别，支持 {@link MQEnum.TicketFunResourceChangeCategory#PACKAGE_CHANGE} 和 {@link MQEnum.TicketFunResourceChangeCategory#TRAVEL_JOURNEY_CHANGE}
     * @return {@link Boolean} 如果支持则返回true，否则返回false
     */
    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
                || changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE));
    }

    /**
     * 转换数据列表，将资源变更信息转换为套餐模板资源信息列表。
     * <p>
     * 该方法是套餐转换服务的核心入口，负责处理套餐变更和旅程变更两种类型的资源变更。
     * 当前实现为待完善状态，需要根据变更类别进行相应的参数校验、数据查询、数据校验和数据匹配处理。
     * <p>
     * 支持的变更类别：
     * <ul>
     *   <li>{@link MQEnum.TicketFunResourceChangeCategory#PACKAGE_CHANGE}：套餐变更</li>
     *   <li>{@link MQEnum.TicketFunResourceChangeCategory#TRAVEL_JOURNEY_CHANGE}：旅程变更</li>
     * </ul>
     *
     * @param changeRo       资源变更请求对象，包含产品ID、套餐ID或旅程ID等信息
     * @param changeCategory 变更类别，决定具体的处理逻辑
     * @return {@link List}<{@link TRTransformResultRo}<{@link TicketFunTemplateResourcePackageInfo}>> 转换结果列表，包含成功或失败的转换信息
     */
    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getPackageId() == null || changeRo.getPackageId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
//        if (changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.TRAVEL_JOURNEY_CHANGE)
//                && ((changeRo.getProductId() == null || changeRo.getProductId() < 1) || (changeRo.getJourneyId() == null || changeRo.getJourneyId() < 1))) {
//            return new ArrayList<>(Arrays.asList(new TRTransformResultRo(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id或套餐Id为空")));
//        }
        // -------------- 数据查询 --------------

        // -------------- 数据校验 --------------

        // -------------- 数据匹配 --------------
        return matchData(new MainResourceAgg());
    }

    /**
     * 匹配数据，将主资源聚合对象转换为套餐模板资源信息列表。
     * <p>
     * 该方法负责将玩乐主资源的各种信息转换为套餐模板资源格式，包括：
     * <ul>
     *   <li>品类信息转换</li>
     *   <li>赠品清单解析</li>
     *   <li>退改规则处理</li>
     *   <li>服务提供方信息</li>
     *   <li>预订确认和凭证配置</li>
     *   <li>联系人信息和问卷配置</li>
     * </ul>
     * 对每个子资源进行详细的数据匹配，包括基础信息、销售属性、配送信息、附加费用、条款、预订限制等。
     *
     * @param mainResourceAgg 主资源聚合对象，包含完整的资源信息
     * @return {@link List}<{@link TRTransformResultRo}<{@link TicketFunTemplateResourcePackageInfo}>> 转换结果列表，每个子资源对应一个套餐信息
     */
    public List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> matchData(MainResourceAgg mainResourceAgg) {
        // -------------- 匹配共用数据 --------------
        // 1.品类
        ResourceCategoryDto resourceCategoryDto = parseCategoryFun().apply(mainResourceAgg.getCategoryList());
        // 2.赠品清单
        List<ResourceGiftDto> giftList = parseGiftList(mainResourceAgg.getMainResourceGiftList());
        //3.退改规则
        RefundPolicyDto refundPolicyDto = parseRefundPolicy(mainResourceAgg.getCancelChangeRuleAgg());
        //4.服务提供方
        ResourceServiceProviderDto serviceProviderDto = parseServiceProvider(mainResourceAgg.getServiceProvider());
        //5.预定确定和凭证
        Tuple2<BookConfirmDto, BookVoucherDto> bookConfirmVoucher = parseBookConfirmVoucher(mainResourceAgg.getBookConfigAgg());
        //6.预订联系人信息
        BookContactInfoDto bookContactInfoDto = parseBookContactInfo(mainResourceAgg.getBookFillInfoAgg());
        //7.游客填写信息
        List<BookPassengerQuestionDto> bookPassengerQuestionList = parseCommonBookPassengerQuestion(mainResourceAgg.getBookFillInfoAgg());
        //8.发票提供方
        PackageResourceEnum.InvoiceProvider invoiceProvider = funEnumMapping(PackageResourceEnum.InvoiceProvider.class, mainResourceAgg.getDetail().getInvoiceMode());
        //9.来源
        SourceAndCooperationEnum.SourceTypeEnum sourceFrom = funEnumMapping(SourceAndCooperationEnum.SourceTypeEnum.class, mainResourceAgg.getMainResource().getSource());
        //TODO挂牌语言、币种

        return mainResourceAgg.getSubResourceAggList()
                .stream()
                .map(subResourceAgg -> {
                            TicketFunTemplateResourcePackageInfo packageResource = new TicketFunTemplateResourcePackageInfo();
                            try {
                                //共用数据
                                packageResource.setCategory(resourceCategoryDto);
                                packageResource.setGiftList(giftList);
                                packageResource.setRefundPolicy(refundPolicyDto);
                                packageResource.setServiceProvider(serviceProviderDto);
                                packageResource.setBookConfirm(bookConfirmVoucher.getT1());
                                packageResource.setBookVoucher(bookConfirmVoucher.getT2());
                                packageResource.setBookContactInfo(bookContactInfoDto);
                                packageResource.setBookPassengerQuestionList(new ArrayList<>(bookPassengerQuestionList));
                                packageResource.setCooperationType(SourceAndCooperationEnum.CooperationTypeEnum.DAILY_ENTERTAINMENT.getValue());
                                packageResource.setInvoiceProvider(invoiceProvider.getValue());
                                packageResource.setSourceFrom(sourceFrom.getValue());

                                // -------------- 匹配基础信息 --------------
                                matchBaseInfo(subResourceAgg.getSubResource(), subResourceAgg.getDetail(), packageResource);
                                // -------------- 匹配销售属性 --------------
                                matchSaleProperty(subResourceAgg.getSetmealAttributeAggList(), packageResource);
                                // -------------- 配送信息--------------
                                matchDeliveryInfo(subResourceAgg.getDeliveryInfo(), packageResource);
                                // -------------- 附加费用--------------
                                matchSurchargeList(subResourceAgg.getSurchargeList(), packageResource);
                                // -------------- 解析人群-------------
                                List<ResourceBandInfoEnum.BandCode> crowdList = parseCrowdList(subResourceAgg.getSkuResourceAggList());
                                // -------------- 条款--------------
                                matchClauseList(subResourceAgg.getTravelJourneyAgg().getClauseAggList(), crowdList, packageResource);
                                // -------------- 预定限制--------------
                                matchBookLimit(subResourceAgg.getBookLimit(), mainResourceAgg.getBookConfigAgg().getBookConfig(), packageResource);
                                // -------------- 预定确认--------------
                                matchBookConfirm(subResourceAgg.getBookConfigAgg(), packageResource);
                                // -------------- 问卷--------------
                                matchBookPassengerQuestion(subResourceAgg.getPassengerQuestionList(), mainResourceAgg.getBookFillInfoAgg().getBookFillInfo().getTravellerInfoNeedType(), packageResource);
                                // -------------- 销售数据--------------
                                matchProductSalesSummaryList(subResourceAgg.getDetail(), packageResource);
                                // -------------- 逻辑拓展--------------
                                matchLogicExtendList(subResourceAgg.getLogicExtend(), packageResource);
                                // -------------- sku--------------
                                matchSkuList(subResourceAgg.getSkuResourceAggList(), packageResource);
                                // -------------- 线路--------------
                                matchTravelJourneyList(subResourceAgg.getTravelJourneyAgg(), packageResource);

                                return new TRTransformResultRo<>(packageResource);
                            } catch (TRTransformException e) {
                                return new TRTransformResultRo<TicketFunTemplateResourcePackageInfo>(e.getErrorInfo(), e.getExtendList());
                            } catch (Exception e) {
                                LogUtils.error(LogOperateTypeEnum.OTHER, e, subResourceAgg.getSubResource().getSerialId(), "package");
                                return new TRTransformResultRo<TicketFunTemplateResourcePackageInfo>(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "");
                            }
                        }
                )
                .collect(Collectors.toList());
    }


    /**
     * 匹配旅行旅程列表配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setTravelJourney(TravelJourneyDto)} 行程信息列表</li>
     * </ul>
     * 根据旅程聚合对象，构建包含旅程ID、名称、天数以及出发城市信息的旅程配置。
     * 当前实现为单个旅程的列表形式。
     *
     * @param travelJourneyAgg 旅程聚合对象
     * @param packageResource  套餐资源对象
     */
    private void matchTravelJourneyList(TravelJourneyAgg travelJourneyAgg, TicketFunTemplateResourcePackageInfo packageResource) {
        TravelJourney journey = travelJourneyAgg.getJourney();
        TravelJourneyDto travelJourneyDto = new TravelJourneyDto();
        travelJourneyDto.setJourneyId(journey.getId());
        travelJourneyDto.setJourneyName(journey.getJourneyName());
        travelJourneyDto.setJourneyDays(journey.getDayNum());
        travelJourneyDto.setDepartureCountryAreaId(journey.getLeavePortCityId());
        travelJourneyDto.setDepartureCountryAreaName(journey.getLeavePortCity());
        packageResource.setTravelJourney(travelJourneyDto);

        //行程详情解析
        travelJourneyAgg.getDetailAggList()
                .stream()
                .map(detailAgg -> {
                            TravelJourneyDto.TravelJourneyDetailDto detailDto = new TravelJourneyDto.TravelJourneyDetailDto();
                            TravelJourneyDetail travelJourneyDetail = detailAgg.getTravelJourneyDetail();
                            TravelJourneyEnum.FirstType firstType = funEnumMapping(TravelJourneyEnum.FirstType.class, travelJourneyDetail.getFirstType());
                            detailDto.setFirstType(firstType.getValue());
                            detailDto.setFirstTypeDesc(firstType.getName());
                            TravelJourneyEnum.SecondType secondType = funEnumMapping(TravelJourneyEnum.SecondType.class, travelJourneyDetail.getSecondType());
                            detailDto.setSecondType(secondType.getValue());
                            detailDto.setSecondTypeDesc(detailDto.getSecondTypeDesc());
                            TravelJourneyEnum.ThirdType thirdType = funEnumMapping(TravelJourneyEnum.ThirdType.class, travelJourneyDetail.getThirdType());
                            detailDto.setThirdType(thirdType.getValue());
                            detailDto.setThirdTypeDesc(detailDto.getThirdTypeDesc());


                            return detailDto;
                        }
                )
                .collect(Collectors.toList());


    }

    /**
     * 匹配SKU列表配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setSkuList(List) skuList}：SKU资源列表</li>
     * </ul>
     * 根据SKU资源聚合列表，构建包含SKU基本信息、数量限制、销售状态以及关联人群信息的SKU配置列表。
     * 销售状态根据SKU价格列表是否为空来判断。
     *
     * @param skuResourceAggList SKU资源聚合列表
     * @param packageResource    套餐资源对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchSkuList(List<SkuResourceAgg> skuResourceAggList, TicketFunTemplateResourcePackageInfo packageResource) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配sku", 0, packageResource.getPackageId(), "package");

            List<SkuResourceDto> skuResourceDtoList = skuResourceAggList.stream()
                    .map(skuResourceAgg -> {
                                SkuResource skuResource = skuResourceAgg.getSkuResource();
                                MainResourceCrowd resourceCrowd = skuResourceAgg.getRelationCrowd();
                                SkuResourceDto skuResourceDto = new SkuResourceDto();
                                skuResourceDto.setProductId(packageResource.getProductId());
                                skuResourceDto.setPackageId(packageResource.getPackageId());
                                skuResourceDto.setSkuId(skuResource.getSerialId());
                                skuResourceDto.setName(skuResource.getName());
                                skuResourceDto.setUnitQuantity(skuResource.getUnitQuantity());
                                skuResourceDto.setUnitMaxQuantity(skuResource.getUnitMaxQuantity());
                                skuResourceDto.setBookMinQuantity(skuResource.getMinNumber());
                                skuResourceDto.setBookMaxQuantity(skuResource.getMaxNumber());
                                skuResourceDto.setSaleAloneType(funEnumMapping(SkuResourceEnum.SaleAloneType.class, skuResource.getSaleAloneType()).getValue());
                                skuResourceDto.setSaleStatus(CollectionUtils.size(skuResourceAgg.getSkuPriceAggList()) > 0 ? ProductResourceEnum.SaleStatus.CAN_SALE.getValue() : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue());
                                ResourceBandInfoDto bandInfoDto = mainResourceCrowd2ResourceBandInfoDto()
                                        .apply(resourceCrowd);
                                skuResourceDto.setBandInfo(Collections.singletonList(bandInfoDto));
                                return skuResourceDto;
                            }
                    )
                    .collect(Collectors.toList());
            packageResource.setSkuList(skuResourceDtoList);

            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配sku结束,结果: " + JSON.toJSONString(skuResourceDtoList),
                    System.currentTimeMillis() - startTime, packageResource.getPackageId(), "package");

        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "skuList");
        }
    }

    /**
     * 解析公共的旅客问卷信息。
     * <p>
     * 根据主资源预订填写信息聚合对象，解析并构建公共的旅客问卷配置列表。
     * 该方法处理所有资源共用的问卷信息，包括问题类型、必填性、问题内容等基础配置。
     * <p>
     * 处理逻辑：
     * <ul>
     *   <li>过滤有效的问题类型ID</li>
     *   <li>映射问题类型信息</li>
     *   <li>构建问卷DTO对象</li>
     *   <li>设置问题类型、必填性、问题代码、标题和数据类型</li>
     * </ul>
     *
     * @param bookFillInfoAgg 主资源预订填写信息聚合对象
     * @return {@link List}<{@link BookPassengerQuestionDto}> 公共旅客问卷配置列表
     * @throws TRTransformException 当解析过程中发生异常时抛出
     */
    private List<BookPassengerQuestionDto> parseCommonBookPassengerQuestion(MainResourceBookFillInfoAgg bookFillInfoAgg) {
        try {
            MainResourceBookFillInfo bookFillInfo = bookFillInfoAgg.getBookFillInfo();
            Map<Long, QuestionTypeInfo> questionTypeInfoMap = bookFillInfoAgg.getQuestionTypeInfoList().stream().collect(Collectors.toMap(QuestionTypeInfo::getId, Function.identity(), (a, b) -> a));
            return bookFillInfoAgg.getBookTravellerInfoList().stream()
                    .filter(bookTravellerInfo -> bookTravellerInfo.getTypeInfoId() != null)
                    .filter(bookTravellerInfo -> questionTypeInfoMap.containsKey(bookTravellerInfo.getTypeInfoId()))
                    .map(bookTravellerInfo -> {
                                BookPassengerQuestionDto bookPassengerQuestionDto = new BookPassengerQuestionDto();
                                QuestionTypeInfo questionTypeInfo = questionTypeInfoMap.get(bookTravellerInfo.getTypeInfoId());
                                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                                bookPassengerQuestionDto.setPassengerRequiredType(funEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, bookFillInfo.getTravellerInfoNeedType()).getValue());
                                bookPassengerQuestionDto.setQuestionId(questionTypeInfo.getId());
                                bookPassengerQuestionDto.setQuestionCode(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionInfo.class, questionTypeInfo.getCode()).getValue());
                                bookPassengerQuestionDto.setQuestionTitle(questionTypeInfo.getName());
                                bookPassengerQuestionDto.setDataTypes(questionTypeInfo.getDataTypes());
                                return bookPassengerQuestionDto;
                            }
                    ).collect(Collectors.toList());
        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            LogUtils.error(LogOperateTypeEnum.OTHER, e, bookFillInfoAgg.getBookFillInfo().getMainResourceSerialId(), "");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, bookFillInfoAgg.getBookFillInfo().getMainResourceSerialId(), "", "bookPassengerQuestion");
        }
    }

    /**
     * 匹配产品销售汇总列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setLogicExtendList(List)}：包含月销量等销售统计信息。</li>
     * </ul>
     * 注意：当前方法为待实现状态，需要根据业务需求补充具体的销售数据统计逻辑。
     *
     * @param ignore  子资源详情对象（当前未使用）
     * @param ignore2 套餐资源对象（当前未使用）
     */
    private void matchProductSalesSummaryList(SubResourceDetail ignore, TicketFunTemplateResourcePackageInfo ignore2) {
        //TODO销售情况汇总
//        LogicExtendDto monthSaleNum = new LogicExtendDto(LogicExtendEnum.key.COMMENT_SCORE.getValue(), String.valueOf(detail.getMonthSaleNum()));

    }

    /**
     * 匹配逻辑扩展列表。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setLogicExtendList(List) logicExtendList}：逻辑扩展信息列表</li>
     * </ul>
     * 当前实现包含问卷相关的逻辑扩展信息，后续可根据业务需求扩展更多逻辑数据。
     *
     * @param logicExtend     子资源逻辑扩展对象
     * @param packageResource 套餐资源对象
     */
    private void matchLogicExtendList(SubResourceLogicExtend logicExtend, TicketFunTemplateResourcePackageInfo packageResource) {
        // TODO逻辑拓展存放哪些数据
        LogicExtendDto hasQuestionnaire = new LogicExtendDto(LogicExtendEnum.key.HAS_QUESTIONNAIRE.getValue(), String.valueOf(logicExtend.getHadPassengerQuestion()));
        if (CollectionUtils.isEmpty(packageResource.getLogicExtendList())) {
            packageResource.setLogicExtendList(new ArrayList<>());
        }
        packageResource.getLogicExtendList().add(hasQuestionnaire);
    }


    /**
     * 匹配旅客问卷信息。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#getBookPassengerQuestionList() bookPassengerQuestionList}：追加旅客问卷列表</li>
     * </ul>
     * 根据旅客问题聚合列表和旅客信息需求类型，构建完整的旅客问卷配置，
     * 包括问题类型、必填性、问题内容、数据类型、备注以及问题选项列表。
     * 如果问题列表为空，则直接返回不做处理。
     *
     * @param passengerQuestionList 旅客问题聚合列表
     * @param travellerInfoNeedType 旅客信息需求类型
     * @param packageResource       套餐资源对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchBookPassengerQuestion(List<PassengerQuestionAgg> passengerQuestionList, Integer travellerInfoNeedType, TicketFunTemplateResourcePackageInfo packageResource) {
        if (CollectionUtils.isEmpty(passengerQuestionList)) {
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配问卷", 0, packageResource.getPackageId(), "passengerQuestion");
            List<BookPassengerQuestionDto> bookPassengerQuestionList = passengerQuestionList.stream().map(passengerQuestionAgg -> {
                BookPassengerQuestionDto bookPassengerQuestionDto = new BookPassengerQuestionDto();
                QuestionTypeInfo questionTypeInfo = passengerQuestionAgg.getQuestionTypeInfo();
                bookPassengerQuestionDto.setQuestionType(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionType.class, questionTypeInfo.getType()).getValue());
                bookPassengerQuestionDto.setPassengerRequiredType(funEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, travellerInfoNeedType).getValue());
                bookPassengerQuestionDto.setQuestionId(questionTypeInfo.getId());
                bookPassengerQuestionDto.setQuestionCode(funEnumMapping(ResourceBookPassengerInfoEnum.QuestionInfo.class, questionTypeInfo.getCode()).getValue());
                bookPassengerQuestionDto.setQuestionTitle(questionTypeInfo.getName());
                bookPassengerQuestionDto.setDataTypes(questionTypeInfo.getDataTypes());
                bookPassengerQuestionDto.setRemark(passengerQuestionAgg.getQuestion().getInputTip());
                bookPassengerQuestionDto.setQuestionItemList(passengerQuestionAgg.getQuestionItemList()
                        .stream()
                        .map(questionItem -> new BookPassengerQuestionDto.QuestionItemDto(questionItem.getId(), questionItem.getValue()))
                        .collect(Collectors.toList())
                );
                return bookPassengerQuestionDto;
            }).collect(Collectors.toList());
            packageResource.getBookPassengerQuestionList().addAll(bookPassengerQuestionList);

            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配问卷结束,结果: " + JSON.toJSONString(bookPassengerQuestionList),
                    System.currentTimeMillis() - startTime, packageResource.getPackageId(), "package");
        } catch (Exception e) {
            LogUtils.error(LogOperateTypeEnum.OTHER, e, packageResource.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResource.getProductId(), packageResource.getPackageId(), "bookPassengerQuestion");
        }
    }

    /**
     * 解析预订联系人信息配置。
     * <p>
     * 根据主资源预订填写信息聚合对象，解析并构建预订时需要填写的联系人信息配置。
     * 包括姓名、手机号、邮箱等联系方式的必填性设置。
     * <p>
     * 解析规则：
     * <ul>
     *   <li>姓名：默认必填</li>
     *   <li>手机号：根据联系信息配置中是否包含手机类型来确定</li>
     *   <li>邮箱：根据联系信息配置中是否包含邮箱类型来确定</li>
     * </ul>
     *
     * @param bookFillInfoAgg 主资源预订填写信息聚合对象
     * @return {@link BookContactInfoDto} 预订联系人信息配置对象
     */
    private BookContactInfoDto parseBookContactInfo(MainResourceBookFillInfoAgg bookFillInfoAgg) {
        MainResourceBookFillInfo bookFillInfo = bookFillInfoAgg.getBookFillInfo();
        BookContactInfoDto bookContactInfo = new BookContactInfoDto();
        bookContactInfo.setNameRequired(WhetherEnum.YES.getValue());
        for (String contactInfo : bookFillInfo.getContactInfo().split(StrUtil.COMMA)) {
            if (!StrUtil.isNumeric(contactInfo)) {
                continue;
            }
            if (Integer.valueOf(contactInfo).equals(MainResourceBookFillInfoEnum.ContactInfoType.MOBILE.getValue())) {
                bookContactInfo.setMobileRequired(WhetherEnum.YES.getValue());
            } else {
                bookContactInfo.setMobileRequired(WhetherEnum.NO.getValue());
            }
            if (Integer.valueOf(contactInfo).equals(MainResourceBookFillInfoEnum.ContactInfoType.EMAIL.getValue())) {
                bookContactInfo.setEmailRequired(WhetherEnum.YES.getValue());
            } else {
                bookContactInfo.setEmailRequired(WhetherEnum.NO.getValue());
            }
            //TODO非大陆电子邮件
        }
        return bookContactInfo;
    }

    /**
     * 解析预订确认和凭证配置信息。
     * <p>
     * 根据主资源预订配置聚合对象，同时解析预订确认配置和凭证发送配置，
     * 返回包含两个配置对象的元组。
     * <p>
     * 返回内容：
     * <ul>
     *   <li>T1: {@link BookConfirmDto} 预订确认配置，包含确认模式和确认时长</li>
     *   <li>T2: {@link BookVoucherDto} 凭证配置，包含凭证发送方式和发送者类型</li>
     * </ul>
     *
     * @param bookConfigAgg 主资源预订配置聚合对象
     * @return {@link Tuple2}<{@link BookConfirmDto}, {@link BookVoucherDto}> 预订确认和凭证配置的元组
     * @throws TRTransformException 当解析过程中发生异常时抛出
     */
    private Tuple2<BookConfirmDto, BookVoucherDto> parseBookConfirmVoucher(MainResourceBookConfigAgg bookConfigAgg) {
        try {
            MainResourceBookConfig bookConfig = bookConfigAgg.getBookConfig();
            BookConfirmDto bookConfirmDto = getBookConfirmDto(bookConfig);
            BookVoucherDto bookVoucherDto = new BookVoucherDto();
            bookVoucherDto.setSendVoucher(bookConfig.getSendVoucher());
            bookVoucherDto.setVoucherSender(funEnumMapping(BookVoucherEnum.VoucherSender.class, bookConfig.getSendVoucherType()).getValue());
            //TODO凭证使用方式
            return Tuples.of(bookConfirmDto, bookVoucherDto);
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, bookConfigAgg.getBookConfig().getMainResourceSerialId(), bookConfigAgg.getBookConfig().getSubResourceSerialId(), "bookConfirm");
        }
    }

    /**
     * 构建预订确认DTO对象。
     * <p>
     * 根据主资源预订配置信息，构建包含确认模式和确认时长的预订确认对象。
     * 包括一级、二级、三级确认模式以及确认时长类型和具体分钟数。
     *
     * @param bookConfig 主资源预订配置对象
     * @return {@link BookConfirmDto} 预订确认DTO对象
     */
    private BookConfirmDto getBookConfirmDto(MainResourceBookConfig bookConfig) {
        BookConfirmDto bookConfirmDto = new BookConfirmDto();
        bookConfirmDto.setConfirmFirstMode(funEnumMapping(BookConfirmEnum.ConfirmFirstMode.class, bookConfig.getConfirmFirstMode()).getValue());
        bookConfirmDto.setConfirmSecondMode(funEnumMapping(BookConfirmEnum.ConfirmSecondMode.class, bookConfig.getConfirmSecondMode()).getValue());
        bookConfirmDto.setConfirmThirdMode(funEnumMapping(BookConfirmEnum.ConfirmThirdMode.class, bookConfig.getConfirmThirdMode()).getValue());
        bookConfirmDto.setConfirmDurationType(funEnumMapping(BookConfirmEnum.ConfirmDurationType.class, bookConfig.getConfirmDurationType()).getValue());
        bookConfirmDto.setConfirmMinutes(bookConfig.getConfirmDurationMinute());
        return bookConfirmDto;
    }

    /**
     * 解析服务提供方信息。
     * <p>
     * 根据主资源服务提供方对象，解析并构建包含品牌信息、售前售后服务信息、
     * 资质证照等完整的服务提供方配置。
     * <p>
     * 解析内容包括：
     * <ul>
     *   <li>品牌类型和品牌名称</li>
     *   <li>供应商名称</li>
     *   <li>售前服务提供方、工作日和联系电话</li>
     *   <li>售后服务提供方、工作日和联系电话</li>
     *   <li>许可证和营业执照信息</li>
     * </ul>
     *
     * @param serviceProvider 主资源服务提供方对象，如果为null则返回null
     * @return {@link ResourceServiceProviderDto} 服务提供方DTO对象，当输入为null时返回null
     * @throws TRTransformException 当解析过程中发生异常时抛出
     */
    private ResourceServiceProviderDto parseServiceProvider(MainResourceServiceProvider serviceProvider) {
        if (serviceProvider == null) {
            return null;
        }
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配服务提供方", 0, "", "package");
            ResourceServiceProviderDto serviceProviderDto = new ResourceServiceProviderDto();
            serviceProviderDto.setBrandType(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getBrandType()).getValue());
            serviceProviderDto.setBrandName(serviceProvider.getBrandName());
            serviceProviderDto.setVendorName(serviceProvider.getVendorName());
            serviceProviderDto.setPreServiceProvider(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getPreServiceProvider()).getValue());
            serviceProviderDto.setPreServiceWorkDay(serviceProvider.getPreServiceWorkDay());
            serviceProviderDto.setPreServicePhone(serviceProvider.getPreServicePhone());
            serviceProviderDto.setAfterServiceProvider(funEnumMapping(ResourceServiceProviderEnum.Provider.class, serviceProvider.getAfterServiceProvider()).getValue());
            serviceProviderDto.setAfterServiceWorkDay(serviceProvider.getAfterServiceWorkDay());
            serviceProviderDto.setAfterServicePhone(serviceProvider.getAfterServicePhone());
            serviceProviderDto.setLicense(serviceProvider.getLicense());
            serviceProviderDto.setBusinessLicense(serviceProvider.getBusinessLicense());
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配服务提供方结束,结果: " + JSON.toJSONString(serviceProviderDto),
                    System.currentTimeMillis() - startTime, "", "package");
            return serviceProviderDto;
        } catch (TRTransformException e) {
            throw e;
        } catch (Exception e) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, serviceProvider.getMainResourceSerialId(), "", "serviceProvider");
        }
    }


    /**
     * 解析退款政策配置。
     * <p>
     * 根据取消变更规则聚合对象，解析并构建完整的退款政策配置，
     * 包括退款类型、时间限制类型、部分退款设置以及详细的退款规则项列表。
     * <p>
     * 解析内容包括：
     * <ul>
     *   <li>退款类型：根据规则类型映射</li>
     *   <li>时间限制类型：根据日期类型映射</li>
     *   <li>部分退款：是否支持单独退款</li>
     *   <li>过期退款：默认设置为不支持</li>
     *   <li>附加说明：退款政策的额外说明</li>
     *   <li>退款规则项：包含时间比较、费用类型、费用值等详细规则</li>
     * </ul>
     *
     * @param cancelChangeRuleAgg 取消变更规则聚合对象
     * @return {@link RefundPolicyDto} 退款政策DTO对象
     */
    private RefundPolicyDto parseRefundPolicy(CancelChangeRuleAgg cancelChangeRuleAgg) {
        RefundPolicyDto refundPolicy = new RefundPolicyDto();
        CancelChangeRule changeRule = cancelChangeRuleAgg.getRule();
        refundPolicy.setRefundType(funEnumMapping(RefundPolicyEnum.RefundType.class, changeRule.getRuleType()).getValue());
        refundPolicy.setTimeLimitType(funEnumMapping(RefundPolicyEnum.TimeLimitType.class, changeRule.getDateType()).getValue());
        refundPolicy.setPartialRefund(changeRule.getIsCanSingle());
        //TODO是否支持过期退
        refundPolicy.setOverdueRefund(WhetherEnum.NO.getValue());
        refundPolicy.setAdditionalNote(changeRule.getAdditionalNote());
        refundPolicy.setItemList(cancelChangeRuleAgg.getRuleDetailList().stream().map(ruleDetail -> {
                    RefundPolicyDto.RefundPolicyItemDto itemDto = new RefundPolicyDto.RefundPolicyItemDto();
                    //TODO退款政策,比较时间类型如何映射
                    itemDto.setCompareDays(ruleDetail.getDay());
                    itemDto.setCompareHour(ruleDetail.getHour());
                    itemDto.setCompareMinute(ruleDetail.getHour());
                    TimeCompareEnum timeCompareEnum = enumMapping(TimeCompareEnum.class, ruleDetail.getTimeAfter());
                    if (TimeCompareEnum.AFTER.equals(timeCompareEnum)) {
                        itemDto.setCompareTimeAfter(TimeCompareRuleEnum.AFTER_EXCLUDE_CURRENT.getCode());
                    } else if (TimeCompareEnum.BEFORE.equals(timeCompareEnum)) {
                        itemDto.setCompareTimeAfter(TimeCompareRuleEnum.BEFORE_INCLUDE_CURRENT.getCode());
                    }

                    itemDto.setCostType(funEnumMapping(RefundPolicyEnum.CostType.class, ruleDetail.getFeeType()).getValue());
                    //数字格式
                    itemDto.setCostValue(String.valueOf(ruleDetail.getFee()));
                    itemDto.setCostCurrency(ruleDetail.getCostCurrency());
                    itemDto.setDescription(ruleDetail.getDescription());
                    return itemDto;
                }).collect(Collectors.toList())
        );
        return refundPolicy;
    }

    /**
     * 解析赠品列表配置。
     * <p>
     * 根据主资源赠品列表，解析并构建包含赠品内容、有效期类型和有效时间的赠品配置列表。
     * 如果输入列表为空，则返回null。
     * <p>
     * 解析内容包括：
     * <ul>
     *   <li>赠品内容描述</li>
     *   <li>有效期类型：根据赠品有效期类型枚举映射</li>
     *   <li>有效期开始时间：转换为时间戳格式</li>
     *   <li>有效期结束时间：转换为时间戳格式</li>
     * </ul>
     *
     * @param mainResourceGiftList 主资源赠品列表，如果为空则返回null
     * @return {@link List}<{@link ResourceGiftDto}> 赠品DTO列表，当输入为空时返回null
     * @throws TRTransformException 当解析过程中发生异常时抛出
     */
    private List<ResourceGiftDto> parseGiftList(List<MainResourceGift> mainResourceGiftList) {
        if (CollectionUtils.isEmpty(mainResourceGiftList)) {
            return null;
        }
        try {
            return mainResourceGiftList.stream().map(mainResourceGift -> {
                ResourceGiftDto resourceGiftDto = new ResourceGiftDto();
                resourceGiftDto.setContent(mainResourceGift.getContent());
                resourceGiftDto.setValidityTimeType(funEnumMapping(ResourceGiftEnum.ValidityTimeType.class, mainResourceGift.getValidityTimeType()).getValue());
                resourceGiftDto.setValidityBeginTime(LocalDateTimeUtil.toEpochMilli(mainResourceGift.getValidityBeginTime()));
                resourceGiftDto.setValidityEndTime(LocalDateTimeUtil.toEpochMilli(mainResourceGift.getValidityBeginTime()));
                return resourceGiftDto;
            }).collect(Collectors.toList());

        } catch (Exception ex) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, mainResourceGiftList.get(0).getMainResourceSerialId(), "", "giftList");
        }
    }

    /**
     * 匹配套餐预订确认信息。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setBookConfirm(BookConfirmDto) bookConfirm}：预订确认配置信息</li>
     * </ul>
     * 根据主资源预订配置聚合对象，提取并设置套餐的预订确认相关信息。
     *
     * @param bookConfigAgg      主资源预订配置聚合对象
     * @param packageResourceDto 套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchBookConfirm(MainResourceBookConfigAgg bookConfigAgg, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始套餐匹配预定确认", 0, packageResourceDto.getPackageId(), "package");
            Optional.ofNullable(bookConfigAgg.getBookConfig())
                    .map(this::getBookConfirmDto)
                    .ifPresent(packageResourceDto::setBookConfirm);
            LogUtils.info(LogOperateTypeEnum.OTHER, "套餐匹配预定确认结束,结果: " + JSON.toJSONString(packageResourceDto.getBookConfirm()),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "bookConfirm");
        }
    }

    /**
     * 匹配预订限制配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setBookLimit(BookLimitDto) bookLimit}：预订限制配置</li>
     * </ul>
     * 根据资源预订限制和主资源预订配置，构建完整的预订限制配置，包括提前预订时间、
     * 预订天数范围、预订人数限制以及支付超时时间等。
     *
     * @param bookLimit          资源预订限制对象
     * @param bookConfig         主资源预订配置对象
     * @param packageResourceDto 套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchBookLimit(ResourceBookLimit bookLimit, MainResourceBookConfig bookConfig, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配预定限制", 0, packageResourceDto.getPackageId(), "package");
            BookLimitDto bookLimitDto = new BookLimitDto();
            bookLimitDto.setNeedAdvance(WhetherEnum.YES.getValue());
            bookLimitDto.setAdvanceBookDays(bookLimit.getAdvanceDay());
            bookLimitDto.setAdvanceBookHour(bookLimit.getAdvanceHour());
            bookLimitDto.setAdvanceBookMinute(bookLimit.getAdvanceMinute());
            bookLimitDto.setBookMaxDay(bookLimit.getMaxDay());
            bookLimitDto.setBookMinDay(bookLimit.getMinDay());
            bookLimitDto.setBookMinQuantity(bookLimit.getMinPerson());
            bookLimitDto.setBookMaxQuantity(bookLimit.getMaxPerson());
            bookLimitDto.setPayTimeoutMinutes(bookConfig.getPayConfirmMinute());
            packageResourceDto.setBookLimit(bookLimitDto);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配预定限制结束,结果: " + JSON.toJSONString(bookLimitDto),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "bookLimit");
        }

    }

    /**
     * 解析人群列表，提取SKU关联的人群代码。
     * <p>
     * 从SKU资源聚合列表中提取关联的人群信息，并转换为人群代码枚举列表。
     * 会自动去重，确保返回的人群代码列表中没有重复项。
     * <p>
     * 处理逻辑：
     * <ul>
     *   <li>遍历所有SKU资源聚合对象</li>
     *   <li>提取每个SKU关联的人群信息</li>
     *   <li>将人群代码映射为对应的枚举值</li>
     *   <li>去重并返回唯一的人群代码列表</li>
     * </ul>
     *
     * @param skuResourceAggList SKU资源聚合列表
     * @return {@link List}<{@link ResourceBandInfoEnum.BandCode}> 去重后的人群代码枚举列表
     */
    private List<ResourceBandInfoEnum.BandCode> parseCrowdList(List<SkuResourceAgg> skuResourceAggList) {
        return skuResourceAggList.stream().map(SkuResourceAgg::getRelationCrowd)
                .map(resourceCrowd ->
                        funEnumMapping(ResourceBandInfoEnum.BandCode.class, resourceCrowd.getCode())
                ).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 匹配条款列表配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setClauseList(List) clauseList}：按条款类型分组的条款列表</li>
     * </ul>
     * 根据旅程条款聚合列表和人群列表，构建按条款类型分组的条款配置，
     * 包括条款类型、条款内容以及适用人群等信息。如果条款列表为空，则直接返回。
     *
     * @param clauseAggList      旅程条款聚合列表
     * @param crowdList          人群代码列表，用于设置条款项的适用人群
     * @param packageResourceDto 套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchClauseList(List<TravelJourneyClauseAgg> clauseAggList, List<ResourceBandInfoEnum.BandCode> crowdList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (CollectionUtils.isEmpty(clauseAggList)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配条款", 0, packageResourceDto.getPackageId(), "package");
            List<ClauseTypeDto> clauseTypeDtoList = clauseAggList.stream().collect(Collectors.groupingBy(resourceClauseAgg -> resourceClauseAgg.getClause().getTypeCode()))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                                String clauseType = entry.getKey();
                                List<TravelJourneyClauseAgg> mainResourceClauses = entry.getValue();
                                ClauseTypeDto clauseTypeDto = new ClauseTypeDto();
                                clauseTypeDto.setClauseTypeCode(clauseType);
                                clauseTypeDto.setClauseAvailableCount(mainResourceClauses.size());
                                ClauseEnum.TypeCodeEnum typeCodeEnum = enumMapping(ClauseEnum.TypeCodeEnum.class, clauseType);
                                clauseTypeDto.setTitle(typeCodeEnum.getName());
                                List<ClauseTypeDto.ClauseDto> clauses = mainResourceClauses
                                        .stream()
                                        .map(travelJourneyClauseAgg -> {
                                                    ClauseTypeDto.ClauseDto clauseDto = new ClauseTypeDto.ClauseDto();
                                                    TravelJourneyClause clause = travelJourneyClauseAgg.getClause();
                                                    clauseDto.setClauseCode(clause.getClauseCode());
                                                    clauseDto.setTitle(clause.getClauseName());
                                                    List<ClauseTypeDto.ClauseItemDto> clauseItemList = travelJourneyClauseAgg.getClauseItemAggList()
                                                            .stream()
                                                            .map(travelJourneyClauseItemAgg -> {
                                                                        ClauseTypeDto.ClauseItemDto clauseItemDto = new ClauseTypeDto.ClauseItemDto();
                                                                        TravelJourneyClauseItem clauseItem = travelJourneyClauseItemAgg.getClauseItem();
                                                                        clauseItemDto.setClauseItemCode(clauseItem.getCode());
                                                                        clauseItemDto.setTitle(clauseItem.getCreateName());
                                                                        clauseItemDto.setContent(clauseItem.getDescription());
                                                                        String crowdCodes = Optional.ofNullable(clauseItem.getCrowdCodes()).filter(StrUtil::isNotBlank).orElse(crowdList.stream().map(ResourceBandInfoEnum.BandCode::getValue).collect(Collectors.joining(StrUtil.COMMA)));
                                                                        clauseItemDto.setSuitCrowdCodes(crowdCodes);
                                                                        return clauseItemDto;
                                                                    }
                                                            ).collect(Collectors.toList());
                                                    clauseDto.setClauseItemList(clauseItemList);
                                                    return clauseDto;
                                                }
                                        ).collect(Collectors.toList());
                                clauseTypeDto.setClauseList(clauses);
                                return clauseTypeDto;
                            }
                    ).collect(Collectors.toList());
            packageResourceDto.setClauseList(clauseTypeDtoList);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配条款结束,结果: " + JSON.toJSONString(clauseTypeDtoList),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "clauseList");
        }

    }

    /**
     * 匹配附加费用列表配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setSurchargeList(List) surchargeList}：附加费用列表</li>
     * </ul>
     * 根据子资源附加费用列表，构建包含费用类型、收取类型、收取方式、费用承担方、
     * 退款类型以及费用金额等信息的附加费用配置。如果附加费用列表为空，则直接返回。
     *
     * @param surchargeList      子资源附加费用列表
     * @param packageResourceDto 套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchSurchargeList(List<SubResourceSurcharge> surchargeList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (CollectionUtils.isEmpty(surchargeList)) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配附加费用", 0, packageResourceDto.getPackageId(), "package");
            List<ResourceSurchargeDto> resourceSurchargeList = surchargeList.stream().map(subResourceSurcharge -> {
                ResourceSurchargeDto resourceSurchargeDto = new ResourceSurchargeDto();
                ResourceSurchargeEnum.FeeType feeType = funEnumMapping(ResourceSurchargeEnum.FeeType.class, subResourceSurcharge.getFeeType());
                resourceSurchargeDto.setFeeType(feeType.getValue());
                resourceSurchargeDto.setCollectType(funEnumMapping(ResourceSurchargeEnum.CollectType.class, subResourceSurcharge.getCollectType()).getValue());
                resourceSurchargeDto.setCollectWay(funEnumMapping(ResourceSurchargeEnum.CollectWay.class, subResourceSurcharge.getCollectWay()).getValue());
                resourceSurchargeDto.setCostBear(funEnumMapping(ResourceSurchargeEnum.CostBear.class, subResourceSurcharge.getCostBear()).getValue());
                resourceSurchargeDto.setRefundType(funEnumMapping(ResourceSurchargeEnum.RefundType.class, subResourceSurcharge.getCostIsRefund()).getValue());
                //TODO附加费用币种

                resourceSurchargeDto.setAmount(subResourceSurcharge.getAmount());
                return resourceSurchargeDto;
            }).collect(Collectors.toList());
            packageResourceDto.setSurchargeList(resourceSurchargeList);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配附加费用结束,结果: " + JSON.toJSONString(resourceSurchargeList),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "surcharge");
        }
    }

    /**
     * 匹配配送信息配置。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setDeliveryInfo(ResourceDeliveryInfoDto) deliveryInfo}：配送信息配置</li>
     * </ul>
     * 根据子资源取件退件信息，构建包含配送类型、配送时间节点以及具体配送时间的配送配置。
     * 如果子资源取件退件信息为null，则直接返回不做处理。
     *
     * @param subResourceTakeReturn 子资源取件退件信息对象
     * @param packageResourceDto    套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchDeliveryInfo(SubResourceTakeReturn subResourceTakeReturn, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            if (subResourceTakeReturn == null) {
                return;
            }
            long startTime = System.currentTimeMillis();
            LogUtils.info(LogOperateTypeEnum.OTHER, "开始匹配配送信息", 0, packageResourceDto.getPackageId(), "package");

            ResourceDeliveryInfoDto deliveryInfo = new ResourceDeliveryInfoDto();
            deliveryInfo.setDeliveryType(funEnumMapping(ResourceDeliveryInfoEnum.DeliveryType.class, subResourceTakeReturn.getTakeType()).getValue());
            SubResourceTakeReturnEnum.LatestDeliveryTimeNode latestDeliveryTimeNode = enumMapping(SubResourceTakeReturnEnum.LatestDeliveryTimeNode.class, subResourceTakeReturn.getLatestDeliveryTimeNode());
            SubResourceTakeReturnEnum.LatestDeliveryTimeAfter latestDeliveryTimeAfter = enumMapping(SubResourceTakeReturnEnum.LatestDeliveryTimeAfter.class, subResourceTakeReturn.getLatestDeliveryTimeAfter());
            TimeNodeEnum timeNodeEnum = Optional.ofNullable(TimeNodeEnum.fromFunEnum(latestDeliveryTimeNode, latestDeliveryTimeAfter)).orElseThrow(() -> new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, TimeNodeEnum.class.getSimpleName(), subResourceTakeReturn.getLatestDeliveryTimeNode() + "_" + subResourceTakeReturn.getLatestDeliveryTimeAfter()));
            deliveryInfo.setDeliveryTimeNode(timeNodeEnum.getValue());
            deliveryInfo.setDeliveryTimeDays(subResourceTakeReturn.getLatestDeliveryTimeDay());
            deliveryInfo.setDeliveryTimeHour(subResourceTakeReturn.getLatestDeliveryTimeHour());
            deliveryInfo.setDeliveryTimeMinute(subResourceTakeReturn.getLatestDeliveryTimeMinute());
            packageResourceDto.setDeliveryInfo(deliveryInfo);
            LogUtils.info(LogOperateTypeEnum.OTHER, "匹配配送信息结束,结果: " + JSON.toJSONString(deliveryInfo),
                    System.currentTimeMillis() - startTime, packageResourceDto.getPackageId(), "package");
        } catch (TRTransformException ex) {
            throw ex;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "deliveryInfo");
        }
    }

    /**
     * 匹配套餐销售属性信息。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link TicketFunTemplateResourcePackageInfo#setSalePropertyList(List) salePropertyList}：销售属性列表</li>
     * </ul>
     * 根据套餐属性聚合列表，解析并构建套餐的销售属性配置，包括属性ID、名称、代码以及对应的属性值信息。
     * 当前实现取每个属性的第一个选项作为默认值。
     *
     * @param setmealOptionsRelationAggList 套餐属性聚合列表
     * @param packageResourceDto            套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchSaleProperty(List<SetmealAttributeAgg> setmealOptionsRelationAggList, TicketFunTemplateResourcePackageInfo packageResourceDto) {
        try {
            //TODO销售属性逻辑待确认、子销售属性
            List<PackageSalePropertyDto> packageSalePropertyList = setmealOptionsRelationAggList.stream().map(setmealAttributeAgg -> {
                SetmealAttribute setmealAttribute = setmealAttributeAgg.getSetmealAttribute();
                PackageSalePropertyDto packageSalePropertyDto = new PackageSalePropertyDto();
                packageSalePropertyDto.setPropertyId(setmealAttribute.getId());
                packageSalePropertyDto.setPropertyName(setmealAttribute.getAttributeName());
                packageSalePropertyDto.setPropertyCode(setmealAttribute.getCode());
                setmealAttributeAgg.getSetmealOptionsAggList()
                        .stream()
                        .findFirst()
                        .ifPresent(setmealOptionsAgg -> {
                            SetmealOptions setmealOptions = setmealOptionsAgg.getSetmealOptions();
                            packageSalePropertyDto.setPropertyValueId(String.valueOf(setmealOptions.getId()));
                            packageSalePropertyDto.setPropertyValueName(setmealOptions.getOptionName());
                        });
                return packageSalePropertyDto;
            }).collect(Collectors.toList());
            packageResourceDto.setSalePropertyList(packageSalePropertyList);
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, packageResourceDto.getPackageId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, packageResourceDto.getProductId(), packageResourceDto.getPackageId(), "saleProperty");
        }
    }

    /**
     * 匹配套餐基础信息。
     * <p>
     * 已赋值字段：
     * <ul>
     *   <li>{@link PackageResourceDto#setProductId(String) productId}：产品ID</li>
     *   <li>{@link PackageResourceDto#setPackageId(String) packageId}：套餐ID</li>
     *   <li>{@link PackageResourceDto#setName(String) name}：套餐名称</li>
     *   <li>{@link PackageResourceDto#setServiceLanguageList(List) serviceLanguageList}：服务语言列表</li>
     *   <li>{@link PackageResourceDto#setSaleStatus(String) saleStatus}：销售状态</li>
     *   <li>{@link PackageResourceDto#setCreateTime(Long) createTime}：创建时间</li>
     * </ul>
     * 根据子资源和子资源详情信息，设置套餐的基本属性信息。
     *
     * @param subResource       子资源对象
     * @param subResourceDetail 子资源详情对象
     * @param packageResource   套餐资源DTO对象
     * @throws TRTransformException 当转换过程中发生异常时抛出
     */
    private void matchBaseInfo(SubResource subResource, SubResourceDetail subResourceDetail, PackageResourceDto packageResource) {
        try {
            packageResource.setProductId(subResource.getMainResourceSerialId());
            packageResource.setPackageId(subResource.getSerialId());
            packageResource.setName(subResource.getTitle());
            //TODO服务语言
            packageResource.setServiceLanguageList(JSON.parseArray(subResource.getLanguage(), String.class));
            packageResource.setSaleStatus(WhetherEnum.YES.getValue().equals(subResourceDetail.getIsCanAdvance()) ? ProductResourceEnum.SaleStatus.CAN_SALE.getValue() : ProductResourceEnum.SaleStatus.CAN_NOT_SALE.getValue());
            packageResource.setCreateTime(LocalDateTimeUtil.toEpochMilli(subResource.getCreateDate()));
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex, subResource.getSerialId(), "package");
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_TRANSFORM_FAIL, subResource.getMainResourceSerialId(), subResource.getSerialId(), "baseInfo");
        }
    }

}
