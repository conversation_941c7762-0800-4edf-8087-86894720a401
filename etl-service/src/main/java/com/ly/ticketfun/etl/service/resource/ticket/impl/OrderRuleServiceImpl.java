package com.ly.ticketfun.etl.service.resource.ticket.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.SkyLogUtils;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.base.GenderEnum;
import com.ly.ticketfun.etl.common.enums.base.ZodiacEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.*;
import com.ly.ticketfun.etl.common.enums.ticket.*;
import com.ly.ticketfun.etl.common.utils.ConfigClientUtils;
import com.ly.ticketfun.etl.common.utils.JsonUtils;
import com.ly.ticketfun.etl.common.utils.PolicyMapSortUtil;
import com.ly.ticketfun.etl.dataService.tczbyresource.*;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.IPrecastTicketManageService;
import com.ly.ticketfun.etl.domain.bo.LimitCondition;
import com.ly.ticketfun.etl.domain.bo.MpIncludeTypeUrlSettingBo;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.PrecastTicketManage;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.service.resource.common.transform.TransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.ticket.IOrderRuleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderRuleServiceImpl extends TransformBaseServiceImpl implements IOrderRuleService {

    @Resource
    private IPolicyOrderModeService policyOrderModeService;
    @Resource
    private IPolicyOrderLimitService policyOrderLimitService;
    @Resource
    private IPolicyOrderLimitRuleService policyOrderLimitRuleService;
    @Resource
    private IPolicyTravellerBaseService policyTravellerBaseService;
    @Resource
    private IPolicyTravellerCrowdInfoService policyTravellerCrowdInfoService;
    @Resource
    private IPolicyTravellerCrowdLimitService policyTravellerCrowdLimitService;
    @Resource
    private IPolicyUseRuleService policyUseRuleService;
    @Resource
    private IPolicyUseRuleAdressService policyUseRuleAddressService;
    @Resource
    private IPolicyRefundRuleService policyRefundRuleService;
    @Resource
    private IPolicyRefundRuleDetailService policyRefundRuleDetailService;
    @Resource
    private IPolicyMultipleOptionsService policyMultipleOptionsService;
    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;
    @Resource
    private IPrecastTicketManageService precastTicketManageService;
    @Resource
    private IPolicyFeeIncludeItemService policyFeeIncludeItemService;
    @Resource
    private IPolicyFeeIncludeItemExtendService policyFeeIncludeItemExtendService;
    @Resource
    private IMarketProductTagService marketProductTagService;
    @Resource
    private IMarketPolicyTagRelationService marketPolicyTagRelationService;
    @Resource
    private IResourceHotelIncludeItemsService resourceHotelIncludeItemsService;


    @Override
    public void toTemplateResource(Long resourceId, Long policyId) throws Exception {
        QueryWrapper<PolicyOrderMode> orderModeQueryWrapper = of(PolicyOrderMode.class, policyId, resourceId);
        PolicyOrderMode orderMode = policyOrderModeService.queryOne(orderModeQueryWrapper);

        QueryWrapper<PolicyOrderLimit> orderLimitQueryWrapper = of(PolicyOrderLimit.class, policyId, resourceId);
        PolicyOrderLimit orderLimit = policyOrderLimitService.queryOne(orderLimitQueryWrapper);

        QueryWrapper<PolicyOrderLimitRule> orderLimitRuleQueryWrapper = of(PolicyOrderLimitRule.class, policyId, resourceId);
        List<PolicyOrderLimitRule> orderLimitRuleList = policyOrderLimitRuleService.queryList(orderLimitRuleQueryWrapper);

        QueryWrapper<PolicyTravellerBase> travelBaseQueryWrapper = of(PolicyTravellerBase.class, policyId, resourceId);
        PolicyTravellerBase travellerBase = policyTravellerBaseService.queryOne(travelBaseQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdInfo> travelCrowdInfoQueryWrapper = of(PolicyTravellerCrowdInfo.class, policyId, resourceId);
        List<PolicyTravellerCrowdInfo> crowdInfoList = policyTravellerCrowdInfoService.queryList(travelCrowdInfoQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdLimit> travelCrowdLimitQueryWrapper = of(PolicyTravellerCrowdLimit.class, policyId, resourceId);
        List<PolicyTravellerCrowdLimit> travellerCrowdLimitList = policyTravellerCrowdLimitService.queryList(travelCrowdLimitQueryWrapper);

        QueryWrapper<PolicyUseRule> useRuleQueryWrapper = of(PolicyUseRule.class, policyId, resourceId);
        PolicyUseRule useRule = policyUseRuleService.queryOne(useRuleQueryWrapper);

        QueryWrapper<PolicyUseRuleAdress> useRuleAddressQueryWrapper = of(PolicyUseRuleAdress.class, policyId, resourceId);
        List<PolicyUseRuleAdress> useRuleAdressesList = policyUseRuleAddressService.queryList(useRuleAddressQueryWrapper);

        QueryWrapper<PolicyRefundRule> refundRuleQueryWrapper = of(PolicyRefundRule.class, policyId, resourceId);
        PolicyRefundRule refundRule = policyRefundRuleService.queryOne(refundRuleQueryWrapper);

        QueryWrapper<PolicyRefundRuleDetail> refundRuleDetailQueryWrapper = of(PolicyRefundRuleDetail.class, policyId, resourceId);
        List<PolicyRefundRuleDetail> refundChangeRuleDetailList = policyRefundRuleDetailService.queryList(refundRuleDetailQueryWrapper);

        QueryWrapper<PolicyMultipleOptions> multipleOptionsQueryWrapper = of(PolicyMultipleOptions.class, policyId, resourceId);
        List<PolicyMultipleOptions> policyMultipleOptions = policyMultipleOptionsService.queryList(multipleOptionsQueryWrapper);

        QueryWrapper<PolicyBaseInfo> policyQueryWrapper = of(PolicyBaseInfo.class, policyId, resourceId);
        PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryOne(policyQueryWrapper);

        QueryWrapper<PolicyFeeIncludeItem> policyFeeIncludeItemQueryWrapper = of(PolicyFeeIncludeItem.class, policyId, resourceId);
        List<PolicyFeeIncludeItem> policyFeeIncludeItems = policyFeeIncludeItemService.queryList(policyFeeIncludeItemQueryWrapper);

        QueryWrapper<PolicyFeeIncludeItemExtend> policyFeeIncludeItemExtendQueryWrapper = of(PolicyFeeIncludeItemExtend.class, policyId, resourceId);
        List<PolicyFeeIncludeItemExtend> policyFeeIncludeItemExtends = policyFeeIncludeItemExtendService.queryList(policyFeeIncludeItemExtendQueryWrapper);

        QueryWrapper<ResourceHotelIncludeItems> resourceHotelIncludeItemsQueryWrapper = of(ResourceHotelIncludeItems.class, policyId, resourceId);
        List<ResourceHotelIncludeItems> resourceHotelIncludeItemList = resourceHotelIncludeItemsService.queryList(resourceHotelIncludeItemsQueryWrapper);

        LambdaQueryWrapper<PrecastTicketManage> PrecastTicketManageQueryWrapper = new LambdaQueryWrapper<PrecastTicketManage>()
                .eq(PrecastTicketManage::getResourceId, resourceId)
                .eq(PrecastTicketManage::getIgnoreStatus, 1)
                .eq(PrecastTicketManage::getRowStatus, 1);
        List<PrecastTicketManage> precastTicketManages = precastTicketManageService.queryList(PrecastTicketManageQueryWrapper);

        BookModeDto bookModeDto = orderMode(orderMode, policyBaseInfo, precastTicketManages);
        BookLimitDto bookLimitDto = orderLimit(orderLimit, orderLimitRuleList);
        //游玩人信息+是否需要游玩人信息
        List<BookPassengerQuestionDto> bookPassengerQuestionList = travellerInfo(policyMultipleOptions, travellerBase);
        //适用人群
        List<ResourceBandInfoDto> bandInfoList = bandInfoList(crowdInfoList, travellerCrowdLimitList, travellerBase);
        //购票人信息
        BookContactInfoDto bookContactInfoDto = travellerContact(policyMultipleOptions);
        //核销方式
        UseRuleDto useRuleDto = useRule(useRule, useRuleAdressesList);
        BookVoucherDto voucherDto = voucher(policyMultipleOptions);

        RefundPolicyDto refundPolicyDto = refundRule(refundRule, refundChangeRuleDetailList);
        //条款=费用包含不含
        List<ClauseTypeDto> clauseList = feeDesc(policyFeeIncludeItems, policyFeeIncludeItemExtends);

        //酒景
        List<ClauseTypeDto> sceneryHotelList = sceneryHotel(resourceHotelIncludeItemList);
    }

    private List<ClauseTypeDto> sceneryHotel(List<ResourceHotelIncludeItems> resourceHotelIncludeItemList) {
        List<ClauseTypeDto> list = new ArrayList<>();
        ClauseTypeDto dto = new ClauseTypeDto();
        dto.setClauseTypeCode("SCENERY_HOTEL");
        dto.setTitle("景+酒信息设置");
        dto.setClauseList(new ArrayList<>());
        list.add(dto);
        //主图
        ResourceHotelIncludeItems img = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 2)
                .findFirst()
                .orElse(null);
        if (img != null) {
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_PIC.getCode());
            typeDto.setTitle(FeeDescEnum.SCENERY_HOTEL_PIC.getMessage());
            typeDto.setContent(img.getMoreRemarks());
            dto.getClauseList().add(typeDto);
        }
        //预约
        ResourceHotelIncludeItems booking = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 1)
                .findFirst()
                .orElse(null);
        if (booking != null) {
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_BOOKING.getCode());
            typeDto.setTitle(FeeDescEnum.SCENERY_HOTEL_BOOKING.getMessage());
            typeDto.setBookType(ticketEnumMapping(SceneryHotelBookTypeEnum.class, booking.getNeedTimesBook(), false).getCode());
            typeDto.setContent(booking.getMoreRemarks());
            dto.getClauseList().add(typeDto);
        }
        //酒店项目
        List<ResourceHotelIncludeItems> hotelList = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 2)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(hotelList)) {
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_HOTEL.getCode());
            typeDto.setTitle(FeeDescEnum.SCENERY_HOTEL_HOTEL.getMessage());
            typeDto.setClauseItemList(new ArrayList<>());
            for (ResourceHotelIncludeItems hotelItem : hotelList) {
                ClauseTypeDto.ClauseItemDto itemDto = new ClauseTypeDto.ClauseItemDto();
                itemDto.setContent(hotelItem.getItemName() + hotelItem.getBetweenNum() + "间" + hotelItem.getNightNum()
                        + "夜" + hotelItem.getItemExtend() + hotelItem.getItemNum() + "份");
                itemDto.setCityName(hotelItem.getCityName());
                itemDto.setProvinceName(hotelItem.getProvinceName());
                itemDto.setSceneryHotelId(hotelItem.getResourceId().toString());
                itemDto.setSceneryHotelName(hotelItem.getItemResourceName());
                typeDto.getClauseItemList().add(itemDto);
            }
            dto.getClauseList().add(typeDto);
        }
        //酒店数量
        List<ResourceHotelIncludeItems> hotelNum = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 201)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(hotelNum)) {
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_HOTEL_NUM.getCode());
            typeDto.setTitle(FeeDescEnum.SCENERY_HOTEL_HOTEL_NUM.getMessage());
            typeDto.setItemAvailableCount(hotelNum.get(0).getSelectNum());
            typeDto.setContent(hotelNum.get(0).getSelectOption() == 0 ? "用户可选" : "商家指定");
            dto.getClauseList().add(typeDto);
        }
        //景区项目
        List<ResourceHotelIncludeItems> sceneryList = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 1)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sceneryList)) {
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_SCENERY.getCode());
            typeDto.setTitle(FeeDescEnum.SCENERY_HOTEL_SCENERY.getMessage());
            typeDto.setClauseItemList(new ArrayList<>());
            for (ResourceHotelIncludeItems hotelItem : sceneryList) {
                ClauseTypeDto.ClauseItemDto itemDto = new ClauseTypeDto.ClauseItemDto();
                itemDto.setContent(hotelItem.getItemName() + hotelItem.getItemNum() + hotelItem.getItemExtend());
                itemDto.setSceneryHotelId(hotelItem.getResourceId().toString());
                itemDto.setSceneryHotelName(hotelItem.getItemResourceName());
                typeDto.getClauseItemList().add(itemDto);
            }
            dto.getClauseList().add(typeDto);
        }
        //酒店数量
        Integer sceneryNum = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 101)
                .map(ResourceHotelIncludeItems::getSelectNum)
                .findFirst()
                .orElse(0);
        ClauseTypeDto.ClauseDto typehotelNumDto = new ClauseTypeDto.ClauseDto();
        typehotelNumDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_SCENERY_NUM.getCode());
        typehotelNumDto.setTitle(FeeDescEnum.SCENERY_HOTEL_SCENERY_NUM.getMessage());
        typehotelNumDto.setItemAvailableCount(sceneryNum);
        dto.getClauseList().add(typehotelNumDto);
        //其他权益
        List<ResourceHotelIncludeItems> rightList = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 4)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(rightList)) {
            ClauseTypeDto.ClauseDto typeRightDto = new ClauseTypeDto.ClauseDto();
            typeRightDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_RIGHT.getCode());
            typeRightDto.setTitle(FeeDescEnum.SCENERY_HOTEL_RIGHT.getMessage());
            typeRightDto.setClauseItemList(new ArrayList<>());
            for (ResourceHotelIncludeItems item : rightList) {
                ClauseTypeDto.ClauseItemDto itemDto = new ClauseTypeDto.ClauseItemDto();
                itemDto.setContent(item.getItemName() + item.getItemNum() + item.getItemExtend());
                typeRightDto.getClauseItemList().add(itemDto);
            }
            dto.getClauseList().add(typeRightDto);
        }
        //补充说明
        String desc = resourceHotelIncludeItemList.stream()
                .filter(i -> i.getMainType() == 0 && i.getIncludeType() == 3)
                .map(ResourceHotelIncludeItems::getMoreRemarks)
                .findFirst()
                .orElse("");
        ClauseTypeDto.ClauseDto typeDescDto = new ClauseTypeDto.ClauseDto();
        typeDescDto.setClauseCode(FeeDescEnum.SCENERY_HOTEL_DESC.getCode());
        typeDescDto.setTitle(FeeDescEnum.SCENERY_HOTEL_DESC.getMessage());
        typeDescDto.setContent(desc);
        dto.getClauseList().add(typeDescDto);
        return list;
    }


    /**
     * 套餐销售属性
     *
     * @param poiId     poiId
     * @param productId productId
     * @param policyId  policyId
     * @return 销售属性组map
     */
    private Map<Long/*销售属性组id*/, List<MarketProductTag>/*销售属性组*/> getSalePropertyList(
            Long poiId,
            Long productId,
            Long policyId) {
        List<MarketProductTag> marketProductTags = marketProductTagService.queryList(new QueryWrapper<MarketProductTag>()
                .eq("resource_id", poiId)
                .eq("product_id", productId)
                .eq("type_id", 1)
                .eq("row_status", 1)
        );

        List<MarketPolicyTagRelation> marketPolicyTagRelations = marketPolicyTagRelationService.queryList(new QueryWrapper<MarketPolicyTagRelation>()
                .eq("mount_resource_id", poiId)
                .eq("mount_product_id", productId)
                .eq("policy_id", policyId)
                .eq("row_status", 1)
        );
        Map<Long, List<MarketProductTag>> map = new HashMap<>();
        for (Map.Entry<Long, List<MarketPolicyTagRelation>> entry : marketPolicyTagRelations
                .stream()
                .collect(Collectors.groupingBy(MarketPolicyTagRelation::getPolicyIdUk))
                .entrySet()) {
            List<MarketProductTag> marketProductTagList = Lists.newArrayListWithExpectedSize(entry.getValue().size());
            for (MarketPolicyTagRelation marketPolicyTagRelation : entry.getValue()) {
                Optional<MarketProductTag> tagOptional = marketProductTags
                        .stream()
                        .filter(t -> t.getId().equals(marketPolicyTagRelation.getTagId()))
                        .findFirst();
                if (tagOptional.isPresent()) {
                    marketProductTagList.add(tagOptional.get());
                } else {
                    SkyLogUtils.warn(LogOperateTypeEnum.QUERY.getOpType(),
                            "OrderRuleService", "getSalePropertyList",
                            "未找到tagId对应的tag",
                            policyId.toString(), marketPolicyTagRelation.getId().toString());
                    continue;
                }
            }
            map.put(entry.getKey(), marketProductTagList);
        }
        return map;
    }


    private List<ClauseTypeDto> feeDesc(List<PolicyFeeIncludeItem> policyFeeIncludeItems, List<PolicyFeeIncludeItemExtend> policyFeeIncludeItemExtends) {
        List<ClauseTypeDto> list = new ArrayList<>();
        ClauseTypeDto dto = new ClauseTypeDto();
        dto.setClauseTypeCode("FEE_INCLUDE");
        dto.setTitle("费用包含");
        dto.setClauseList(new ArrayList<>());
        list.add(dto);
        Map<Integer, List<PolicyFeeIncludeItem>> collect = policyFeeIncludeItems
                .stream()
                .collect(Collectors.groupingBy(PolicyFeeIncludeItem::getItemType));
        Set<Map.Entry<Integer, List<PolicyFeeIncludeItem>>> entries = collect.entrySet();
        entries.forEach(entry -> {
            Integer itemType = entry.getKey();
            List<PolicyFeeIncludeItem> value = entry.getValue()
                    .stream()
                    .sorted(Comparator.comparing(PolicyFeeIncludeItem::getItemSort).reversed())
                    .collect(Collectors.toList());
            ClauseTypeDto.ClauseDto typeDto = new ClauseTypeDto.ClauseDto();
            typeDto.setClauseCode(ticketEnumMapping(FeeDescEnum.class, itemType, false).getCode());
            typeDto.setTitle(ticketEnumMapping(FeeDescEnum.class, itemType, false).getMessage());
            if (itemType.equals(FeeIncludeTypeEnums.INCLUDE_FEE.getType())) {
                //region 费用包含
                for (PolicyFeeIncludeItem item : value) {
                    //该item的各个列
                    List<PolicyFeeIncludeItemExtend> extendList = policyFeeIncludeItemExtends
                            .stream()
                            .filter(e -> e.getItemId().equals(item.getId()))
                            .collect(Collectors.toList());
                    String project = extendList
                            .stream()
                            .filter(e -> e.getExtendType().equals(1))
                            .map(PolicyFeeIncludeItemExtend::getExtendContent)
                            .findFirst()
                            .orElse(null);
                    String num = extendList
                            .stream()
                            .filter(e -> e.getExtendType().equals(2))
                            .map(PolicyFeeIncludeItemExtend::getExtendContent)
                            .findFirst()
                            .orElse(null);
                    String unit = extendList
                            .stream()
                            .filter(e -> e.getExtendType().equals(3))
                            .map(PolicyFeeIncludeItemExtend::getExtendContent)
                            .findFirst()
                            .orElse(null);
                    String type = extendList
                            .stream()
                            .filter(e -> e.getExtendType().equals(5))
                            .map(PolicyFeeIncludeItemExtend::getExtendContent)
                            .findFirst()
                            .orElse(null);
                    ClauseTypeDto.ClauseItemDto clauseItemDto = new ClauseTypeDto.ClauseItemDto();
                    clauseItemDto.setClauseItemCode(IncludeItemTypeEnums.getEnum(Integer.valueOf(type)).getCode().toString());
                    clauseItemDto.setTitle(IncludeItemTypeEnums.getEnum(Integer.valueOf(type)).getMessage());
                    clauseItemDto.setContent(project + num + unit);
                    clauseItemDto.setIconUrl(getFeeIcon(Integer.valueOf(type)));
                    typeDto.getClauseItemList().add(clauseItemDto);
                }
                //endregion
            } else if (itemType.equals(FeeIncludeTypeEnums.INCLUDE_COUNT.getType())) {
                String itemContent = value.get(0).getItemContent();
                typeDto.setItemAvailableCount(Integer.parseInt(itemContent));
            } else if (itemType.equals(FeeIncludeTypeEnums.INCLUDE_DESC.getType())) {
                String itemContent = value.get(0).getItemContent();
                typeDto.setContent(itemContent);
            } else if (itemType.equals(FeeIncludeTypeEnums.EXCLUDE.getType())) {
                List<ClauseTypeDto.ClauseItemDto> excludeList = value.stream()
                        .map(v -> {
                            ClauseTypeDto.ClauseItemDto clauseItemDto = new ClauseTypeDto.ClauseItemDto();
                            clauseItemDto.setContent(v.getItemContent());
                            return clauseItemDto;
                        })
                        .collect(Collectors.toList());
                typeDto.setClauseItemList(excludeList);
            }
            dto.getClauseList().add(typeDto);
        });
        return list;
    }

    private String getFeeIcon(Integer type) {
        String url = "";
        try {
            String settingStr = ConfigClientUtils.getOrDefault("tcscenery.java.resourcefrontend", "MpIncludeTypeUrlSetting", "");
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(settingStr)) {
                List<MpIncludeTypeUrlSettingBo> mpIncludeTypeUrlSettingBos = JsonUtils.string2GenericAbstract(settingStr, new TypeReference<List<MpIncludeTypeUrlSettingBo>>() {
                });
                if (!CollectionUtils.isEmpty(mpIncludeTypeUrlSettingBos)) {
                    List<MpIncludeTypeUrlSettingBo> selectUrlInfos = mpIncludeTypeUrlSettingBos.stream().filter(s -> s.getTypeId().equals(type)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(selectUrlInfos)) {
                        url = selectUrlInfos.get(0).getUrl();
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }

    private RefundPolicyDto refundRule(PolicyRefundRule refundRule, List<PolicyRefundRuleDetail> refundChangeRuleDetailList) {
        RefundPolicyDto refundChangePolicyDto = new RefundPolicyDto();
        refundChangePolicyDto.setRefundType(ticketEnumMapping(RefundPolicyEnum.RefundType.class, refundRule.getRefundType(), false).getValue());
        refundChangePolicyDto.setPartialRefund(ticketEnumMapping(RefundPolicyEnum.PartialRefund.class, refundRule.getPartialRefund(), false).getValue());
        refundChangePolicyDto.setOverdueRefund(refundRule.getIfOverdueAutoRefund());
        refundChangePolicyDto.setItemList(new ArrayList<>());
        List<PolicyRefundRuleDetail> refundRuleDetailList = refundChangeRuleDetailList.stream().filter(r -> r.getType() == 0).collect(Collectors.toList());
        for (PolicyRefundRuleDetail detail : refundRuleDetailList) {
            RefundPolicyDto.RefundChangePolicyItemDto itemDto = new RefundPolicyDto.RefundChangePolicyItemDto();
            if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.TravelDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.AfterTravelDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.TRAVEL_DATE.getValue());
                if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())) {
                    itemDto.setCompareDays(-detail.getDays());
                } else if (detail.getDateType().equals(RefundDateTypeEnums.AfterTravelDate.getCode())) {
                    itemDto.setCompareDays(detail.getDays());
                } else {
                    itemDto.setCompareDays(0);
                }
                itemDto.setCompareHour(detail.getTimes().getHour());
                itemDto.setCompareMinute(detail.getTimes().getMinute());

            } else if (detail.getDateType().equals(RefundDateTypeEnums.OrderDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.AfterOrderDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.CREATE_DATE.getValue());
                if (detail.getDateType().equals(RefundDateTypeEnums.OrderDate.getCode())) {
                    itemDto.setCompareDays(0);
                } else {
                    itemDto.setCompareDays(detail.getDays());
                }
                itemDto.setCompareHour(detail.getTimes().getHour());
                itemDto.setCompareMinute(detail.getTimes().getMinute());

            } else if (detail.getDateType().equals(RefundDateTypeEnums.NoLimit.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.NO_DATE_LIMIT.getValue());
            } else if (detail.getDateType().equals(RefundDateTypeEnums.SessionBeginBefore.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.SessionBeginAfter.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SESSION_BEGIN.getValue());
                itemDto.setCompareHour(detail.getMinutes() / 60);
                itemDto.setCompareMinute(detail.getMinutes() % 60);

            } else if (detail.getDateType().equals(RefundDateTypeEnums.SessionEndBefore.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.SessionEndAfter.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SESSION_END.getValue());
                itemDto.setCompareHour(detail.getMinutes() / 60);
                itemDto.setCompareMinute(detail.getMinutes() % 60);

            } else if (detail.getDateType().equals(RefundDateTypeEnums.SpecifyDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SPECIFY_DATE.getValue());
                itemDto.setSpecifyDate(detail.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            itemDto.setType(RefundPolicyEnum.DetailType.REFUND.getValue());
            //罚金类型0每票1每单
            Integer penaltyType = detail.getPenaltyType();
            //0币种1百分比
            Integer penaltyUnit = detail.getPenaltyUnit();
            //0订单支付价格1门市价
            Integer priceType = detail.getPriceType();
            if (penaltyUnit == 0 && penaltyType == 0) {
                itemDto.setCostType(RefundPolicyEnum.CostType.UNIT_CONSTANT.getValue());
            } else if (penaltyUnit == 0 && penaltyType == 1) {
                itemDto.setCostType(RefundPolicyEnum.CostType.CONSTANT.getValue());
            } else if (penaltyUnit == 1 && priceType == 0) {
                itemDto.setCostType(RefundPolicyEnum.CostType.PERCENTAGE_SALE.getValue());
            } else if (penaltyUnit == 1 && priceType == 1) {
                itemDto.setCostType(RefundPolicyEnum.CostType.PERCENTAGE_MARKET.getValue());
            }
            itemDto.setCostValue(itemDto.getCostValue());
            itemDto.setCostCurrency("CNY");
            refundChangePolicyDto.getItemList().add(itemDto);
        }
        //改
        refundChangePolicyDto.setChangeType(ticketEnumMapping(RefundPolicyEnum.ChangeType.class, refundRule.getChangeType(), false).getValue());
        List<PolicyRefundRuleDetail> changeRuleDetailList = refundChangeRuleDetailList.stream().filter(r -> r.getType() == 1).collect(Collectors.toList());
        for (PolicyRefundRuleDetail detail : changeRuleDetailList) {
            RefundPolicyDto.RefundChangePolicyItemDto itemDto = new RefundPolicyDto.RefundChangePolicyItemDto();
            itemDto.setType(RefundPolicyEnum.DetailType.CHANGE.getValue());
            itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.TRAVEL_DATE.getValue());
            if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())) {
                itemDto.setCompareDays(-detail.getDays());
            } else {
                itemDto.setCompareDays(0);
            }
            itemDto.setCompareHour(detail.getTimes().getHour());
            itemDto.setCompareMinute(detail.getTimes().getMinute());
            itemDto.setChangeDimension(ticketEnumMapping(RefundPolicyEnum.ChangeDimensionType.class, detail.getChangeDimension(), false).getValue());
            refundChangePolicyDto.getItemList().add(itemDto);
        }
        return refundChangePolicyDto;
    }

    private BookVoucherDto voucher(List<PolicyMultipleOptions> policyMultipleOptions) {
        BookVoucherDto voucherDto = new BookVoucherDto();
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o ->
                        o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.ElectronicVouchersType.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.SupportCertificate.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.MsgType.getCode()))
                .collect(Collectors.toList());
        List<String> voucherList = collect.stream()
                .map(v -> ticketEnumMapping(BookVoucherEnum.VoucherUsageMethod.class, v.getSelectValue(), false).getValue())
                .collect(Collectors.toList());
        voucherDto.setVoucherUsageMethod(voucherList);
        return voucherDto;
    }

    private UseRuleDto useRule(PolicyUseRule useRule, List<PolicyUseRuleAdress> useRuleAdressesList) {
        UseRuleDto useRuleDto = new UseRuleDto();
        useRuleDto.setEnterType(useRule.getEnterType());
        useRuleDto.setVoucherAll(useRule.getVoucherAll());
        useRuleDto.setSupplementDesc(useRule.getSupplementDesc());
        //region 地址
        List<AddressTimeDto> enterAddressTimeVoList = new ArrayList<>();
        List<AddressTimeDto> changeAddressTimeVoList = new ArrayList<>();
        if (useRule.getEnterType().equals(EnterTypeEnum.Direct.getCode())) {
            //直接入园
            handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
        } else if (useRule.getEnterType().equals(EnterTypeEnum.Change.getCode())) {
            //换票
            if (useRule.getIfSameAddress() == 1) {
                //地址一致
                handleChangeAddress(useRuleAdressesList, useRuleDto, changeAddressTimeVoList);
                useRuleDto.setIfSameAddress(1);
            } else {
                //地址不一致
                handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
                handleChangeAddress(useRuleAdressesList, useRuleDto, changeAddressTimeVoList);
                useRuleDto.setIfSameAddress(0);
            }
        } else {
            //其他
            handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
        }
        //endregion
        return useRuleDto;
    }

    private void handleChangeAddress(List<PolicyUseRuleAdress> addressList, UseRuleDto useRuleDto, List<AddressTimeDto> changeAddressTimeVoList) {
        List<PolicyUseRuleAdress> change = addressList.stream().filter(a -> a.getDataType() == 1).collect(Collectors.toList());
        packageAdressTime(changeAddressTimeVoList, change);
        useRuleDto.setChangeAddressList(changeAddressTimeVoList);
    }

    private void handleEnterAddress(List<PolicyUseRuleAdress> addressList, UseRuleDto useRuleDto, List<AddressTimeDto> enterAddressTimeVoList) {
        List<PolicyUseRuleAdress> enter = addressList.stream().filter(a -> a.getDataType() == 0).collect(Collectors.toList());
        packageAdressTime(enterAddressTimeVoList, enter);
        useRuleDto.setEnterAddressList(enterAddressTimeVoList);
    }

    private void packageAdressTime(List<AddressTimeDto> addressTimeVoList, List<PolicyUseRuleAdress> addressTimeDbList) {
        if (CollectionUtils.isEmpty(addressTimeDbList)) {
            return;
        }
        Map<Long, List<PolicyUseRuleAdress>> collect = addressTimeDbList.stream().collect(Collectors.groupingBy(PolicyUseRuleAdress::getAdressId));
        collect.forEach((key, value) -> value.sort(Comparator.comparing(PolicyUseRuleAdress::getSort)));
        PolicyMapSortUtil<PolicyUseRuleAdress> sortUtil = new PolicyMapSortUtil<>();
        collect = sortUtil.sortMapLimit(collect,
                (Comparator<Map.Entry<Long, List<PolicyUseRuleAdress>>>) (o1, o2) -> o1.getValue().get(0).getSort().compareTo(o2.getValue().get(0).getSort()));
        for (List<PolicyUseRuleAdress> times : collect.values()) {
            //同一个地址，只是时间不同
            PolicyUseRuleAdress address = times.get(0);
            AddressTimeDto addressTime = new AddressTimeDto();
            addressTimeVoList.add(addressTime);
            addressTime.setAddress(address.getAdress());
            String addressType = AddressTypeEnums.getName(address.getAdressType());
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(addressType) && !org.apache.commons.lang3.StringUtils.equals("无", addressType)) {
                addressTime.setAddressType(addressType);
            }
            addressTime.setTimeList(new ArrayList<>());
            for (PolicyUseRuleAdress time : times) {
                AddressTimeDto.Time timeVo = new AddressTimeDto.Time();
                String startTime = time.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                String endTime = time.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                if (!startTime.equals("00:00") || !endTime.equals("00:00")) {
                    String startEndTime = startTime
                            + "-"
                            + endTime;
                    timeVo.setStartEndTime(startEndTime);
                }
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(time.getDescc())) {
                    timeVo.setDesc(time.getDescc());
                }
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(timeVo.getStartEndTime()) || org.apache.commons.lang3.StringUtils.isNotEmpty(timeVo.getDesc())) {
                    addressTime.getTimeList().add(timeVo);
                }
            }
        }
    }

    private List<ResourceBandInfoDto> bandInfoList(List<PolicyTravellerCrowdInfo> crowdInfoList, List<PolicyTravellerCrowdLimit> travellerCrowdLimitList, PolicyTravellerBase travellerBase) throws Exception {
        List<ResourceBandInfoDto> list = new ArrayList<>();
        for (PolicyTravellerCrowdInfo crowd : crowdInfoList) {
            ResourceBandInfoDto band = new ResourceBandInfoDto();
            band.setBandCode(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getValue());
            band.setBandTitle(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getName());
            band.setPersonQuantity(crowd.getNum());
            Integer needAllCrowd = travellerBase.getNeedAllCrowd();
            band.setPassengerRequiredType(needAllCrowd == 0 ?
                    ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ALL.getValue()
                    : ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ONE.getValue());
            band.setBandLimitRuleList(getBandLimitList(travellerCrowdLimitList));
            list.add(band);
        }
        return list;
    }

    private List<ResourceBandInfoDto.BandLimitRuleDto> getBandLimitList(List<PolicyTravellerCrowdLimit> travellerCrowdLimitList) throws Exception {
        List<ResourceBandInfoDto.BandLimitRuleDto> list = new ArrayList<>();
        Map<Long, List<PolicyTravellerCrowdLimit>> map = travellerCrowdLimitList.stream().collect(Collectors.groupingBy(PolicyTravellerCrowdLimit::getGroupId));
        for (List<PolicyTravellerCrowdLimit> value : map.values()) {
            ResourceBandInfoDto.BandLimitRuleDto bandLimitRuleDto = new ResourceBandInfoDto.BandLimitRuleDto();
            for (PolicyTravellerCrowdLimit limit : value) {
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Age.getCode())) {
                    //region年龄
                    ResourceBandInfoDto.AgeLimitRuleDto ageLimitRuleDto = new ResourceBandInfoDto.AgeLimitRuleDto();
                    ageLimitRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    ageLimitRuleDto.setAgeCalcBirthdayDimension(ticketEnumMapping(ResourceBandInfoEnum.BirthdayDimension.class, limit.getDimensionType(), false).getValue());
                    ageLimitRuleDto.setAgeCalcCompareDimension("");
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    for (LimitCondition limitCondition : limitConditions) {
                        String contentFirst = limitCondition.getContentFirst();
                        String contentSecond = limitCondition.getContentSecond();
                        String contentFirstLimit = limitCondition.getContentFirstLimit();
                        String contentSecondLimit = limitCondition.getContentSecondLimit();
                        ResourceBandInfoDto.AgeRangeDto ageRangeDto = new ResourceBandInfoDto.AgeRangeDto(
                                StringUtils.isNotEmpty(contentFirst) ? Integer.valueOf(contentFirst) : null,
                                StringUtils.isNotEmpty(contentFirstLimit) ? Integer.valueOf(contentFirstLimit) : null,
                                StringUtils.isNotEmpty(contentSecond) ? Integer.valueOf(contentSecond) : null,
                                StringUtils.isNotEmpty(contentSecondLimit) ? Integer.valueOf(contentSecondLimit) : null
                        );
                        ageLimitRuleDto.getAgeRangeList().add(ageRangeDto);
                    }
                    bandLimitRuleDto.getAgeLimitRuleList().add(ageLimitRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Gender.getCode())) {
                    //region性别
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        String contentFirst = limitConditions.get(0).getContentFirst();
                        if (StringUtils.isNotEmpty(contentFirst)) {
                            ResourceBandInfoDto.GenderLimitRuleDto genderLimitRuleDto = new ResourceBandInfoDto.GenderLimitRuleDto(
                                    1,
                                    ticketEnumMapping(GenderEnum.class, Integer.valueOf(contentFirst), false).getValue()
                            );
                            bandLimitRuleDto.getGenderLimitRuleList().add(genderLimitRuleDto);
                        }
                    }
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.SpecialArea.getCode())) {
                    //region 特殊区域
                    ResourceBandInfoDto.AreaLimitRuleDto areaRuleDto = new ResourceBandInfoDto.AreaLimitRuleDto();
                    areaRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    areaRuleDto.setAreaDimension(limit.getDimensionType().toString());
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> cityIds = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    areaRuleDto.setValueList(cityIds);
                    bandLimitRuleDto.getAreaLimitRuleList().add(areaRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Birthday.getCode())) {
                    //region 出生日期
                    ResourceBandInfoDto.BirthdayLimitRuleDto birthRuleDto = new ResourceBandInfoDto.BirthdayLimitRuleDto();
                    birthRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    birthRuleDto.setDayDimension(getDimension(limit.getDimensionType()));
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    birthRuleDto.setValueList(values);
                    bandLimitRuleDto.getBirthdayLimitRuleList().add(birthRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.ChineseZodiac.getCode())) {
                    //region 生肖
                    ResourceBandInfoDto.ZodiacLimitRuleDto zodiacRuleDto = new ResourceBandInfoDto.ZodiacLimitRuleDto();
                    zodiacRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .map(s -> ZodiacEnum.getValue(Integer.valueOf(s)))
                            .collect(Collectors.toList());
                    zodiacRuleDto.setZodiacTypeList(values);
                    bandLimitRuleDto.getZodiacLimitRuleList().add(zodiacRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Name.getCode())) {
                    //region 姓名
                    ResourceBandInfoDto.NameLimitRuleDto nameRuleDto = new ResourceBandInfoDto.NameLimitRuleDto();
                    nameRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .collect(Collectors.toList());
                    nameRuleDto.setValueList(values);
                    bandLimitRuleDto.getNameLimitRuleList().add(nameRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Student.getCode())) {
                    //region 学生
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        LimitCondition limitCondition = limitConditions.get(0);
                        Integer code = Integer.valueOf(limitCondition.getContentFirst());
                        ResourceBandInfoDto.IdentityAuthLimitRuleDto studentRuleDto = new ResourceBandInfoDto.IdentityAuthLimitRuleDto();
                        studentRuleDto.setCanBook(1);
                        studentRuleDto.setIdentityType(ResourceBandInfoEnum.IdentityType.getValue(code));
                        bandLimitRuleDto.getIdentityAuthLimitRuleDtoList().add(studentRuleDto);
                    }
                    //endregion
                }
            }
            list.add(bandLimitRuleDto);
        }
        return list;
    }

    private String getDimension(Integer dimensionType) {
        if (dimensionType.equals(BirthDateDimensionEnums.BirthdayYear.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.YEAR.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayMonth.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.MONTH.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayDay.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.DAY.getValue();
        }
        return "";
    }

    private List<BookPassengerQuestionDto> travellerInfo(List<PolicyMultipleOptions> policyMultipleOptions, PolicyTravellerBase travellerBase) {
        List<BookPassengerQuestionDto> list = new ArrayList<>();
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o ->
                        o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            for (PolicyMultipleOptions option : collect) {
                BookPassengerQuestionDto optionDto = new BookPassengerQuestionDto();
                optionDto.setQuestionType(getOptionType(option.getSelectTypeId()));
                optionDto.setPassengerRequiredType(ticketEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, travellerBase.getNeedAllInfo(), false).getValue());
                optionDto.setQuestionCode(getQuestionCode(option.getSelectValue()));
                optionDto.setDataType(ResourceBookPassengerInfoEnum.DataType.MULTIPLE_ENUM.getName());
                list.add(optionDto);
            }
        }
        return list;
    }

    private String getQuestionCode(Integer selectValue) {
        if (selectValue.equals(TravellerIdCardTypeEnums.Id_Card.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_ID_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.Passport.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PASSPORT.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.GanAo.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_HM_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.TaiWan.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_TW_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.HuiXiang.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_BACK_HOME_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.YongJu.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PERMANENT_RESIDENCE_CARD_FOREIGNERS.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.NAME.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_CHINA_NAME.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.CHINA_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.FOREIGN_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE_INTERNATIONAL.getValue();
        }
        return "";
    }


    private String getOptionType(Integer selectTypeId) {
        if (selectTypeId.equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.CREDENTIALS.getValue();
        } else if (selectTypeId.equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.BASIC.getValue();
        }
        return null;
    }

    private BookContactInfoDto travellerContact(List<PolicyMultipleOptions> policyMultipleOptions) {
        BookContactInfoDto bookContactInfoDto = new BookContactInfoDto();
        bookContactInfoDto.setNameRequired(0);
        bookContactInfoDto.setMobileRequired(0);
        bookContactInfoDto.setOverseaMobileRequired(0);
        bookContactInfoDto.setEmailRequired(0);
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o -> o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.ContactUserInfo.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.EMAIL.getCode()))) {
                bookContactInfoDto.setEmailRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.MAINLAND.getCode()))) {
                bookContactInfoDto.setMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NOT_MAINLAND.getCode()))) {
                bookContactInfoDto.setOverseaMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NAME.getCode()))) {
                bookContactInfoDto.setNameRequired(1);
            }
        }
        return bookContactInfoDto;
    }

    private BookLimitDto orderLimit(PolicyOrderLimit orderLimit, List<PolicyOrderLimitRule> orderLimitRuleList) {
        BookLimitDto bookLimitDto = new BookLimitDto();
        bookLimitDto.setNeedAdvance(WhetherEnum.YES.getValue());
        bookLimitDto.setAdvanceBookDays(orderLimit.getInAdvanceDays());
        bookLimitDto.setAdvanceBookHour(orderLimit.getInAdvanceTime().getHour());
        bookLimitDto.setAdvanceBookMinute(orderLimit.getInAdvanceTime().getMinute());
        bookLimitDto.setAdvanceBookDescription("");
        bookLimitDto.setBookEffectMinutes(new BigDecimal(orderLimit.getInAdvanceHours() * 60 + orderLimit.getInAdvanceMinutes()));
        bookLimitDto.setPayTimeoutMinutes(orderLimit.getPayLimitMinute());
        bookLimitDto.setBookDaysFromCurrent(orderLimit.getOrderDaysLimit());
        bookLimitDto.setBookMinQuantity(orderLimit.getMinBuyNum());
        bookLimitDto.setBookMaxQuantity(orderLimit.getMaxBuyNum());
        List<BookQuotaLimitDto> limitRuleList = new ArrayList<>();
        orderLimitRuleList.forEach(rule -> {
            BookQuotaLimitDto ruleDto = new BookQuotaLimitDto();
            ruleDto.setGroupId(rule.getGroupId().toString());
            ruleDto.setLimitTargetType(ticketEnumMapping(BookQuotaLimitEnum.LimitTargetType.class, rule.getIdType(), false).getValue());
            ruleDto.setLimitTargetDesc("");
            ruleDto.setLimitDateType(rule.getDateType() == 0 ? BookQuotaLimitEnum.LimitDateType.USAGE_DATE.getValue() : BookQuotaLimitEnum.LimitDateType.CREATE_DATE.getValue());
            ruleDto.setLimitPeriodDays(rule.getDays().toString());
            ruleDto.setQuotaQuantity(rule.getNum().toString());
            ruleDto.setQuotaUnit(UnitEnums.getV(rule.getUnit()));
            limitRuleList.add(ruleDto);
        });
        bookLimitDto.setBookQuotaLimitList(limitRuleList);
        return bookLimitDto;
    }


    private BookModeDto orderMode(PolicyOrderMode orderMode, PolicyBaseInfo policyBaseInfo, List<PrecastTicketManage> precastTicketManages) {
        Integer ifAppointDate = orderMode.getIfAppointDate();
        Integer usePeriodType = orderMode.getUsePeriodType();
        Integer validDays = orderMode.getValidDays();
        Integer validDaysUnit = orderMode.getValidDaysUnit();
        LocalDateTime validityPeriodStart = orderMode.getValidityPeriodStart();
        LocalDateTime validityPeriodEnd = orderMode.getValidityPeriodEnd();
        Integer enterLimitType = orderMode.getEnterLimitType();
        BookModeDto bookModeDto = new BookModeDto();
        if (ifAppointDate == 1) {
            //指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getTicketValue())) {
                //当日
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_SINGLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getValue());
            } else {
                //指定日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_MULTIPLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_SPECIFY_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        } else {
            //无需指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getTicketValue())) {
                //指定有效期
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getValue());
                bookModeDto.setValidityPeriodStart(validityPeriodStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                bookModeDto.setValidityPeriodEnd(validityPeriodEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            } else {
                //下单日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_ORDER_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        }
        bookModeDto.setBeyondValidityPeriodCanUse(enterLimitType == 2 ? 1 : 0);
        //bookModeDto.setPolicyMode(ticketEnumMapping(BookModeEnum.PolicyMode.class, policyBaseInfo.getPolicyMode(), false).getValue());
        preCaseInfo(bookModeDto, orderMode, policyBaseInfo, precastTicketManages);
        bookModeDto.setUseLimitType(orderMode.getUsePeriodType());
        bookModeDto.setUseLimitWeekList(orderMode.getDateLimitWeek());
        bookModeDto.setUseLimitDateList(orderMode.getDateLimitDate());
        bookModeDto.setUseLimitTimes(orderMode.getUseLimitTimes());
        return bookModeDto;
    }

    private void preCaseInfo(BookModeDto bookModeDto, PolicyOrderMode orderMode, PolicyBaseInfo policyBaseInfo, List<PrecastTicketManage> precastTicketManages) {
        if (policyBaseInfo.getPolicyMode().compareTo(BookModeEnum.PolicyMode.Combined_Ticket.getTicketValue()) == 0 ||
                policyBaseInfo.getPolicyMode().compareTo(BookModeEnum.PolicyMode.Kill_Ticket.getTicketValue()) == 0 ||
                policyBaseInfo.getPayType().compareTo(0) == 0 ||
                orderMode.getIfAppointDate().compareTo(0) == 0) {
            return;
        }
        PrecastTicketManage findResource = precastTicketManages.stream().filter(t -> t.getDimensionType() == 2).findFirst().orElse(null);
        if (findResource != null) {
            bookModeDto.setPrecastBook(1);
            bookModeDto.setPrecastBookDays(findResource.getAppendSoldDays());
            return;
        }
        PrecastTicketManage policy = precastTicketManages.stream()
                .filter(t -> t.getDimensionType() == 4 && t.getPolicyId().intValue() == policyBaseInfo.getPolicyOriginalId())
                .findFirst()
                .orElse(null);
        if (policy != null) {
            bookModeDto.setPrecastBook(1);
            bookModeDto.setPrecastBookDays(policy.getAppendSoldDays());
        }
    }

    public static <T> QueryWrapper<T> of(Class<T> entityClass, Long policyId, Long resourceId) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return wrapper.eq("policy_id", policyId)
                .eq("resource_id", resourceId)
                .eq("row_status", 1);
    }
}
