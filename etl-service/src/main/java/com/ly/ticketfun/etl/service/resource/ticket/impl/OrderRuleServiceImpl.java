package com.ly.ticketfun.etl.service.resource.ticket.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.base.GenderEnum;
import com.ly.ticketfun.etl.common.enums.base.ZodiacEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.*;
import com.ly.ticketfun.etl.common.enums.ticket.*;
import com.ly.ticketfun.etl.common.utils.PolicyMapSortUtil;
import com.ly.ticketfun.etl.dataService.tczbyresource.*;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.IPrecastTicketManageService;
import com.ly.ticketfun.etl.domain.bo.LimitCondition;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.PrecastTicketManage;
import com.ly.ticketfun.etl.domain.templateResource.dto.*;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.TRTransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.ticket.IOrderRuleService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OrderRuleServiceImpl extends TRTransformBaseServiceImpl implements IOrderRuleService {

    @Resource
    private IPolicyOrderModeService policyOrderModeService;
    @Resource
    private IPolicyOrderLimitService policyOrderLimitService;
    @Resource
    private IPolicyOrderLimitRuleService policyOrderLimitRuleService;
    @Resource
    private IPolicyTravellerBaseService policyTravellerBaseService;
    @Resource
    private IPolicyTravellerCrowdInfoService policyTravellerCrowdInfoService;
    @Resource
    private IPolicyTravellerCrowdLimitService policyTravellerCrowdLimitService;
    @Resource
    private IPolicyUseRuleService policyUseRuleService;
    @Resource
    private IPolicyUseRuleAdressService policyUseRuleAddressService;
    @Resource
    private IPolicyRefundRuleService policyRefundRuleService;
    @Resource
    private IPolicyRefundRuleDetailService policyRefundRuleDetailService;
    @Resource
    private IPolicyMultipleOptionsService policyMultipleOptionsService;
    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;
    @Resource
    private IPrecastTicketManageService precastTicketManageService;

    @Override
    public void toTemplateResource(Long resourceId, Long policyId) throws Exception {
        QueryWrapper<PolicyOrderMode> orderModeQueryWrapper = of(PolicyOrderMode.class, policyId, resourceId);
        PolicyOrderMode orderMode = policyOrderModeService.queryOne(orderModeQueryWrapper);

        QueryWrapper<PolicyOrderLimit> orderLimitQueryWrapper = of(PolicyOrderLimit.class, policyId, resourceId);
        PolicyOrderLimit orderLimit = policyOrderLimitService.queryOne(orderLimitQueryWrapper);

        QueryWrapper<PolicyOrderLimitRule> orderLimitRuleQueryWrapper = of(PolicyOrderLimitRule.class, policyId, resourceId);
        List<PolicyOrderLimitRule> orderLimitRuleList = policyOrderLimitRuleService.queryList(orderLimitRuleQueryWrapper);

        QueryWrapper<PolicyTravellerBase> travelBaseQueryWrapper = of(PolicyTravellerBase.class, policyId, resourceId);
        PolicyTravellerBase travellerBase = policyTravellerBaseService.queryOne(travelBaseQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdInfo> travelCrowdInfoQueryWrapper = of(PolicyTravellerCrowdInfo.class, policyId, resourceId);
        List<PolicyTravellerCrowdInfo> crowdInfoList = policyTravellerCrowdInfoService.queryList(travelCrowdInfoQueryWrapper);

        QueryWrapper<PolicyTravellerCrowdLimit> travelCrowdLimitQueryWrapper = of(PolicyTravellerCrowdLimit.class, policyId, resourceId);
        List<PolicyTravellerCrowdLimit> travellerCrowdLimitList = policyTravellerCrowdLimitService.queryList(travelCrowdLimitQueryWrapper);

        QueryWrapper<PolicyUseRule> useRuleQueryWrapper = of(PolicyUseRule.class, policyId, resourceId);
        PolicyUseRule useRule = policyUseRuleService.queryOne(useRuleQueryWrapper);

        QueryWrapper<PolicyUseRuleAdress> useRuleAddressQueryWrapper = of(PolicyUseRuleAdress.class, policyId, resourceId);
        List<PolicyUseRuleAdress> useRuleAdressesList = policyUseRuleAddressService.queryList(useRuleAddressQueryWrapper);

        QueryWrapper<PolicyRefundRule> refundRuleQueryWrapper = of(PolicyRefundRule.class, policyId, resourceId);
        PolicyRefundRule refundRule = policyRefundRuleService.queryOne(refundRuleQueryWrapper);

        QueryWrapper<PolicyRefundRuleDetail> refundRuleDetailQueryWrapper = of(PolicyRefundRuleDetail.class, policyId, resourceId);
        List<PolicyRefundRuleDetail> refundChangeRuleDetailList = policyRefundRuleDetailService.queryList(refundRuleDetailQueryWrapper);

        QueryWrapper<PolicyMultipleOptions> multipleOptionsQueryWrapper = of(PolicyMultipleOptions.class, policyId, resourceId);
        List<PolicyMultipleOptions> policyMultipleOptions = policyMultipleOptionsService.queryList(multipleOptionsQueryWrapper);

        QueryWrapper<PolicyBaseInfo> policyQueryWrapper = of(PolicyBaseInfo.class, policyId, resourceId);
        PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryOne(policyQueryWrapper);

        LambdaQueryWrapper<PrecastTicketManage> PrecastTicketManageQueryWrapper = new LambdaQueryWrapper<PrecastTicketManage>()
                .eq(PrecastTicketManage::getResourceId, resourceId)
                .eq(PrecastTicketManage::getIgnoreStatus, 1)
                .eq(PrecastTicketManage::getRowStatus, 1);
        List<PrecastTicketManage> precastTicketManages = precastTicketManageService.queryList(PrecastTicketManageQueryWrapper);


        BookModeDto bookModeDto = orderMode(orderMode, policyBaseInfo, precastTicketManages);
        BookLimitDto bookLimitDto = orderLimit(orderLimit, orderLimitRuleList);
        //游玩人信息+是否需要游玩人信息
        List<BookPassengerQuestionDto> bookPassengerQuestionList = travellerInfo(policyMultipleOptions, travellerBase);
        //适用人群
        List<ResourceBandInfoDto> bandInfoList = bandInfoList(crowdInfoList, travellerCrowdLimitList, travellerBase);
        //购票人信息
        BookContactInfoDto bookContactInfoDto = travellerContact(policyMultipleOptions);
        //核销方式
        UseRuleDto useRuleDto = useRule(useRule, useRuleAdressesList);
        BookVoucherDto voucherDto = voucher(policyMultipleOptions);

        RefundPolicyDto refundPolicyDto = refundRule(refundRule, refundChangeRuleDetailList);

    }

    private RefundPolicyDto refundRule(PolicyRefundRule refundRule, List<PolicyRefundRuleDetail> refundChangeRuleDetailList) {
        RefundPolicyDto refundChangePolicyDto = new RefundPolicyDto();
        refundChangePolicyDto.setRefundType(ticketEnumMapping(RefundPolicyEnum.RefundType.class, refundRule.getRefundType(), false).getValue());
        refundChangePolicyDto.setPartialRefund(ticketEnumMapping(RefundPolicyEnum.PartialRefund.class, refundRule.getPartialRefund(), false).getValue());
        refundChangePolicyDto.setOverdueRefund(refundRule.getIfOverdueAutoRefund());
        refundChangePolicyDto.setItemList(new ArrayList<>());
        List<PolicyRefundRuleDetail> refundRuleDetailList = refundChangeRuleDetailList.stream().filter(r -> r.getType() == 0).collect(Collectors.toList());
        for (PolicyRefundRuleDetail detail : refundRuleDetailList) {
            RefundPolicyDto.RefundChangePolicyItemDto itemDto = new RefundPolicyDto.RefundChangePolicyItemDto();
            if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.TravelDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.AfterTravelDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.TRAVEL_DATE.getValue());
                if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())) {
                    itemDto.setCompareDays(-detail.getDays());
                } else if (detail.getDateType().equals(RefundDateTypeEnums.AfterTravelDate.getCode())) {
                    itemDto.setCompareDays(detail.getDays());
                } else {
                    itemDto.setCompareDays(0);
                }
                itemDto.setCompareHour(detail.getTimes().getHour());
                itemDto.setCompareMinute(detail.getTimes().getMinute());

            } else if (detail.getDateType().equals(RefundDateTypeEnums.OrderDate.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.AfterOrderDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.CREATE_DATE.getValue());
                if (detail.getDateType().equals(RefundDateTypeEnums.OrderDate.getCode())) {
                    itemDto.setCompareDays(0);
                } else {
                    itemDto.setCompareDays(detail.getDays());
                }
                itemDto.setCompareHour(detail.getTimes().getHour());
                itemDto.setCompareMinute(detail.getTimes().getMinute());

            } else if (detail.getDateType().equals(RefundDateTypeEnums.NoLimit.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.NO_DATE_LIMIT.getValue());
            } else if (detail.getDateType().equals(RefundDateTypeEnums.SessionBeginBefore.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.SessionBeginAfter.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SESSION_BEGIN.getValue());
                itemDto.setCompareHour(detail.getMinutes() / 60);
                itemDto.setCompareMinute(detail.getMinutes() % 60);

            } else if (detail.getDateType().equals(RefundDateTypeEnums.SessionEndBefore.getCode())
                    || detail.getDateType().equals(RefundDateTypeEnums.SessionEndAfter.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SESSION_END.getValue());
                itemDto.setCompareHour(detail.getMinutes() / 60);
                itemDto.setCompareMinute(detail.getMinutes() % 60);

            } else if (detail.getDateType().equals(RefundDateTypeEnums.SpecifyDate.getCode())) {
                itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.SPECIFY_DATE.getValue());
                itemDto.setSpecifyDate(detail.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            itemDto.setType(RefundPolicyEnum.DetailType.REFUND.getValue());
            //罚金类型0每票1每单
            Integer penaltyType = detail.getPenaltyType();
            //0币种1百分比
            Integer penaltyUnit = detail.getPenaltyUnit();
            //0订单支付价格1门市价
            Integer priceType = detail.getPriceType();
            if (penaltyUnit == 0 && penaltyType == 0) {
                itemDto.setCostType(RefundPolicyEnum.CostType.UNIT_CONSTANT.getValue());
            } else if (penaltyUnit == 0 && penaltyType == 1) {
                itemDto.setCostType(RefundPolicyEnum.CostType.CONSTANT.getValue());
            } else if (penaltyUnit == 1 && priceType == 0) {
                itemDto.setCostType(RefundPolicyEnum.CostType.PERCENTAGE_SALE.getValue());
            } else if (penaltyUnit == 1 && priceType == 1) {
                itemDto.setCostType(RefundPolicyEnum.CostType.PERCENTAGE_MARKET.getValue());
            }
            itemDto.setCostValue(itemDto.getCostValue());
            itemDto.setCostCurrency("CNY");
            refundChangePolicyDto.getItemList().add(itemDto);
        }
        //改
        refundChangePolicyDto.setChangeType(ticketEnumMapping(RefundPolicyEnum.ChangeType.class, refundRule.getChangeType(), false).getValue());
        List<PolicyRefundRuleDetail> changeRuleDetailList = refundChangeRuleDetailList.stream().filter(r -> r.getType() == 1).collect(Collectors.toList());
        for (PolicyRefundRuleDetail detail : changeRuleDetailList) {
            RefundPolicyDto.RefundChangePolicyItemDto itemDto = new RefundPolicyDto.RefundChangePolicyItemDto();
            itemDto.setType(RefundPolicyEnum.DetailType.CHANGE.getValue());
            itemDto.setCompareTimeType(RefundPolicyEnum.CompareTimeType.TRAVEL_DATE.getValue());
            if (detail.getDateType().equals(RefundDateTypeEnums.BeforeTravelDate.getCode())) {
                itemDto.setCompareDays(-detail.getDays());
            } else {
                itemDto.setCompareDays(0);
            }
            itemDto.setCompareHour(detail.getTimes().getHour());
            itemDto.setCompareMinute(detail.getTimes().getMinute());
            itemDto.setChangeDimension(ticketEnumMapping(RefundPolicyEnum.ChangeDimensionType.class, detail.getChangeDimension(), false).getValue());
            refundChangePolicyDto.getItemList().add(itemDto);
        }
        return refundChangePolicyDto;
    }

    private BookVoucherDto voucher(List<PolicyMultipleOptions> policyMultipleOptions) {
        BookVoucherDto voucherDto = new BookVoucherDto();
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o ->
                        o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.ElectronicVouchersType.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.SupportCertificate.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.MsgType.getCode()))
                .collect(Collectors.toList());
        List<String> voucherList = collect.stream()
                .map(v -> ticketEnumMapping(BookVoucherEnum.VoucherUsageMethod.class, v.getSelectValue(), false).getValue())
                .collect(Collectors.toList());
        voucherDto.setVoucherUsageMethod(voucherList);
        return voucherDto;
    }

    private UseRuleDto useRule(PolicyUseRule useRule, List<PolicyUseRuleAdress> useRuleAdressesList) {
        UseRuleDto useRuleDto = new UseRuleDto();
        useRuleDto.setEnterType(useRule.getEnterType());
        useRuleDto.setVoucherAll(useRule.getVoucherAll());
        useRuleDto.setSupplementDesc(useRule.getSupplementDesc());
        //region 地址
        List<AddressTimeDto> enterAddressTimeVoList = new ArrayList<>();
        List<AddressTimeDto> changeAddressTimeVoList = new ArrayList<>();
        if (useRule.getEnterType().equals(EnterTypeEnum.Direct.getCode())) {
            //直接入园
            handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
        } else if (useRule.getEnterType().equals(EnterTypeEnum.Change.getCode())) {
            //换票
            if (useRule.getIfSameAddress() == 1) {
                //地址一致
                handleChangeAddress(useRuleAdressesList, useRuleDto, changeAddressTimeVoList);
                useRuleDto.setIfSameAddress(1);
            } else {
                //地址不一致
                handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
                handleChangeAddress(useRuleAdressesList, useRuleDto, changeAddressTimeVoList);
                useRuleDto.setIfSameAddress(0);
            }
        } else {
            //其他
            handleEnterAddress(useRuleAdressesList, useRuleDto, enterAddressTimeVoList);
        }
        //endregion
        return useRuleDto;
    }

    private void handleChangeAddress(List<PolicyUseRuleAdress> addressList, UseRuleDto useRuleDto, List<AddressTimeDto> changeAddressTimeVoList) {
        List<PolicyUseRuleAdress> change = addressList.stream().filter(a -> a.getDataType() == 1).collect(Collectors.toList());
        packageAdressTime(changeAddressTimeVoList, change);
        useRuleDto.setChangeAddressList(changeAddressTimeVoList);
    }

    private void handleEnterAddress(List<PolicyUseRuleAdress> addressList, UseRuleDto useRuleDto, List<AddressTimeDto> enterAddressTimeVoList) {
        List<PolicyUseRuleAdress> enter = addressList.stream().filter(a -> a.getDataType() == 0).collect(Collectors.toList());
        packageAdressTime(enterAddressTimeVoList, enter);
        useRuleDto.setEnterAddressList(enterAddressTimeVoList);
    }

    private void packageAdressTime(List<AddressTimeDto> addressTimeVoList, List<PolicyUseRuleAdress> addressTimeDbList) {
        if (CollectionUtils.isEmpty(addressTimeDbList)) {
            return;
        }
        Map<Long, List<PolicyUseRuleAdress>> collect = addressTimeDbList.stream().collect(Collectors.groupingBy(PolicyUseRuleAdress::getAdressId));
        collect.forEach((key, value) -> value.sort(Comparator.comparing(PolicyUseRuleAdress::getSort)));
        PolicyMapSortUtil<PolicyUseRuleAdress> sortUtil = new PolicyMapSortUtil<>();
        collect = sortUtil.sortMapLimit(collect,
                (Comparator<Map.Entry<Long, List<PolicyUseRuleAdress>>>) (o1, o2) -> o1.getValue().get(0).getSort().compareTo(o2.getValue().get(0).getSort()));
        for (List<PolicyUseRuleAdress> times : collect.values()) {
            //同一个地址，只是时间不同
            PolicyUseRuleAdress address = times.get(0);
            AddressTimeDto addressTime = new AddressTimeDto();
            addressTimeVoList.add(addressTime);
            addressTime.setAddress(address.getAdress());
            String addressType = AddressTypeEnums.getName(address.getAdressType());
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(addressType) && !org.apache.commons.lang3.StringUtils.equals("无", addressType)) {
                addressTime.setAddressType(addressType);
            }
            addressTime.setTimeList(new ArrayList<>());
            for (PolicyUseRuleAdress time : times) {
                AddressTimeDto.Time timeVo = new AddressTimeDto.Time();
                String startTime = time.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                String endTime = time.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm"));
                if (!startTime.equals("00:00") || !endTime.equals("00:00")) {
                    String startEndTime = startTime
                            + "-"
                            + endTime;
                    timeVo.setStartEndTime(startEndTime);
                }
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(time.getDescc())) {
                    timeVo.setDesc(time.getDescc());
                }
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(timeVo.getStartEndTime()) || org.apache.commons.lang3.StringUtils.isNotEmpty(timeVo.getDesc())) {
                    addressTime.getTimeList().add(timeVo);
                }
            }
        }
    }

    private List<ResourceBandInfoDto> bandInfoList(List<PolicyTravellerCrowdInfo> crowdInfoList, List<PolicyTravellerCrowdLimit> travellerCrowdLimitList, PolicyTravellerBase travellerBase) throws Exception {
        List<ResourceBandInfoDto> list = new ArrayList<>();
        for (PolicyTravellerCrowdInfo crowd : crowdInfoList) {
            ResourceBandInfoDto band = new ResourceBandInfoDto();
            band.setBandCode(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getValue());
            band.setBandTitle(ticketEnumMapping(ResourceBandInfoEnum.BandCode.class, crowd.getCrowdType().toString(), false).getName());
            band.setPersonQuantity(crowd.getNum());
            Integer needAllCrowd = travellerBase.getNeedAllCrowd();
            band.setPassengerRequiredType(needAllCrowd == 0 ?
                    ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ALL.getValue()
                    : ResourceBookPassengerInfoEnum.PassengerRequiredType.REQUIRED_ONE.getValue());
            band.setBandLimitRuleList(getBandLimitList(travellerCrowdLimitList));
            list.add(band);
        }
        return list;
    }

    private List<ResourceBandInfoDto.BandLimitRuleDto> getBandLimitList(List<PolicyTravellerCrowdLimit> travellerCrowdLimitList) throws Exception {
        List<ResourceBandInfoDto.BandLimitRuleDto> list = new ArrayList<>();
        Map<Long, List<PolicyTravellerCrowdLimit>> map = travellerCrowdLimitList.stream().collect(Collectors.groupingBy(PolicyTravellerCrowdLimit::getGroupId));
        for (List<PolicyTravellerCrowdLimit> value : map.values()) {
            ResourceBandInfoDto.BandLimitRuleDto bandLimitRuleDto = new ResourceBandInfoDto.BandLimitRuleDto();
            for (PolicyTravellerCrowdLimit limit : value) {
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Age.getCode())) {
                    //region年龄
                    ResourceBandInfoDto.AgeLimitRuleDto ageLimitRuleDto = new ResourceBandInfoDto.AgeLimitRuleDto();
                    ageLimitRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    ageLimitRuleDto.setAgeCalcBirthdayDimension(ticketEnumMapping(ResourceBandInfoEnum.BirthdayDimension.class, limit.getDimensionType(), false).getValue());
                    ageLimitRuleDto.setAgeCalcCompareDimension("");
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    for (LimitCondition limitCondition : limitConditions) {
                        String contentFirst = limitCondition.getContentFirst();
                        String contentSecond = limitCondition.getContentSecond();
                        String contentFirstLimit = limitCondition.getContentFirstLimit();
                        String contentSecondLimit = limitCondition.getContentSecondLimit();
                        ResourceBandInfoDto.AgeRangeDto ageRangeDto = new ResourceBandInfoDto.AgeRangeDto(
                                StringUtils.isNotEmpty(contentFirst) ? Integer.valueOf(contentFirst) : null,
                                StringUtils.isNotEmpty(contentFirstLimit) ? Integer.valueOf(contentFirstLimit) : null,
                                StringUtils.isNotEmpty(contentSecond) ? Integer.valueOf(contentSecond) : null,
                                StringUtils.isNotEmpty(contentSecondLimit) ? Integer.valueOf(contentSecondLimit) : null
                        );
                        ageLimitRuleDto.getAgeRangeList().add(ageRangeDto);
                    }
                    bandLimitRuleDto.getAgeLimitRuleList().add(ageLimitRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Gender.getCode())) {
                    //region性别
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        String contentFirst = limitConditions.get(0).getContentFirst();
                        if (StringUtils.isNotEmpty(contentFirst)) {
                            ResourceBandInfoDto.GenderLimitRuleDto genderLimitRuleDto = new ResourceBandInfoDto.GenderLimitRuleDto(
                                    1,
                                    ticketEnumMapping(GenderEnum.class, Integer.valueOf(contentFirst), false).getValue()
                            );
                            bandLimitRuleDto.getGenderLimitRuleList().add(genderLimitRuleDto);
                        }
                    }
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.SpecialArea.getCode())) {
                    //region 特殊区域
                    ResourceBandInfoDto.AreaLimitRuleDto areaRuleDto = new ResourceBandInfoDto.AreaLimitRuleDto();
                    areaRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    areaRuleDto.setAreaDimension(limit.getDimensionType().toString());
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> cityIds = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    areaRuleDto.setValueList(cityIds);
                    bandLimitRuleDto.getAreaLimitRuleList().add(areaRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Birthday.getCode())) {
                    //region 出生日期
                    ResourceBandInfoDto.BirthdayLimitRuleDto birthRuleDto = new ResourceBandInfoDto.BirthdayLimitRuleDto();
                    birthRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    birthRuleDto.setDayDimension(getDimension(limit.getDimensionType()));
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream().map(LimitCondition::getContentFirst).collect(Collectors.toList());
                    birthRuleDto.setValueList(values);
                    bandLimitRuleDto.getBirthdayLimitRuleList().add(birthRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.ChineseZodiac.getCode())) {
                    //region 生肖
                    ResourceBandInfoDto.ZodiacLimitRuleDto zodiacRuleDto = new ResourceBandInfoDto.ZodiacLimitRuleDto();
                    zodiacRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .map(s -> ZodiacEnum.getValue(Integer.valueOf(s)))
                            .collect(Collectors.toList());
                    zodiacRuleDto.setZodiacTypeList(values);
                    bandLimitRuleDto.getZodiacLimitRuleList().add(zodiacRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Name.getCode())) {
                    //region 姓名
                    ResourceBandInfoDto.NameLimitRuleDto nameRuleDto = new ResourceBandInfoDto.NameLimitRuleDto();
                    nameRuleDto.setCanBook(limit.getCanBook() == 1 ? 1 : 0);
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    List<String> values = limitConditions.stream()
                            .map(LimitCondition::getContentFirst)
                            .collect(Collectors.toList());
                    nameRuleDto.setValueList(values);
                    bandLimitRuleDto.getNameLimitRuleList().add(nameRuleDto);
                    //endregion
                }
                if (limit.getLimitType().equals(TravellerCrowdLimitEnums.Student.getCode())) {
                    //region 学生
                    List<LimitCondition> limitConditions = com.ly.ticketfun.etl.common.utils.JsonUtils.string2GenericAbstract(limit.getLimitConditions(), new TypeReference<List<LimitCondition>>() {
                    });
                    if (!CollectionUtils.isEmpty(limitConditions)) {
                        LimitCondition limitCondition = limitConditions.get(0);
                        Integer code = Integer.valueOf(limitCondition.getContentFirst());
                        ResourceBandInfoDto.IdentityAuthLimitRuleDto studentRuleDto = new ResourceBandInfoDto.IdentityAuthLimitRuleDto();
                        studentRuleDto.setCanBook(1);
                        studentRuleDto.setIdentityType(ResourceBandInfoEnum.IdentityType.getValue(code));
                        bandLimitRuleDto.getIdentityAuthLimitRuleDtoList().add(studentRuleDto);
                    }
                    //endregion
                }
            }
            list.add(bandLimitRuleDto);
        }
        return list;
    }

    private String getDimension(Integer dimensionType) {
        if (dimensionType.equals(BirthDateDimensionEnums.BirthdayYear.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.YEAR.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayMonth.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.MONTH.getValue();
        } else if (dimensionType.equals(BirthDateDimensionEnums.BirthdayDay.getCode())) {
            return ResourceBandInfoEnum.BirthdayDimension.DAY.getValue();
        }
        return "";
    }

    private List<BookPassengerQuestionDto> travellerInfo(List<PolicyMultipleOptions> policyMultipleOptions, PolicyTravellerBase travellerBase) {
        List<BookPassengerQuestionDto> list = new ArrayList<>();
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o ->
                        o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())
                                || o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            for (PolicyMultipleOptions option : collect) {
                BookPassengerQuestionDto optionDto = new BookPassengerQuestionDto();
                optionDto.setQuestionType(getOptionType(option.getSelectTypeId()));
                optionDto.setPassengerRequiredType(ticketEnumMapping(ResourceBookPassengerInfoEnum.PassengerRequiredType.class, travellerBase.getNeedAllInfo(), false).getValue());
                optionDto.setQuestionCode(getQuestionCode(option.getSelectValue()));
                optionDto.setDataTypes(ResourceBookPassengerInfoEnum.DataType.MULTIPLE_ENUM.getName());
                list.add(optionDto);
            }
        }
        return list;
    }

    private String getQuestionCode(Integer selectValue) {
        if (selectValue.equals(TravellerIdCardTypeEnums.Id_Card.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_ID_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.Passport.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PASSPORT.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.GanAo.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_HM_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.TaiWan.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_TW_PASS.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.HuiXiang.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_BACK_HOME_CARD.getValue();
        } else if (selectValue.equals(TravellerIdCardTypeEnums.YongJu.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.C_PERMANENT_RESIDENCE_CARD_FOREIGNERS.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.NAME.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_CN_NAME.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.CHINA_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE.getValue();
        } else if (selectValue.equals(TravellerBaseInfoTypeEnums.FOREIGN_MOBILE.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionInfo.B_TEL_PHONE_INTERNATIONAL.getValue();
        }
        return "";
    }


    private String getOptionType(Integer selectTypeId) {
        if (selectTypeId.equals(PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.CREDENTIALS.getValue();
        } else if (selectTypeId.equals(PolicyMultipleOptionsEnums.BasicInformationOfTraveller.getCode())) {
            return ResourceBookPassengerInfoEnum.QuestionType.BASIC.getValue();
        }
        return null;
    }

    private BookContactInfoDto travellerContact(List<PolicyMultipleOptions> policyMultipleOptions) {
        BookContactInfoDto bookContactInfoDto = new BookContactInfoDto();
        bookContactInfoDto.setNameRequired(0);
        bookContactInfoDto.setMobileRequired(0);
        bookContactInfoDto.setOverseaMobileRequired(0);
        bookContactInfoDto.setEmailRequired(0);
        List<PolicyMultipleOptions> collect = policyMultipleOptions.stream()
                .filter(o -> o.getSelectTypeId().equals(PolicyMultipleOptionsEnums.ContactUserInfo.getCode()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.EMAIL.getCode()))) {
                bookContactInfoDto.setEmailRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.MAINLAND.getCode()))) {
                bookContactInfoDto.setMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NOT_MAINLAND.getCode()))) {
                bookContactInfoDto.setOverseaMobileRequired(1);
            }
            if (collect.stream().anyMatch(c -> c.getSelectValue().equals(TravellerLinkerInfoTypeEnums.NAME.getCode()))) {
                bookContactInfoDto.setNameRequired(1);
            }
        }
        return bookContactInfoDto;
    }

    private BookLimitDto orderLimit(PolicyOrderLimit orderLimit, List<PolicyOrderLimitRule> orderLimitRuleList) {
        BookLimitDto bookLimitDto = new BookLimitDto();
        bookLimitDto.setNeedAdvance(WhetherEnum.YES.getValue());
        bookLimitDto.setAdvanceBookDays(orderLimit.getInAdvanceDays());
        bookLimitDto.setAdvanceBookHour(orderLimit.getInAdvanceTime().getHour());
        bookLimitDto.setAdvanceBookMinute(orderLimit.getInAdvanceTime().getMinute());
        bookLimitDto.setAdvanceBookDescription("");
        bookLimitDto.setBookEffectMinutes(new BigDecimal(orderLimit.getInAdvanceHours() * 60 + orderLimit.getInAdvanceMinutes()));
        bookLimitDto.setPayTimeoutMinutes(orderLimit.getPayLimitMinute());
        bookLimitDto.setBookDaysFromCurrent(orderLimit.getOrderDaysLimit());
        bookLimitDto.setBookMinQuantity(orderLimit.getMinBuyNum());
        bookLimitDto.setBookMaxQuantity(orderLimit.getMaxBuyNum());
        List<BookQuotaLimitDto> limitRuleList = new ArrayList<>();
        orderLimitRuleList.forEach(rule -> {
            BookQuotaLimitDto ruleDto = new BookQuotaLimitDto();
            ruleDto.setGroupId(rule.getGroupId().toString());
            ruleDto.setLimitTargetType(ticketEnumMapping(BookQuotaLimitEnum.LimitTargetType.class, rule.getIdType(), false).getValue());
            ruleDto.setLimitTargetDesc("");
            ruleDto.setLimitDateType(rule.getDateType() == 0 ? BookQuotaLimitEnum.LimitDateType.USAGE_DATE.getValue() : BookQuotaLimitEnum.LimitDateType.CREATE_DATE.getValue());
            ruleDto.setLimitPeriodDays(rule.getDays().toString());
            ruleDto.setQuotaQuantity(rule.getNum().toString());
            ruleDto.setQuotaUnit(UnitEnums.getV(rule.getUnit()));
            limitRuleList.add(ruleDto);
        });
        bookLimitDto.setBookQuotaLimitList(limitRuleList);
        return bookLimitDto;
    }


    private BookModeDto orderMode(PolicyOrderMode orderMode, PolicyBaseInfo policyBaseInfo, List<PrecastTicketManage> precastTicketManages) {
        Integer ifAppointDate = orderMode.getIfAppointDate();
        Integer usePeriodType = orderMode.getUsePeriodType();
        Integer validDays = orderMode.getValidDays();
        Integer validDaysUnit = orderMode.getValidDaysUnit();
        LocalDateTime validityPeriodStart = orderMode.getValidityPeriodStart();
        LocalDateTime validityPeriodEnd = orderMode.getValidityPeriodEnd();
        Integer enterLimitType = orderMode.getEnterLimitType();
        BookModeDto bookModeDto = new BookModeDto();
        if (ifAppointDate == 1) {
            //指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getTicketValue())) {
                //当日
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_SINGLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.ON_THAT_DAY.getValue());
            } else {
                //指定日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.SPECIFY_MULTIPLE_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_SPECIFY_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        } else {
            //无需指定日
            if (usePeriodType.equals(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getTicketValue())) {
                //指定有效期
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.SPECIFY_VALIDITY_PERIOD.getValue());
                bookModeDto.setValidityPeriodStart(validityPeriodStart.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                bookModeDto.setValidityPeriodEnd(validityPeriodEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            } else {
                //下单日期起
                bookModeDto.setPriceMode(BookModeEnum.PriceMode.NOT_SPECIFY_USAGE_DATE.getValue());
                bookModeDto.setUsagePeriodType(BookModeEnum.UsagePeriodType.FROM_ORDER_DATE_ONWARD.getValue());
                if (validDaysUnit == 0) {
                    bookModeDto.setValidityDays(validDays.toString());
                } else {
                    bookModeDto.setValidityDays(String.valueOf(validDays * 365));
                }
            }
        }
        bookModeDto.setBeyondValidityPeriodCanUse(enterLimitType == 2 ? 1 : 0);
        //bookModeDto.setPolicyMode(ticketEnumMapping(BookModeEnum.PolicyMode.class, policyBaseInfo.getPolicyMode(), false).getValue());
        preCaseInfo(bookModeDto, orderMode, policyBaseInfo, precastTicketManages);
        bookModeDto.setUseLimitType(orderMode.getUsePeriodType());
        bookModeDto.setUseLimitWeekList(orderMode.getDateLimitWeek());
        bookModeDto.setUseLimitDateList(orderMode.getDateLimitDate());
        bookModeDto.setUseLimitTimes(orderMode.getUseLimitTimes());
        return bookModeDto;
    }

    private void preCaseInfo(BookModeDto bookModeDto, PolicyOrderMode orderMode, PolicyBaseInfo policyBaseInfo, List<PrecastTicketManage> precastTicketManages) {
        if (policyBaseInfo.getPolicyMode().compareTo(BookModeEnum.PolicyMode.Combined_Ticket.getTicketValue()) == 0 ||
                policyBaseInfo.getPolicyMode().compareTo(BookModeEnum.PolicyMode.Kill_Ticket.getTicketValue()) == 0 ||
                policyBaseInfo.getPayType().compareTo(0) == 0 ||
                orderMode.getIfAppointDate().compareTo(0) == 0) {
            return;
        }
        PrecastTicketManage findResource = precastTicketManages.stream().filter(t -> t.getDimensionType() == 2).findFirst().orElse(null);
        if (findResource != null) {
            bookModeDto.setPrecastBook(1);
            bookModeDto.setPrecastBookDays(findResource.getAppendSoldDays());
            return;
        }
        PrecastTicketManage policy = precastTicketManages.stream()
                .filter(t -> t.getDimensionType() == 4 && t.getPolicyId().intValue() == policyBaseInfo.getPolicyOriginalId())
                .findFirst()
                .orElse(null);
        if (policy != null) {
            bookModeDto.setPrecastBook(1);
            bookModeDto.setPrecastBookDays(policy.getAppendSoldDays());
        }
    }

    public static <T> QueryWrapper<T> of(Class<T> entityClass, Long policyId, Long resourceId) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return wrapper.eq("policy_id", policyId)
                .eq("resource_id", resourceId)
                .eq("row_status", 1);
    }
}
