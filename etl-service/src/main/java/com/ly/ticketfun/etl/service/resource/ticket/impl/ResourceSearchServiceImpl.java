package com.ly.ticketfun.etl.service.resource.ticket.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.LogicExtendEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceImageEnum;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import com.ly.ticketfun.etl.common.utils.ConfigClientUtils;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.*;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.IResourcePlayService;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresourceall.*;
import com.ly.ticketfun.etl.domain.tczbyresourceall.dto.BasicResourceInfoDto;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceFileDto;
import com.ly.ticketfun.etl.domain.templateResourceSearch.TicketFunTemplateResourceSearchInfo;
import com.ly.ticketfun.etl.domain.templateResourceSearch.dto.*;
import com.ly.ticketfun.etl.service.resource.common.transform.TransformBaseServiceImpl;
import com.ly.ticketfun.etl.service.resource.ticket.IResourceSearchService;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

//@Service
public class ResourceSearchServiceImpl extends TransformBaseServiceImpl implements IResourceSearchService {

    @Resource
    private ITidbResourceBaseInfoService resourceBaseInfoService;
    @Resource
    private ITidbResourceAreaTravelInfoService resourceAreaTravelInfoService;
    @Resource
    private ITidbResourceTagInfoService resourceTagInfoService;
    @Resource
    private ITidbResourceChannelSaleShowService resourceChannelSaleShowService;
    @Resource
    private IResourcePlayService resourcePlayService;
    @Resource
    private ITidbResourceExposedShelfService resourceExposedShelfService;
    @Resource
    private ITidbResourceLabelRelationService resourceLabelRelationService;

    @Override
    public TicketFunTemplateResourceSearchInfo toTemplateResourceSearch(Long resourceId) {
        TicketFunTemplateResourceSearchInfo templateResourceSearchInfo = new TicketFunTemplateResourceSearchInfo();

        ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryValidByResourceId(resourceId);

        // 资源基础信息
        fetchBasicResourceInfo(resourceId, templateResourceSearchInfo);

        // 资源游玩景点+主题+别名
        fetchResourceSubjectAliasesInfo(resourceId, templateResourceSearchInfo);

        // 出发地和目的地
        fetchLocationInfo(resourceBaseInfo, templateResourceSearchInfo);

        // 标签信息
        fetchSearchLabelInfo(resourceId, templateResourceSearchInfo);

        // 渠道价格信息
        fetchResourceChannelSaleShow(resourceId, templateResourceSearchInfo);

        // 亮点信息
        fetchResourceHighlightInfo(resourceId, templateResourceSearchInfo);

        // 资源视频
        fetchResourceVideoInfo(resourceBaseInfo, templateResourceSearchInfo);

        // 货架状态
        fetchMpShelfShowInfo(resourceId, templateResourceSearchInfo);

        // 设施信息
        fetchResourceFacilityInfo(resourceId, templateResourceSearchInfo);

        return templateResourceSearchInfo;
    }

    private void fetchBasicResourceInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        BasicResourceInfoDto basicResourceInfo = resourceBaseInfoService.getBasicResourceInfo(resourceId);
        BeanUtil.copyProperties(basicResourceInfo, templateResourceSearchInfo);
        templateResourceSearchInfo.setEstablishmentDate(formatLocalDateTime(basicResourceInfo.getEstablishmentDate()));
        templateResourceSearchInfo.setBaiduLon(basicResourceInfo.getBaiduLon().toPlainString());
        templateResourceSearchInfo.setBaiduLat(basicResourceInfo.getBaiduLat().toPlainString());
        templateResourceSearchInfo.setEffectiveTime(convertToTimestamp(basicResourceInfo.getEffectiveTime()));
        List<String> selfSupports = Arrays.asList("72901", "72905");
        templateResourceSearchInfo.setCooperationType(selfSupports.contains(basicResourceInfo.getCooperationType()) ? 1 : 0);
        templateResourceSearchInfo.setRefundType(ticketEnumMapping(ResourceSearchEnum.RefundTypeEnum.class, basicResourceInfo.getRefundType(), false).getValue());
        templateResourceSearchInfo.setResourceGrade(ticketEnumMapping(ResourceSearchEnum.ResourceGradeEnum.class, basicResourceInfo.getResourceGrade(), false).getValue());
        templateResourceSearchInfo.setRevolutionResource(ticketEnumMapping(WhetherEnum.class, isRevolutionResource(String.valueOf(resourceId)), false).getValue());
        templateResourceSearchInfo.setLogicalData(convertFromBasicResourceInfo(basicResourceInfo));
        // todo 供应商Id
    }

    public boolean isRevolutionResource(String resourceId) {
        String revolutionResourceStr = ConfigClientUtils.getOrDefault(
                ConfigClientUtils.TCSCENERY_JAVA_RESOURCE_FRONTEND,
                "revolutionResource",
                "");
        return revolutionResourceStr.contains(String.format(",%s,", resourceId));
    }

    private String formatLocalDateTime(LocalDateTime dateTime) {
        return Objects.isNull(dateTime) ? null : dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private long convertToTimestamp(LocalDateTime dateTime) {
        return Objects.isNull(dateTime) ? 0L : dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    private List<SearchLogicExtendDto> convertFromBasicResourceInfo(BasicResourceInfoDto basicResourceInfo) {
        List<SearchLogicExtendDto> result = new ArrayList<>();
        for (LogicExtendEnum.ResourceSearchKeyEnum keyEnum : LogicExtendEnum.ResourceSearchKeyEnum.values()) {
            String key = keyEnum.getValue();
            Object value = getFieldValueByEnumKey(basicResourceInfo, key);
            if (Objects.nonNull(value)) {
                result.add(new SearchLogicExtendDto(key, value));
            }
        }
        return result;
    }

    private Object getFieldValueByEnumKey(BasicResourceInfoDto basicResourceInfo, String enumKey) {
        try {
            Field field = BasicResourceInfoDto.class.getDeclaredField(enumKey);
            field.setAccessible(true);
            return field.get(basicResourceInfo);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }

    private void fetchResourceSubjectAliasesInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<ResourceAreaTravelInfo> resourceAreaTravelInfoList = resourceAreaTravelInfoService.queryValidByResourceId(resourceId);
        List<SubjectAliasesDto> subjectAliasesList = resourceAreaTravelInfoList.stream().map(resourceAreaTravelInfo -> {
            SubjectAliasesDto subjectAliasesDto = new SubjectAliasesDto();
            subjectAliasesDto.setResourceId(resourceAreaTravelInfo.getResourceId());
            subjectAliasesDto.setType(ticketEnumMapping(ResourceSearchEnum.SubjectTypeEnum.class, resourceAreaTravelInfo.getResourceType(), false).getValue());
            subjectAliasesDto.setTravelResourceId(resourceAreaTravelInfo.getTravelResourceId());
            subjectAliasesDto.setTravelResourceLocalName(resourceAreaTravelInfo.getLocalName());
            subjectAliasesDto.setTravelResourceEnglishName(resourceAreaTravelInfo.getEnglishName());
            subjectAliasesDto.setTravelResourceThemeId(Long.valueOf(resourceAreaTravelInfo.getThemeId()));
            subjectAliasesDto.setTravelResourceThemeName(resourceAreaTravelInfo.getThemeName());
            subjectAliasesDto.setTravelResourceSubThemeId(Long.valueOf(resourceAreaTravelInfo.getSecondThemeId()));
            subjectAliasesDto.setTravelResourceSubThemeName(resourceAreaTravelInfo.getSecondThemeName());
            subjectAliasesDto.setTravelResourceAliasName(resourceAreaTravelInfo.getTravelResourceAliasName());
            return subjectAliasesDto;
        }).collect(Collectors.toList());
        templateResourceSearchInfo.setSubjectAliasesList(subjectAliasesList);
    }

    private void fetchLocationInfo(ResourceBaseInfo resourceBaseInfo, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        LocationDto locationDto = BeanUtil.copyProperties(resourceBaseInfo, LocationDto.class);
        locationDto.setResourceId(resourceBaseInfo.getId());
        locationDto.setPlaceType(ticketEnumMapping(ResourceSearchEnum.PlaceTypeEnum.class, 1, false).getValue());
        templateResourceSearchInfo.setLocationList(Collections.singletonList(locationDto));
    }

    private void fetchSearchLabelInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<ResourceTagInfo> resourceTagInfos = resourceTagInfoService.queryValidListByResourceId(resourceId);
        List<SearchLabelInfoDto> searchLabelInfoList = resourceTagInfos.stream()
                .map(resourceTagInfo -> BeanUtil.copyProperties(resourceTagInfo, SearchLabelInfoDto.class))
                .collect(Collectors.toList());
        templateResourceSearchInfo.setSearchLabelInfoList(searchLabelInfoList);
    }

    private void fetchResourceChannelSaleShow(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<ResourceChannelSaleShow> channelSaleShows = resourceChannelSaleShowService.queryValidListByResourceId(resourceId);
        List<ChannelPriceDto> channelPriceList = channelSaleShows.stream()
                .map(channelSaleShow -> {
                    ChannelPriceDto channelPrice = BeanUtil.copyProperties(channelSaleShow, ChannelPriceDto.class);
                    channelPrice.setPriceType(ticketEnumMapping(ResourceSearchEnum.PriceTypeEnum.class, channelSaleShow.getPriceType(), false).getValue());
                    channelPrice.setBatchNos(channelSaleShow.getRedpackageBatchNos());
                    // todo 销售终端 + 销售渠道 + 销售RefId
                    return channelPrice;
                })
                .collect(Collectors.toList());
        templateResourceSearchInfo.setChannelPriceList(channelPriceList);
    }

    private void fetchResourceHighlightInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<HighLightInfoDto> highLightInfoList = resourcePlayService.queryHighLightInfoByResourceId(resourceId);
        templateResourceSearchInfo.setHighLightInfoList(highLightInfoList);
    }

    private void fetchResourceVideoInfo(ResourceBaseInfo resourceBaseInfo, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        ResourceFileDto resourceFileDto = new ResourceFileDto();
        resourceFileDto.setFileType(ResourceImageEnum.FileType.VIDEO_INTRODUCE.getValue());
        resourceFileDto.setFileUrl(resourceBaseInfo.getVideoUrl());
        resourceFileDto.setFileCompressUrl(resourceBaseInfo.getVideoUrl360());
        resourceFileDto.setFileCoverUrl(resourceBaseInfo.getImagePath());
        templateResourceSearchInfo.setFileList(Collections.singletonList(resourceFileDto));
    }

    private void fetchMpShelfShowInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<ResourceExposedShelf> resourceExposedShelves = resourceExposedShelfService.queryResourceExposedShelfListByResourceId(resourceId);
        List<MpShelfShowDto> mpShelfShowList = resourceExposedShelves.stream()
                .map(resourceExposedShelf -> {
                    MpShelfShowDto mpShelfShowDto = BeanUtil.copyProperties(resourceExposedShelf, MpShelfShowDto.class);
                    mpShelfShowDto.setShowShelfType(ticketEnumMapping(ResourceSearchEnum.ShowShelfTypeEnum.class, resourceExposedShelf.getShowShelfType(), false).getValue());
                    return mpShelfShowDto;
                }).collect(Collectors.toList());
        templateResourceSearchInfo.setShelfShowList(mpShelfShowList);
    }

    private void fetchResourceFacilityInfo(Long resourceId, TicketFunTemplateResourceSearchInfo templateResourceSearchInfo) {
        List<ResourceLabelRelation> resourceLabelRelationList = resourceLabelRelationService.queryValidListByResourceId(resourceId);
        List<FacilityInfoDto> resourceFacilityInfoList = resourceLabelRelationList.stream()
                .map(resourceLabelRelation -> {
                    FacilityInfoDto facilityInfoDto = new FacilityInfoDto();
                    facilityInfoDto.setId(String.valueOf(resourceLabelRelation.getLabelId()));
                    facilityInfoDto.setName(resourceLabelRelation.getLabelIdName());
                    facilityInfoDto.setTypeId(Math.toIntExact(resourceLabelRelation.getLabelTypeId()));
                    facilityInfoDto.setTypeName(resourceLabelRelation.getLabelTypeName());
                    facilityInfoDto.setUseMonth(resourceLabelRelation.getUseMonth());
                    facilityInfoDto.setShowUrl(resourceLabelRelation.getLabelUrl());
                    facilityInfoDto.setFacilitySourceFrom(ticketEnumMapping(ResourceSearchEnum.FacilitySourceFromEnum.class, resourceLabelRelation.getLabelSource(), false).getValue());
                    return facilityInfoDto;
                }).collect(Collectors.toList());
        templateResourceSearchInfo.setFacilityInfoList(resourceFacilityInfoList);
    }
}
