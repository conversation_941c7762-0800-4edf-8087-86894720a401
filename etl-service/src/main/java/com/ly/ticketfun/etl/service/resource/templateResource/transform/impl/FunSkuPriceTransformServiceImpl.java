package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.ResourceStock;
import com.ly.localactivity.model.domain.tczbactivityresource.SkuPrice;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SkuPriceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SkuResourceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SubResourceAgg;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.SkuResourcePriceDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * 玩乐Sku价格转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class FunSkuPriceTransformServiceImpl extends TRTransformBaseServiceImpl implements ITRTransformService {


    @Autowired
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 支持
     *
     * @param resourceType   资源类型
     * @param changeCategory 变更类别
     * @return {@link Boolean}
     */
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.FUN)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    public List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
        if (checkParams(changeRo)) {
            return null;
        }
        // -------------- 数据查询 --------------
        MainResourceAgg mainResourceAgg = ticketFunInnerApiService.queryFunSkuResourceAgg(changeRo.getProductId().toString(), changeRo.getPackageId().toString(), changeRo.getSkuId().toString(), true);
        // -------------- 数据校验 --------------
        if (checkData(mainResourceAgg)) {
            return null;
        }
        // -------------- 数据匹配 --------------
        // -------------- 数据返回 --------------
        return convertData(mainResourceAgg);
    }

    private List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> convertData(MainResourceAgg mainResourceAgg) {
        List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> resultListRo = new ArrayList<>();
        MainResource mainResource = mainResourceAgg.getMainResource();
        for (SubResourceAgg subResourceAgg : mainResourceAgg.getSubResourceAggList()) {
            for (SkuResourceAgg skuResourceAgg : subResourceAgg.getSkuResourceAggList()) {
                TRTransformResultRo<TicketFunTemplateResourceSkuInfo> resultRo = new TRTransformResultRo<>();
                resultRo.setProductId(mainResource.getId().toString());
                resultRo.setPackageId(subResourceAgg.getSubResource().getId().toString());
                resultRo.setSkuId(skuResourceAgg.getSkuResource().getId().toString());
                TicketFunTemplateResourceSkuInfo skuInfo = new TicketFunTemplateResourceSkuInfo();
                skuInfo.setProductId(mainResource.getId().toString());
                skuInfo.setPackageId(subResourceAgg.getSubResource().getId().toString());
                skuInfo.setSkuId(skuResourceAgg.getSkuResource().getId().toString());
                skuInfo.setNetPriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo 这里从产品的维度取值
                skuInfo.setSalePriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo
                skuInfo.setMarketPriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo
                List<SkuResourcePriceDto.PriceCalendarDto> priceList = new ArrayList<>();
                for (SkuPriceAgg skuPriceAgg : skuResourceAgg.getSkuPriceAggList()) {
                    SkuResourcePriceDto.PriceCalendarDto priceCalendarDto = new SkuResourcePriceDto.PriceCalendarDto();
                    SkuPrice skuPrice = skuPriceAgg.getSkuPrice();
                    if (skuPrice != null) {
                        priceCalendarDto.setPriceDate(skuPrice.getPriceDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                        priceCalendarDto.setSalePrice(skuPrice.getSellPrice());
                        priceCalendarDto.setNetPrice(skuPrice.getSettlementPrice());
                        priceCalendarDto.setMarketPrice(skuPrice.getSellPrice());
                        // todo fengjian
//                        priceCalendarDto.setStockType(skuPrice.getStockType());
                        ResourceStock resourceStock = skuPriceAgg.getResourceStock();
                        if (resourceStock != null && resourceStock.getStockTotalNum() != null && resourceStock.getStockUseNum() != null) {
                            priceCalendarDto.setStockQuantity(resourceStock.getStockTotalNum() - resourceStock.getStockUseNum());
                        }
                        priceList.add(priceCalendarDto);
                    }
                }
                skuInfo.setPriceList(priceList);
                resultListRo.add(resultRo);
            }
        }
        return resultListRo;
    }

    /**
     * 检查参数
     *
     * @param changeRo
     * @return
     */
    private Boolean checkParams(TicketFunResourceChangeRo changeRo) {
        if (changeRo == null || changeRo.getProductId() == null || changeRo.getPackageId() == null || changeRo.getSkuId() == null) {
            return false;
        }
        return true;
    }

    /**
     * 检查 数据
     *
     * @param mainResourceAgg
     * @return
     */
    private Boolean checkData(MainResourceAgg mainResourceAgg) {
        if (mainResourceAgg == null || CollectionUtil.isEmpty(mainResourceAgg.getSubResourceAggList())) {
            return false;
        }
        for (SubResourceAgg subResourceAgg : mainResourceAgg.getSubResourceAggList()) {
            if (CollectionUtil.isEmpty(subResourceAgg.getSkuResourceAggList())) {
                return false;
            }
        }
        return true;
    }

}
