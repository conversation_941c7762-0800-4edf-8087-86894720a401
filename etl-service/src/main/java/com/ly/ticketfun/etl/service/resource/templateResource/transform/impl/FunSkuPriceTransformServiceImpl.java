package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.ResourceStock;
import com.ly.localactivity.model.domain.tczbactivityresource.SkuPrice;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SkuPriceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SkuResourceAgg;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.SubResourceAgg;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceEnum;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SkuResourceEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.SkuResourcePriceDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.common.transform.FunBaseTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 玩乐Sku价格转换模版资源服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class FunSkuPriceTransformServiceImpl extends FunBaseTransformServiceImpl implements ITRTransformService<TicketFunTemplateResourceSkuInfo> {
    @Autowired
    private ITicketFunInnerApiService ticketFunInnerApiService;

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    public List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        // -------------- 入参校验 --------------
        if (changeRo == null || StringUtils.isEmpty(changeRo.getProductId())) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "产品Id为空");
        }
        // -------------- 数据查询 --------------
        MainResourceAgg mainResourceAgg = ticketFunInnerApiService.queryFunSkuResourceAgg(changeRo.getProductId(), changeRo.getPackageId(), changeRo.getSkuId(), true, true);
        // -------------- 数据校验 --------------
        if (mainResourceAgg == null) {
            // 需要下架产品
            return Collections.singletonList(getErrorTransformResultRo(changeRo.getProductId(), null, null, new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS, changeRo.getProductId())));
        }
        if (mainResourceAgg.getMainResource().getDataFlag().equals(DataFlagEnum.INVALID.getValue())
                || mainResourceAgg.getMainResource().getIsDelete().equals(DeleteFlagEnum.DELETED.getValue())
                || !mainResourceAgg.getMainResource().getAuditStatus().equals(MainResourceEnum.AuditStatus.AUDIT_PASS.getValue())
                || mainResourceAgg.getDetail().getDataFlag().equals(DataFlagEnum.INVALID.getValue())
                || mainResourceAgg.getDetail().getIsCanAdvance().equals(WhetherEnum.NO.getValue())) {
            // 需要下架产品
            return Collections.singletonList(getErrorTransformResultRo(changeRo.getProductId(), null, null, new TRTransformResultRo<>(TRTransformException.ErrorInfo.RESOURCE_NOT_ON_SALE, changeRo.getProductId())));
        }
        // 下架套餐
        if (CollectionUtil.isEmpty(mainResourceAgg.getSubResourceAggList())) {
            // 需要下架产品套餐
            return Collections.singletonList(getErrorTransformResultRo(changeRo.getProductId(), changeRo.getPackageId(), null, new TRTransformResultRo<>(TRTransformException.ErrorInfo.PACKAGE_NOT_EXISTS, changeRo.getProductId())));
        }

        // -------------- 数据匹配 --------------
        return matchData(mainResourceAgg);
    }

    /**
     * 获取错误结果实体
     *
     * @param productId
     * @param packageId
     * @param transformResultRo
     * @return
     */
    private TRTransformResultRo<TicketFunTemplateResourceSkuInfo> getErrorTransformResultRo(String productId, String packageId, String skuId, TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformResultRo) {
        if (transformResultRo == null) {
            transformResultRo = new TRTransformResultRo<>();
        }
        transformResultRo.setProductId(productId);
        transformResultRo.setPackageId(packageId);
        transformResultRo.setSkuId(skuId);

        return transformResultRo;
    }

    private List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> matchData(MainResourceAgg mainResourceAgg) {
        List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> resultListRo = new ArrayList<>();
        MainResource mainResource = mainResourceAgg.getMainResource();
        for (SubResourceAgg subResourceAgg : mainResourceAgg.getSubResourceAggList()) {
            for (SkuResourceAgg skuResourceAgg : subResourceAgg.getSkuResourceAggList()) {
                TRTransformResultRo<TicketFunTemplateResourceSkuInfo> resultRo = new TRTransformResultRo<>();
                resultRo.setProductId(mainResource.getSerialId());
                resultRo.setPackageId(subResourceAgg.getSubResource().getSerialId());
                resultRo.setSkuId(skuResourceAgg.getSkuResource().getSerialId());

                if (!DataFlagEnum.VALID.getValue().equals(subResourceAgg.getSubResource().getDataFlag()) || subResourceAgg.getDetail() == null || !WhetherEnum.YES.getValue().equals(subResourceAgg.getDetail().getIsCanAdvance())) {
                    // 需要下架sku
                    resultListRo.add(getErrorTransformResultRo(resultRo.getProductId(), resultRo.getPackageId(), resultRo.getSkuId(), new TRTransformResultRo<>(TRTransformException.ErrorInfo.SKU_NOT_ON_SALE, resultRo.getProductId(), resultRo.getPackageId(), resultRo.getSkuId())));
                     continue;
                }

                resultRo.setIsSuccess(true);
                TicketFunTemplateResourceSkuInfo skuInfo = new TicketFunTemplateResourceSkuInfo();
                skuInfo.setLocale(LocaleEnum.ZH_CN.getValue());// todo 语言
                skuInfo.setProductId(resultRo.getProductId());
                skuInfo.setPackageId(resultRo.getPackageId());
                skuInfo.setSkuId(resultRo.getSkuId());

                skuInfo.setNetPriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo 这里从产品的维度取值
                skuInfo.setSalePriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo
                skuInfo.setMarketPriceCurrency(mainResourceAgg.getDetail().getCurrency());//todo
                if (CollectionUtil.isNotEmpty(skuResourceAgg.getSkuPriceAggList())) {
                    List<SkuResourcePriceDto.PriceCalendarDto> priceList = new ArrayList<>();
                    for (SkuPriceAgg skuPriceAgg : skuResourceAgg.getSkuPriceAggList()) {
                        SkuResourcePriceDto.PriceCalendarDto priceCalendarDto = new SkuResourcePriceDto.PriceCalendarDto();
                        SkuPrice skuPrice = skuPriceAgg.getSkuPrice();
                        if (skuPrice != null) {
                            priceCalendarDto.setPriceDate(skuPrice.getPriceDate().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                            priceCalendarDto.setSalePrice(skuPrice.getSellPrice());
                            priceCalendarDto.setNetPrice(skuPrice.getSettlementPrice());
                            priceCalendarDto.setMarketPrice(skuPrice.getSellPrice());
                            if (skuPrice.getStockType() != null) {
                                SkuResourceEnum.StockType stockType = funEnumMapping(SkuResourceEnum.StockType.class, skuPrice.getStockType());
                                if (stockType != null) {
                                    priceCalendarDto.setStockType(stockType.getValue());
                                }
                            }
                            ResourceStock resourceStock = skuPriceAgg.getResourceStock();
                            if (SkuResourceEnum.StockType.LIMITED_STOCK.getValue().equals(priceCalendarDto.getStockType()) && resourceStock != null && resourceStock.getStockTotalNum() != null && resourceStock.getStockUseNum() != null) {
                                priceCalendarDto.setStockQuantity(resourceStock.getStockTotalNum() - resourceStock.getStockUseNum());
                            }
                            priceList.add(priceCalendarDto);
                        }
                    }
                    skuInfo.setPriceList(priceList);
                }
                resultRo.setData(skuInfo);
                resultListRo.add(resultRo);
            }
        }
        return resultListRo;
    }

}
