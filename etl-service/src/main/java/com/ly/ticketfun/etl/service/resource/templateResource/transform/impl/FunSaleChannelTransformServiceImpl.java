package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.localactivity.model.domain.tczbactivityresource.MainResource;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.localactivity.model.enums.tczbactivityresource.MainResourceEnum;
import com.ly.ticketfun.etl.common.enums.base.PlatformEnum;
import com.ly.ticketfun.etl.common.enums.base.SalePointCodeEnum;
import com.ly.ticketfun.etl.common.enums.fun.SelectionSupplierEnum;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SourceAndCooperationEnum;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSaleChannelInfo;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceCategoryDto;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.common.transform.FunBaseTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * FunSaleChannelTransformServiceImpl
 *
 * <AUTHOR>
 * @date 2025/9/16
 */
@Service
public class FunSaleChannelTransformServiceImpl extends FunBaseTransformServiceImpl implements ITRTransformService<TicketFunTemplateResourceSaleChannelInfo> {

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类型
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourceSaleChannelInfo>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        MainResourceAgg mainResourceAgg = new MainResourceAgg();

        //处理终端
        List<SalePointCodeEnum> salePointList = parseSalePointCode(mainResourceAgg.getMainResource());
        //处理品类
        ResourceCategoryDto resourceCategoryDto = parseCategoryFun().apply(mainResourceAgg.getCategoryList());
        //售卖时间限制
        WhetherEnum saleTimeLimit = WhetherEnum.NO;
        //处理平台
        List<PlatformEnum> platformList = parsePlatforms(mainResourceAgg.getMainResource());


        return null;

    }

    private List<PlatformEnum> parsePlatforms(MainResource mainResource) {
        Integer source = mainResource.getSource();
        Long supplierId = mainResource.getSupplierId();
        if (MainResourceEnum.Source.SELECTION.getValue().equals(source) &&
                (SelectionSupplierEnum.KLook.getValue().equals(supplierId) ||
                        SelectionSupplierEnum.CTRIP_WAN_CHENG.getValue().equals(supplierId) ||
                        SelectionSupplierEnum.CTRIP_TRAVEL_HOLDING_LIMITED.getValue().equals(supplierId))
        ) {
            return Stream.concat(PlatformEnum.TC_LX_PLATFORMS.stream(), PlatformEnum.HOPE_GOO_PLATFORMS.stream()).collect(Collectors.toList());

        } else {
            return PlatformEnum.TC_LX_PLATFORMS;
        }

    }


    /**
     * 解析销售终端
     *
     * @param mainResource 主要资源
     * @return {@link SalePointCodeEnum }
     */
    private List<SalePointCodeEnum> parseSalePointCode(MainResource mainResource) {
        Integer source = mainResource.getSource();
        Long supplierId = mainResource.getSupplierId();
        if (MainResourceEnum.Source.SELECTION.getValue().equals(source) &&
                (SelectionSupplierEnum.KLook.getValue().equals(supplierId) ||
                        SelectionSupplierEnum.CTRIP_WAN_CHENG.getValue().equals(supplierId) ||
                        SelectionSupplierEnum.CTRIP_TRAVEL_HOLDING_LIMITED.getValue().equals(supplierId))
        ) {

            return Arrays.asList(SalePointCodeEnum.TCLX, SalePointCodeEnum.HOPEGOO);

        } else {
            return Collections.singletonList(SalePointCodeEnum.TCLX);
        }
    }
}