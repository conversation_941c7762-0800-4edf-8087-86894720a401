package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.ticketfun.etl.common.enums.base.CurrencyEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.domain.common.ExchangeRateInfoDto;
import com.ly.ticketfun.etl.service.resource.common.transform.TransformBaseServiceImpl;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

public abstract class TicketBaseTransformFunctionServiceImpl extends TransformBaseServiceImpl {

    /**
     * 根据汇率计算价格
     *
     * @param price              原始价格
     * @param priceCurrencyCode  原始价格币种code
     * @param resultCurrencyCode 结果价格币种code
     * @param exchangeRateList   汇率列表
     * @param scale              保留小数位数
     * @return 结果价格
     */
    protected BigDecimal calculatePriceByRate(BigDecimal price,
                                              String priceCurrencyCode,
                                              String resultCurrencyCode,
                                              List<ExchangeRateInfoDto> exchangeRateList,
                                              Integer scale
    ) {
        if (Objects.equals(priceCurrencyCode, resultCurrencyCode)) {
            return price;
        }
        if (price.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (CollectionUtils.isEmpty(exchangeRateList)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "汇率为空");
        }

        // 获取币种汇率
        ExchangeRateInfoDto originalPriceExchangeRate = exchangeRateList.stream()
                .filter(i -> Objects.equals(i.getCurrencyType(), priceCurrencyCode))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("获取币种汇率异常" + priceCurrencyCode));

        ExchangeRateInfoDto resultPriceExchangeRate = exchangeRateList.stream()
                .filter(i -> Objects.equals(i.getCurrencyType(), resultCurrencyCode))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("获取币种汇率异常" + resultCurrencyCode));

        BigDecimal originalCurrencyRate = new BigDecimal(originalPriceExchangeRate.getMaxRate());
        BigDecimal resultCurrencyRate = new BigDecimal(resultPriceExchangeRate.getMaxRate());

        BigDecimal resultPrice;
        String cnyCurrencyCode = CurrencyEnum.CNY.getValue();

        // 存在人民币
        if (Objects.equals(priceCurrencyCode, cnyCurrencyCode) || Objects.equals(resultCurrencyCode, cnyCurrencyCode)) {
            // 不同币种，根据汇率转换
            boolean isCNY = Objects.equals(priceCurrencyCode, cnyCurrencyCode);
            BigDecimal currencyRate = isCNY ? originalCurrencyRate : resultCurrencyRate;

            resultPrice = isCNY ?
                    price.divide(currencyRate, scale, RoundingMode.UP) :
                    price.multiply(currencyRate).setScale(scale, RoundingMode.UP);
        } else {
            // 都不为人民币，通过人民币转换
            resultPrice = price.multiply(originalCurrencyRate)
                    .divide(resultCurrencyRate, scale, RoundingMode.UP);
        }

        return resultPrice;
    }
}
