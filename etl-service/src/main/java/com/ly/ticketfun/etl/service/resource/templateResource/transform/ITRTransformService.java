package com.ly.ticketfun.etl.service.resource.templateResource.transform;

import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;

import java.util.List;

/**
 * 模版资源转换服务
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
public interface ITRTransformService<T> {

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类别
     * @return {@link TRTransformResultRo}
     */
    default TRTransformResultRo<T> transformData(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return null;
    }

    /**
     * 转换数据
     *
     * @param changeRo       更改ro
     * @param changeCategory 变更类型
     * @return {@link List}<{@link TRTransformResultRo}>
     */
    default List<TRTransformResultRo<T>> transformDataList(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return null;
    }
}
