package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyPriceCalendarService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerBaseService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceBaseInfoService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSaleChannelInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service("ticketSaleChannelTransformService")
public class TicketSaleChannelTransformServiceImpl extends TRTransformBaseServiceImpl
        implements ITRTransformService<TicketFunTemplateResourceSaleChannelInfo> {

    @Resource
    private IPolicyPriceCalendarService policyPriceCalendarService;

    @Resource
    private IResourceBaseInfoService resourceBaseInfoService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Resource
    private IPolicyTravellerBaseService policyTravellerBaseService;

    @Override
    public Boolean support(String resourceType, MQEnum.TicketFunResourceChangeCategory changeCategory) {
        return resourceType.equals(TemplateResourceConstant.ResourceType.TICKET)
                && changeCategory.equals(MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
    }

    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourceSaleChannelInfo>> transformDataList(
            TicketFunResourceChangeRo changeRo,
            MQEnum.TicketFunResourceChangeCategory changeCategory
    ) {
        try {
            Long resourceId = changeRo.getPoiId();
            Long policyId = Long.valueOf(changeRo.getPackageId());
            List<TRTransformResultRo<TicketFunTemplateResourceSaleChannelInfo>> resultList = new ArrayList<>();

            if (resourceId == null || resourceId <= 0 || policyId == null || policyId <= 0) {
                throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR);
            }

            // 获取景区基础信息
            ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryByResourceId(resourceId);
            if (resourceBaseInfo == null) {
                throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
            }

            // 获取时区信息
            GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(
                    resourceBaseInfo.getCountryId(),
                    resourceBaseInfo.getCityId()
            );
            if (globalRegionDateDto == null) {
                throw new TRTransformException(TRTransformException.ErrorInfo.SALE_CHANNEL_TRANSFORM_FAIL,
                        String.valueOf(resourceId), String.valueOf(policyId), "getTimezone");
            }

            // 获取当地时间
            LocalDateTime nowDateTime = LocalDateTime.now(ZoneOffset.UTC);
            if (StringUtils.isNotEmpty(globalRegionDateDto.getTimeZone())) {
                nowDateTime = nowDateTime.plusHours(Long.parseLong(globalRegionDateDto.getTimeZone()));
            }

            // 查询政策价格日历
            List<PolicyPriceCalendar> policyPriceList = policyPriceCalendarService.querySaleListByResourceIdAndPolicyId(
                    resourceId,
                    policyId,
                    nowDateTime.toLocalDate(),
                    null
            );

            return resultList;
        } catch (TRTransformException e) {
            return new ArrayList<>(Collections.singletonList(
                    new TRTransformResultRo<>(e.getErrorInfo(), e.getExtendList())
            ));
        }
    }
}
