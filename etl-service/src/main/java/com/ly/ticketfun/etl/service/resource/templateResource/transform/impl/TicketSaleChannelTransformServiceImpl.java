package com.ly.ticketfun.etl.service.resource.templateResource.transform.impl;

import com.alibaba.fastjson2.JSON;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.utils.ConfigUtils;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.constant.TemplateResourceConstant;
import com.ly.ticketfun.etl.common.enums.base.CurrencyEnum;
import com.ly.ticketfun.etl.common.enums.base.PlatformEnum;
import com.ly.ticketfun.etl.common.enums.base.SaleSiteEnum;
import com.ly.ticketfun.etl.common.enums.base.SalePointCodeEnum;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.enums.ticket.PolicyMultipleOptionsEnums;
import com.ly.ticketfun.etl.common.enums.ticket.TravellerIdCardTypeEnums;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyBaseInfoService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyChannelSalesService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyPriceCalendarService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyUseRuleService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketOrderRuleService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketResourceService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.common.ExchangeRateInfoDto;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.pojo.SaleChannelRelation;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketResourceAgg;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketTravellerAgg;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSaleChannelInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service("ticketSaleChannelTransformService")
public class TicketSaleChannelTransformServiceImpl extends TicketBaseTransformFunctionServiceImpl
        implements ITRTransformService<TicketFunTemplateResourceSaleChannelInfo> {

    @Resource
    private IPolicyPriceCalendarService policyPriceCalendarService;

    @Resource
    private ITicketResourceService ticketResourceService;

    @Resource
    private ITicketOrderRuleService ticketOrderRuleService;

    @Resource
    private IPolicyUseRuleService policyUseRuleService;

    @Resource
    private IPolicyChannelSalesService policyChannelSalesService;

    @Resource
    private IPolicyBaseInfoService policyBaseInfoService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public List<TRTransformResultRo<TicketFunTemplateResourceSaleChannelInfo>> transformDataList(
            TicketFunResourceChangeRo changeRo,
            MQEnum.TicketFunResourceChangeCategory changeCategory
    ) {
        try {
            Long resourceId = changeRo.getPoiId();
            Long policyId = NumberUtils.toLong(changeRo.getPackageId(), 0);

            if (resourceId == null || resourceId <= 0) {
                throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "景区id为空");
            }
            if (policyId <= 0) {
                throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "政策id为空");
            }

            // 获取政策基础信息
            PolicyBaseInfo policyBaseInfo = policyBaseInfoService.queryByResourceIdAndPolicyId(resourceId, policyId);
            if (policyBaseInfo == null) {
                throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_NOT_EXISTS,
                        String.valueOf(resourceId), String.valueOf(policyId));
            }

            // 汇率列表
            List<ExchangeRateInfoDto> exchangeRateList = ticketFunInnerApiService.fetchExchangeRateList();

            // 获取景区基础信息
            TicketResourceAgg ticketResourceAgg = ticketResourceService.fetchResource(resourceId);
            GlobalRegionDateDto globalRegionDateDto = ticketResourceAgg.getGlobalRegionDate();

            // 获取当地时间
            LocalDateTime nowDateTime = LocalDateTime.now(ZoneOffset.UTC);
            int timeZone = 8;
            if (StringUtils.isNotEmpty(globalRegionDateDto.getTimeZone())) {
                timeZone = Integer.parseInt(globalRegionDateDto.getTimeZone());
                nowDateTime = nowDateTime.plusHours(timeZone);
            }

            LocalDate nowBeginDate = nowDateTime.toLocalDate();
            // 最多售卖四个月
            LocalDate endDate = nowBeginDate.plusMonths(3)
                    .with(TemporalAdjusters.lastDayOfMonth());

            // 查询政策价格日历
            List<PolicyPriceCalendar> priceList = policyPriceCalendarService
                    .querySaleListByResourceIdAndPolicyId(resourceId, policyId, nowBeginDate, endDate);
            if (CollectionUtils.isEmpty(priceList)) {
                throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_GET_FAIL,
                        String.valueOf(resourceId), String.valueOf(policyId), "priceCalendar");
            }

            // 出游人信息
            TicketTravellerAgg travellerAgg = ticketOrderRuleService.fetchTravellerInfo(resourceId, policyId);

            // 核销规则信息
            PolicyUseRule useRuleInfo = policyUseRuleService.queryByPolicyId(resourceId, policyId);

            // 销售渠道列表
            List<PolicyChannelSales> saleChannelList = policyChannelSalesService
                    .queryListByPolicyId(resourceId, policyId);

            return transformEntity(policyBaseInfo, priceList, travellerAgg, useRuleInfo, saleChannelList, timeZone, exchangeRateList).stream()
                    .map(TRTransformResultRo::new)
                    .collect(Collectors.toList());
        } catch (TRTransformException e) {
            return new ArrayList<>(Collections.singletonList(
                    new TRTransformResultRo<>(e.getErrorInfo(), e.getExtendList())
            ));
        }
    }

    /**
     * 转换实体
     *
     * @param policyBaseInfo   政策基础信息
     * @param priceList        政策价格列表
     * @param travellerAgg     出游人信息
     * @param useRuleInfo      核销规则信息
     * @param saleChannelList  销售渠道列表
     * @param timeZone         时区
     * @param exchangeRateList 汇率列表
     * @return 销售渠道列表
     */
    private List<TicketFunTemplateResourceSaleChannelInfo> transformEntity(
            PolicyBaseInfo policyBaseInfo,
            List<PolicyPriceCalendar> priceList,
            TicketTravellerAgg travellerAgg,
            PolicyUseRule useRuleInfo,
            List<PolicyChannelSales> saleChannelList,
            int timeZone,
            List<ExchangeRateInfoDto> exchangeRateList
    ) {
        List<TicketFunTemplateResourceSaleChannelInfo> resultList = new ArrayList<>();

        // 政策渠道
        resultList.addAll(
                getPolicyChannelList(policyBaseInfo, priceList, saleChannelList, timeZone, exchangeRateList)
        );

        // hopegoo渠道
        resultList.addAll(
                getHopegooChannelList(policyBaseInfo, priceList, travellerAgg, useRuleInfo, timeZone, exchangeRateList, saleChannelList)
        );

        return resultList;
    }

    /**
     * 获取政策渠道列表
     *
     * @param policyBaseInfo   政策基础信息
     * @param priceList        政策价格列表
     * @param saleChannelList  政策渠道列表
     * @param timeZone         时区
     * @param exchangeRateList 汇率列表
     * @return 渠道列表
     */
    private List<TicketFunTemplateResourceSaleChannelInfo> getPolicyChannelList(
            PolicyBaseInfo policyBaseInfo,
            List<PolicyPriceCalendar> priceList,
            List<PolicyChannelSales> saleChannelList,
            int timeZone,
            List<ExchangeRateInfoDto> exchangeRateList
    ) {
        try {
            List<TicketFunTemplateResourceSaleChannelInfo> resultList = new ArrayList<>();
            if (CollectionUtils.isEmpty(saleChannelList)) {
                return resultList;
            }
            for (PolicyChannelSales channelSale : saleChannelList) {
                resultList.addAll(
                        getSaleChannelList(
                                policyBaseInfo,
                                priceList,
                                timeZone,
                                SalePointCodeEnum.TCLX.getValue(),
                                channelSale,
                                exchangeRateList
                        )
                );
            }

            return resultList;
        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.SALE_CHANNEL_TRANSFORM_FAIL,
                    resourceId, policyId, "getPolicyChannelList", e.getMessage());
        }
    }

    /**
     * 获取hopegoo渠道列表
     *
     * @param policyBaseInfo   政策基础信息
     * @param priceList        政策价格列表
     * @param travellerAgg     出游人信息
     * @param useRuleInfo      核销规则信息
     * @param timeZone         时区
     * @param exchangeRateList 汇率列表
     * @param saleChannelList  政策渠道列表
     * @return 渠道列表
     */
    private List<TicketFunTemplateResourceSaleChannelInfo> getHopegooChannelList(
            PolicyBaseInfo policyBaseInfo,
            List<PolicyPriceCalendar> priceList,
            TicketTravellerAgg travellerAgg,
            PolicyUseRule useRuleInfo,
            int timeZone,
            List<ExchangeRateInfoDto> exchangeRateList,
            List<PolicyChannelSales> saleChannelList
    ) {
        try {
            List<TicketFunTemplateResourceSaleChannelInfo> resultList = new ArrayList<>();
            if (!isHopegooSale(policyBaseInfo, travellerAgg, useRuleInfo)) {
                return resultList;
            }

            for (PolicyChannelSales channelSale : saleChannelList) {
                resultList.addAll(
                        getSaleChannelList(
                                policyBaseInfo,
                                priceList,
                                timeZone,
                                SalePointCodeEnum.HOPEGOO.getValue(),
                                channelSale,
                                exchangeRateList
                        )
                );
            }

            List<Integer> travellerIdTypeList = travellerAgg.getMultipleOptionList().stream()
                    .filter(i ->
                            Objects.equals(i.getSelectTypeId(), PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())
                    )
                    .map(PolicyMultipleOptions::getSelectValue)
                    .collect(Collectors.toList());
            // 只存在身份证和台胞证，去除ZH_HK
            if (travellerIdTypeList.stream()
                    .allMatch(i -> Objects.equals(TravellerIdCardTypeEnums.Id_Card.getCode(), i)
                            || Objects.equals(TravellerIdCardTypeEnums.TaiWan.getCode(), i)
                    )
            ) {
                resultList = resultList.stream()
                        .filter(i ->
                                !Objects.equals(i.getSalePointCode(), SalePointCodeEnum.HOPEGOO.getValue())
                                        || !Objects.equals(i.getSaleSite(), SaleSiteEnum.ZH_HK.getValue())
                        )
                        .collect(Collectors.toList());
            }
            // 只存在身份证和回乡证，去除en-xx
            if (travellerIdTypeList.stream()
                    .allMatch(i -> Objects.equals(TravellerIdCardTypeEnums.Id_Card.getCode(), i)
                            || Objects.equals(TravellerIdCardTypeEnums.HuiXiang.getCode(), i)
                    )
            ) {
                resultList = resultList.stream()
                        .filter(i ->
                                !Objects.equals(i.getSalePointCode(), SalePointCodeEnum.HOPEGOO.getValue())
                                        || !Objects.equals(i.getSaleSite(), SaleSiteEnum.EN_XX.getValue())
                        )
                        .collect(Collectors.toList());
            }

            return resultList;
        } catch (Exception e) {
            String resourceId = String.valueOf(policyBaseInfo.getResourceId());
            String policyId = String.valueOf(policyBaseInfo.getPolicyId());

            LogUtils.error(LogOperateTypeEnum.OTHER, e, resourceId, policyId);
            throw new TRTransformException(TRTransformException.ErrorInfo.SALE_CHANNEL_TRANSFORM_FAIL,
                    resourceId, policyId, "getHopegooChannelList", e.getMessage());
        }
    }

    /**
     * 获取销售渠道列表
     *
     * @param policyBaseInfo   政策基础信息
     * @param priceList        政策价格列表
     * @param timeZone         时区
     * @param salePointCode    销售终端
     * @param saleChannel      政策渠道信息
     * @param exchangeRateList 汇率列表
     * @return 渠道列表
     */
    private List<TicketFunTemplateResourceSaleChannelInfo> getSaleChannelList(PolicyBaseInfo policyBaseInfo,
                                                                              List<PolicyPriceCalendar> priceList,
                                                                              int timeZone,
                                                                              String salePointCode,
                                                                              PolicyChannelSales saleChannel,
                                                                              List<ExchangeRateInfoDto> exchangeRateList
    ) {
        List<TicketFunTemplateResourceSaleChannelInfo> resultList = new ArrayList<>();
        List<SaleChannelRelation> saleChannelRelationList = getSaleChannelRelationList();

        SaleChannelRelation configInfo = saleChannelRelationList.stream()
                .filter(i -> Objects.equals(i.getSalePointCode(), salePointCode))
                .findFirst()
                .orElse(null);
        if (configInfo == null) {
            return resultList;
        }

        for (SaleChannelRelation.SaleLocalInfo saleLocalInfo : configInfo.getSaleLocalList()) {
            List<SaleChannelRelation.SalePlatformInfo> salePlatformList = saleLocalInfo.getSalePlatformList().stream()
                    .filter(i -> saleChannel == null
                            || i.getPolicyChannelIdList().contains(saleChannel.getChannelID())
                    )
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(salePlatformList)) {
                continue;
            }

            List<String> saleLocalCodeList = saleLocalInfo.getSaleLocalCodeList();
            if (CollectionUtils.isEmpty(saleLocalCodeList)) {
                continue;
            }

            // 区域
            for (String saleLocalCode : saleLocalCodeList) {
                for (SaleChannelRelation.SalePlatformInfo salePlatformInfo : salePlatformList) {
                    List<String> salePlatformCodeList = salePlatformInfo.getSalePlatformCodeList();
                    List<String> saleSceneCodeList = salePlatformInfo.getSaleSceneCodeList();
                    if (CollectionUtils.isEmpty(salePlatformCodeList)) {
                        continue;
                    }

                    // 平台
                    for (String salePlatformCode : salePlatformCodeList) {
                        if (CollectionUtils.isEmpty(saleSceneCodeList)) {
                            // 生成销售渠道实体
                            resultList.add(
                                    generateSaleChannelInfo(
                                            policyBaseInfo,
                                            priceList,
                                            timeZone,
                                            configInfo.getSalePointCode(),
                                            saleLocalCode,
                                            salePlatformCode,
                                            null,
                                            saleChannel,
                                            exchangeRateList
                                    )
                            );
                        } else {
                            // 场景值
                            for (String saleSceneCode : saleSceneCodeList) {
                                // 生成销售渠道实体
                                resultList.add(
                                        generateSaleChannelInfo(
                                                policyBaseInfo,
                                                priceList,
                                                timeZone,
                                                configInfo.getSalePointCode(),
                                                saleLocalCode,
                                                salePlatformCode,
                                                saleSceneCode,
                                                saleChannel,
                                                exchangeRateList
                                        )
                                );
                            }
                        }
                    }
                }
            }
        }

        return resultList;
    }


    /**
     * 生成渠道实体
     *
     * @param policyBaseInfo   政策基础信息
     * @param priceList        政策价格列表
     * @param timeZone         时区
     * @param salePointCode    销售终端code
     * @param saleLocalCode    销售区域code
     * @param salePlatformCode 销售平台code
     * @param saleSceneCode    销售场景code
     * @param exchangeRateList 汇率列表
     * @return 渠道实体
     */
    private TicketFunTemplateResourceSaleChannelInfo generateSaleChannelInfo(
            PolicyBaseInfo policyBaseInfo,
            List<PolicyPriceCalendar> priceList,
            int timeZone,
            String salePointCode,
            String saleLocalCode,
            String salePlatformCode,
            String saleSceneCode,
            PolicyChannelSales saleChannel,
            List<ExchangeRateInfoDto> exchangeRateList
    ) {
        SalePointCodeEnum salePointCodeEnum = enumMapping(SalePointCodeEnum.class, salePointCode);
        SaleSiteEnum saleLocalEnum = enumMapping(SaleSiteEnum.class, saleLocalCode);
        PlatformEnum platformEnum = enumMapping(PlatformEnum.class, salePlatformCode);

        PolicyPriceCalendar policyPriceInfo = priceList.stream()
                .min(Comparator.comparing(PolicyPriceCalendar::getSaleAmount))
                .orElse(null);
        // 渠道加价
        if (saleChannel != null && Objects.equals(saleChannel.getPriceType(), 2)
                && saleChannel.getRevisedAmount().compareTo(BigDecimal.ZERO) != 0
        ) {
            List<PolicyPriceCalendar> copyPriceList = JSON.parseArray(JSON.toJSONString(priceList), PolicyPriceCalendar.class);
            policyPriceInfo = copyPriceList.stream()
                    .peek(i ->
                            i.setSaleAmount(i.getContractAmount().add(saleChannel.getRevisedAmount()))
                    )
                    .min(Comparator.comparing(PolicyPriceCalendar::getSaleAmount))
                    .orElse(null);
        }
        if (policyPriceInfo == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "无价格信息");
        }

        TicketFunTemplateResourceSaleChannelInfo saleChannelInfo = new TicketFunTemplateResourceSaleChannelInfo();
        saleChannelInfo.setSalePointCode(salePointCodeEnum.getValue());
        saleChannelInfo.setSalePointName(salePointCodeEnum.getName());
        saleChannelInfo.setSaleSite(saleLocalEnum.getValue());
        saleChannelInfo.setSalePlatId(platformEnum.getValue());
        saleChannelInfo.setSalePlatName(platformEnum.getName());
        saleChannelInfo.setSaleScene(saleSceneCode);
        saleChannelInfo.setPoiId(String.valueOf(policyBaseInfo.getResourceId()));
        saleChannelInfo.setPackageId(String.valueOf(policyBaseInfo.getPolicyId()));
        // 价格转换
        if (!Objects.equals(policyBaseInfo.getSalePriceCurrencyCode(), CurrencyEnum.CNY.getValue())) {
            saleChannelInfo.setSalePrice_CNY(
                    calculatePriceByRate(
                            policyPriceInfo.getSaleAmount(),
                            policyBaseInfo.getSalePriceCurrencyCode(),
                            CurrencyEnum.CNY.getValue(),
                            exchangeRateList,
                            2
                    )
            );
            saleChannelInfo.setMarketPrice_CNY(
                    calculatePriceByRate(
                            policyPriceInfo.getAmount(),
                            policyBaseInfo.getSalePriceCurrencyCode(),
                            CurrencyEnum.CNY.getValue(),
                            exchangeRateList,
                            2
                    )
            );
        }
        if (!Objects.equals(policyBaseInfo.getBalancePriceCurrencyCode(), CurrencyEnum.CNY.getValue())) {
            saleChannelInfo.setNetPrice_CNY(
                    calculatePriceByRate(
                            policyPriceInfo.getContractAmount(),
                            policyBaseInfo.getBalancePriceCurrencyCode(),
                            CurrencyEnum.CNY.getValue(),
                            exchangeRateList,
                            2
                    )
            );
        }
        saleChannelInfo.setSaleTimeLimit(WhetherEnum.YES.getValue());
        saleChannelInfo.setSaleBeginTime(
                policyBaseInfo.getSaleBeginTime()
                        .toEpochSecond(ZoneOffset.ofHours(timeZone))
        );
        saleChannelInfo.setSaleEndTime(
                policyBaseInfo.getSaleEndTime()
                        .toEpochSecond(ZoneOffset.ofHours(timeZone))
        );

        return saleChannelInfo;
    }

    /**
     * hopegoo渠道是否销售
     *
     * @param policyBaseInfo 政策基础信息
     * @param travellerAgg   出游人信息
     * @param useRuleInfo    核销规则信息
     * @return hopegoo是否销售
     */
    private boolean isHopegooSale(PolicyBaseInfo policyBaseInfo, TicketTravellerAgg travellerAgg, PolicyUseRule useRuleInfo) {
        List<PolicyMultipleOptions> multipleOptionList = travellerAgg.getMultipleOptionList();
        PolicyTravellerBase travellerBase = travellerAgg.getTravellerBase();
        List<PolicyTravellerCrowdLimit> travellerCrowdLimitList = travellerAgg.getTravellerCrowdLimitList();

        // hopegoo渠道支持的政策模式
        // List<Integer> hopegooPolicyModelList = Arrays.asList(BookModeEnum.PolicyMode.Regular_Ticket.getTicketValue(),
        //         BookModeEnum.PolicyMode.Session_Tickets.getTicketValue()
        // );

        // hopegoo渠道支持的入园凭证 AdmissionVoucherEnums
        List<Integer> hopegooVouchersList = Arrays.asList(3, 5, 8, 24);

        boolean hopegooSale = true;

        // 游客信息证件类型只有身份证时， hopegoo渠道不参与销售
        List<Integer> travellerIdTypeList = multipleOptionList.stream()
                .filter(i ->
                        Objects.equals(i.getSelectTypeId(), PolicyMultipleOptionsEnums.TravellerInfoIdType.getCode())
                )
                .map(PolicyMultipleOptions::getSelectValue)
                .collect(Collectors.toList());
        if (Objects.equals(travellerBase.getRequireIdentification(), 1)
                && travellerIdTypeList.stream()
                .allMatch(i -> Objects.equals(i, TravellerIdCardTypeEnums.Id_Card.getCode()))
        ) {
            hopegooSale = false;
        }
        // 政策模式验证
        // else if (!hopegooPolicyModelList.contains(policyBaseInfo.getPolicyMode())) {
        //     hopegooSale = false;
        // }
        // 特殊类型不售卖
        else if (policyBaseInfo.getSpecialTypeId() > 0) {
            hopegooSale = false;
        }
        // 存在人群限制不售卖
        else if (CollectionUtils.isNotEmpty(travellerCrowdLimitList)) {
            hopegooSale = false;
        }

        List<Integer> voucherList = multipleOptionList.stream()
                .filter(i ->
                        Objects.equals(i.getSelectTypeId(), PolicyMultipleOptionsEnums.Vouchers.getCode())
                )
                .map(PolicyMultipleOptions::getSelectValue)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(voucherList)) {
            // 不存在hopegoo渠道支持的入园凭证则不售卖
            if (voucherList.stream()
                    .noneMatch(hopegooVouchersList::contains)
            ) {
                hopegooSale = false;
            }

            // 需要所有凭证且存在不支持的凭证则不售卖
            if (Objects.equals(useRuleInfo.getVoucherAll(), 1)
                    && voucherList.stream()
                    .anyMatch(i -> !hopegooVouchersList.contains(i))
            ) {
                hopegooSale = false;
            }
        }

        return hopegooSale;
    }

    /**
     * 获取渠道关系配置
     */
    private List<SaleChannelRelation> getSaleChannelRelationList() {
        return ConfigUtils.getArray("SaleChannelRelationConfig", "[]", SaleChannelRelation.class);
    }
}
