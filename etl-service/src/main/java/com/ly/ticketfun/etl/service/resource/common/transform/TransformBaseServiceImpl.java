package com.ly.ticketfun.etl.service.resource.common.transform;

import com.ly.localactivity.framework.utils.EnumUtils;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;

/**
 * 模版资源基础服务
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
public abstract class TransformBaseServiceImpl {
    /**
     * 门票枚举映射
     *
     * @param value     值
     * @param enumClass 枚举类
     * @return {@link T}
     */
    public <T> T ticketEnumMapping(Class<T> enumClass, Object value, boolean... throwEx) {
        return enumMapping(enumClass, "ticketValue", value, throwEx.length > 0 ? throwEx[0] : true);
    }

    /**
     * 玩乐枚举映射
     *
     * @param value     值
     * @param enumClass 枚举类
     * @return {@link T}
     */
    public <T> T funEnumMapping(Class<T> enumClass, Object value, boolean... throwEx) {
        return enumMapping(enumClass, "funValue", value, throwEx.length > 0 ? throwEx[0] : true);
    }


    /**
     * 枚举映射
     * 枚举类必须包含value字段
     *
     * @param enumClass 枚举类
     * @param value     值
     * @param throwEx   是否抛出异常
     * @return {@link T }
     */
    public <T> T enumMapping(Class<T> enumClass, Object value, boolean... throwEx) {
        return enumMapping(enumClass, "value", value, throwEx.length == 0 || throwEx[0]);
    }

    /**
     * 枚举映射
     *
     * @param value     值
     * @param enumClass 枚举类
     * @return {@link T}
     */
    private <T> T enumMapping(Class<T> enumClass, String valueField, Object value, boolean throwEx) {
        T result = EnumUtils.getBy(value, enumClass, valueField);
        if (result == null && throwEx) {
            throw new TRTransformException(TRTransformException.ErrorInfo.ENUM_NOT_MAPPING, enumClass.getSimpleName(), String.valueOf(value));
        }
        return result;
    }

    /**
     * 获取错误结果实体
     * 依次 productId、packageId、skuId
     *
     * @param transformResultRo 变换结果ro
     * @return {@link TRTransformResultRo }<{@link T }>
     */
    protected <T> TRTransformResultRo<T> getTransformResultRo(TRTransformResultRo<T> transformResultRo, String... ids) {
        if (transformResultRo == null) {
            transformResultRo = new TRTransformResultRo<T>();
        }
        if (ids != null && ids.length > 0) {
            transformResultRo.setProductId(ids[0]);
            if (ids.length > 1) {
                transformResultRo.setPackageId(ids[1]);
                if (ids.length > 2) {
                    transformResultRo.setSkuId(ids[2]);
                }
            }
        }
        return transformResultRo;
    }

}
