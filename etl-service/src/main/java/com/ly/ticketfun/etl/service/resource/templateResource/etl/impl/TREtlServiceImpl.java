package com.ly.ticketfun.etl.service.resource.templateResource.etl.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.model.redis.RedisCascadeLock;
import com.ly.localactivity.framework.utils.EnumUtils;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.constant.RedisConstant;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourcePackageService;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceProductService;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceSkuService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.etl.ITREtlService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunPackageTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunProductTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunSkuPriceTransformServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模版资源Etl服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class TREtlServiceImpl implements ITREtlService {
    @Resource
    private FunProductTransformServiceImpl funProductTransformService;
    @Resource
    private FunPackageTransformServiceImpl funPackageTransformService;
    @Resource
    private FunSkuPriceTransformServiceImpl funSkuPriceTransformService;
    @Resource
    private ITicketFunTemplateResourceProductService templateResourceProductService;
    @Resource
    private ITicketFunTemplateResourcePackageService templateResourcePackageService;
    @Resource
    private ITicketFunTemplateResourceSkuService templateResourceSkuService;

    @Resource
    private ITRTransformService<TicketFunTemplateResourceSkuInfo> ticketSkuPriceTransformService;

    /**
     * 门票资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    public Boolean ticketResourceChange(TicketFunResourceChangeRo changeRo) {
        try {
            // -------------- 校验请求参数 --------------
            if (!checkChangeRo(changeRo)) {
                return false;
            }
            // -------------- 数据异构 --------------

            // -------------- 数据保存 --------------

            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 玩乐资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    public Boolean funResourceChange(TicketFunResourceChangeRo changeRo) {
        try {
            // -------------- 校验请求参数 --------------
            if (!checkChangeRo(changeRo)) {
                return false;
            }
            // -------------- 数据异构及保存 --------------
            for (String category : changeRo.getCategories()) {
                MQEnum.TicketFunResourceChangeCategory changeCategory = EnumUtils.getByValue(category, MQEnum.TicketFunResourceChangeCategory.class);
                if (changeCategory == null) {
                    continue;
                }
                switch (changeCategory) {
                    case PRODUCT_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, 30000L, changeRo.getProductId())){
                            if (redisCascadeLock.lock()){
                                // -------------- 产品数据异构 --------------
                                TRTransformResultRo<TicketFunTemplateResourceProductInfo> productTransformResultRo = funProductTransformService.transformData(changeRo, changeCategory);
                                // -------------- 产品异构结果处理 --------------
                                productChangeEvent(changeRo, changeCategory, productTransformResultRo);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐产品变更同步 无需重复执行, category:{0}, productId:{1}", changeCategory.getValue(), changeRo.getProductId()));
                            }
                        }
                        break;
                    case PACKAGE_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, 30000L, changeRo.getProductId(), changeRo.getPackageId())){
                            if (redisCascadeLock.lock()){
                                // -------------- 套餐数据异构 --------------
                                List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> packageTransformResultRoList = funPackageTransformService.transformDataList(changeRo, changeCategory);
                                // -------------- 套餐异构结果处理 --------------
                                packageChangeEvent(changeRo, changeCategory, packageTransformResultRoList);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐套餐变更同步 无需重复执行, category:{0}, packageId:{1}, productId:{2}", changeCategory.getValue(), changeRo.getProductId(), changeRo.getPackageId()));
                            }
                        }
                        break;
                    case SKU_PRICE_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, 30000L, changeRo.getProductId(), changeRo.getPackageId(), changeRo.getSkuId())){
                            if (redisCascadeLock.lock()){
                                // -------------- SKU数据异构 --------------
                                List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> skuPriceTransformResultRoList = funSkuPriceTransformService.transformDataList(changeRo, changeCategory);
                                // -------------- SKU异构结果处理 --------------
                                skuPriceChangeEvent(changeRo, changeCategory, skuPriceTransformResultRoList);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐SKU变更同步 无需重复执行, category:{0}, packageId:{1}, productId:{2}, skuId:{3}", changeCategory.getValue(), changeRo.getProductId(), changeRo.getPackageId(), changeRo.getSkuId()));
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
            return true;
        } catch (TRTransformException ex) {
            String errorMsg = "TRTransformException: errorCode=" + ex.getErrorInfo().getCode() + "msg=" + MessageFormat.format(ex.getErrorInfo().getMsg(), ex.getExtendList());
//            todo
            LogUtils.error(LogOperateTypeEnum.OTHER, new RuntimeException(errorMsg));
            return false;
        } catch (Exception ex) {
            LogUtils.error(LogOperateTypeEnum.OTHER, ex);
            return false;
        }
    }

    /**
     * 产品变更事件
     *
     * @param changeRo          更改ro
     * @param changeCategory    更改类别
     * @param transformResultRo 转换结果ro
     */
    private void productChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformResultRo) {
        if (transformResultRo.getIsSuccess()) {
            // -------------- 转换成功 --------------
            onProductOnLine(transformResultRo);
            doOnlineLog(transformResultRo.getProductId(), null, null, "产品");
        } else {
            // -------------- 转换失败，产品下架 --------------
            completelyOfflineProduct(changeRo.getProductId(), transformResultRo, "产品");
        }
    }

    /**
     * 完整下架产品 及其套餐、sku等
     * @param productId
     * @param transformResultRo
     */
    private void completelyOfflineProduct(String productId, TRTransformResultRo<?> transformResultRo, String subTitle) {
        if (StringUtils.isEmpty(productId)){
            return;
        }

        List<TicketFunTemplateResourceProductInfo> offlineProductList = onProductOffline(productId);
        if (CollectionUtil.isNotEmpty(offlineProductList)) {
            offlineProductList.forEach(offlineProduct -> {
                doOfflineLog(productId, null, null, subTitle, transformResultRo);
            });
        }
        // -------------- 转换失败，套餐下架 --------------
        List<TicketFunTemplateResourcePackageInfo> offliinePackageList = onPackageOffline(productId, null, null);
        if (CollectionUtil.isNotEmpty(offliinePackageList)) {
            offliinePackageList.forEach(offlinePackage -> {
                doOfflineLog(offlinePackage.getProductId(), offlinePackage.getPackageId(), null, subTitle, transformResultRo);
            });
        }
        // -------------- 转换失败，Sku下架 --------------
        List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline(productId, null , null, null);
        if (CollectionUtil.isNotEmpty(offlineSkuList)) {
            offlineSkuList.forEach(offlineSku -> {
                doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), subTitle, transformResultRo);
            });
        }
    }

    /**
     * 完整下架套餐 及其sku等
     * @param productId
     * @param offlinePackageResultMap
     */
    private void completelyOfflinePackage(String productId, String packageId, List<String> onSalePackageIdList, Map<String, TRTransformResultRo<?>> offlinePackageResultMap, String subTitle) {
        if (StringUtils.isEmpty(productId)){
            return;
        }
        List<String> packageIdList = StringUtils.isNotEmpty(packageId) ? Collections.singletonList(packageId) : null;

        // -------------- 转换失败，套餐下架 --------------
        List<TicketFunTemplateResourcePackageInfo> offliinePackageList = onPackageOffline(productId, packageIdList, onSalePackageIdList);
        if (CollectionUtil.isNotEmpty(offliinePackageList)) {
            offliinePackageList.forEach(offlinePackage -> {
                TRTransformResultRo<?> transformResultRo = offlinePackageResultMap.getOrDefault(offlinePackage.getPackageId(), new TRTransformResultRo<>(TRTransformException.ErrorInfo.PACKAGE_NOT_ON_SALE, productId, offlinePackage.getPackageId()));
                doOfflineLog(offlinePackage.getProductId(), offlinePackage.getPackageId(), null, subTitle, transformResultRo);
            });

            // -------------- 转换失败，Sku下架 --------------
            List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline(productId, offliinePackageList.stream().map(TicketFunTemplateResourcePackageInfo::getPackageId).collect(Collectors.toList()), null, null);
            if (CollectionUtil.isNotEmpty(offlineSkuList)) {
                offlineSkuList.forEach(offlineSku -> {
                    TRTransformResultRo<?> transformResultRo = offlinePackageResultMap.getOrDefault(offlineSku.getPackageId(), new TRTransformResultRo<>(TRTransformException.ErrorInfo.PACKAGE_NOT_ON_SALE, productId, offlineSku.getPackageId()));
                    doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), subTitle, transformResultRo);
                });
            }
        }

    }

    /**
     * 套餐变更
     *
     * @param changeRo              更改ro
     * @param changeCategory        更改类别
     * @param transformResultRoList 转换结果ro
     */
    private void packageChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> transformResultRoList) {
        // 上架套餐
        List<TicketFunTemplateResourcePackageInfo> onLinePackageList = new ArrayList<>();
        // 下架套餐结果映射 packageId->TRTransformResultRo
        Map<String, TRTransformResultRo<?>> offlinePackageResultMap = new HashMap<>();
        // -------------- 转换结果处理 --------------
        for (TRTransformResultRo<TicketFunTemplateResourcePackageInfo> transformResultRo : transformResultRoList) {
            if (transformResultRo.getIsSuccess()) {
                // -------------- 转换成功 --------------
                onLinePackageList.add(transformResultRo.getData());
            } else {
                // -------------- 转换失败 --------------
                // -------------- 产品失败 则下架产品 退出 --------------
                if (transformResultRo.getPackageId() == null){
                    completelyOfflineProduct(changeRo.getProductId(), transformResultRo, "套餐");
                    return;
                }else{
                    offlinePackageResultMap.put(transformResultRo.getPackageId(), transformResultRo);
                }
            }
        }
        // -------------- 套餐上架 --------------
        if (CollectionUtil.isNotEmpty(onLinePackageList)) {
            onPackageOnline(changeRo, onLinePackageList);
            onLinePackageList.forEach(onLinePackage -> {
                doOnlineLog(onLinePackage.getProductId(), onLinePackage.getPackageId(), null, "套餐");
            });
        }

        // -------------- 套餐下架 --------------
        completelyOfflinePackage(changeRo.getProductId(), changeRo.getPackageId(), onLinePackageList.stream().map(TicketFunTemplateResourcePackageInfo::getPackageId).collect(Collectors.toList()), offlinePackageResultMap, "套餐");
    }

    /**
     * sku价格变更
     *
     * @param changeRo              更改ro
     * @param changeCategory        更改类别
     * @param transformResultRoList 转换结果ro
     */
    private void skuPriceChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformResultRoList) {
        // 上架sku
        List<TicketFunTemplateResourceSkuInfo> onlineSkuList = new ArrayList<>();
        // 下架skuId
        List<String> offlineSkuIdlist = new ArrayList<>();
        // 下架sku结果映射 skuId->TRTransformResultRo
        Map<String, TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> offlineSkuResultMap = new HashMap<>();
        // -------------- 转换结果处理 --------------
        for (TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformResultRo : transformResultRoList) {
            if (transformResultRo.getIsSuccess()) {
                // -------------- 转换成功 --------------
                onlineSkuList.add(transformResultRo.getData());
            } else {
                // -------------- 转换失败 --------------
                if (transformResultRo.getSkuId() == null ){
                    if (transformResultRo.getPackageId() == null){
                        // -------------- 产品失败 则下架产品 退出 --------------
                        completelyOfflineProduct(changeRo.getProductId(), transformResultRo, "SKU");
                        return;
                    }else{
                        // -------------- 套餐失败 则下架套餐--------------
                        completelyOfflinePackage(changeRo.getProductId(), transformResultRo.getPackageId(), null, Collections.singletonMap(transformResultRo.getPackageId(), transformResultRo), "套餐");
                    }
                }else {
                    offlineSkuIdlist.add(transformResultRo.getSkuId());
                    offlineSkuResultMap.put(transformResultRo.getSkuId(), transformResultRo);
                }
            }
        }
        // -------------- sku上架 --------------
        if (CollectionUtil.isNotEmpty(onlineSkuList)) {
            onSkuOnline(changeRo, onlineSkuList);
        }
        // -------------- sku下架 --------------
        if (CollectionUtil.isNotEmpty(offlineSkuIdlist)) {
            // -------------- 转换失败，Sku下架 --------------
            List<String> packageIdList = StringUtils.isNotEmpty(changeRo.getPackageId()) ? Collections.singletonList(changeRo.getPackageId()) : null;
            List<String> skuIdList = StringUtils.isNotEmpty(changeRo.getSkuId()) ? Collections.singletonList(changeRo.getSkuId()) : null;
            List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline(changeRo.getProductId(), packageIdList, skuIdList, onlineSkuList.stream().map(TicketFunTemplateResourceSkuInfo::getSkuId).collect(Collectors.toList()));
            if (CollectionUtil.isNotEmpty(offlineSkuList)) {
                offlineSkuList.forEach(offlineSku -> {
                    TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformResultRo = offlineSkuResultMap.get(offlineSku.getSkuId());
                    doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), "SKU", transformResultRo);
                });
            }
        }
    }

    /**
     * 产品上架
     *
     * @param transformResultRo 结果ro
     */
    private void onProductOnLine(TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformResultRo) {
        // -------------- 产品上架 --------------
        TicketFunTemplateResourceProductInfo productInfo = transformResultRo.getData();
        // -------------- 根据语言匹配索引后缀 --------------
        LocaleEnum localeEnum = EnumUtils.getByValue(productInfo.getLocale(), LocaleEnum.class);
        if (localeEnum == null){
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_LOCALE_FAIL, productInfo.getProductId());
        }

        ESEnum.IndexSuffix indexSuffix = EnumUtils.getBy(localeEnum, ESEnum.IndexSuffix.class, "locale");
        if (indexSuffix == null){
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_LOCALE_FAIL, productInfo.getProductId());
        }

        productInfo.setDataFlag(WhetherEnum.YES.getValue());
        TicketFunTemplateResourceProductInfo oldProductInfo = templateResourceProductService.queryByProductId(productInfo.getProductId(), indexSuffix.getValue());
        Boolean result = templateResourceProductService.saveOrUpdate(oldProductInfo, productInfo, indexSuffix.getValue());
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_ONLINE_FAIL, productInfo.getProductId());
        }
    }

    /**
     * 产品下架
     *
     * @param productId 产品id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    private List<TicketFunTemplateResourceProductInfo> onProductOffline(String productId) {
        if (StringUtils.isEmpty(productId)){
            return null;
        }

        List<TicketFunTemplateResourceProductInfo> ticketFunTemplateResourceProductInfos = new ArrayList<>();
        for (ESEnum.IndexSuffix indexSuffix : ESEnum.IndexSuffix.values()) {
            TicketFunTemplateResourceProductInfo ticketFunTemplateResourceProductInfo = onProductOffline(productId, indexSuffix.getValue());
            if (ticketFunTemplateResourceProductInfo != null){
                ticketFunTemplateResourceProductInfos.add(ticketFunTemplateResourceProductInfo);
            }
        }

        if (CollectionUtil.isEmpty(ticketFunTemplateResourceProductInfos)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_OFFLINE_FAIL);
        }
        return ticketFunTemplateResourceProductInfos;
    }

    /**
     * 产品下架
     *
     * @param productId 产品id
     * @param indexSuffix
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    private TicketFunTemplateResourceProductInfo onProductOffline(String productId, String indexSuffix) {
        if (StringUtils.isEmpty(productId)){
            return null;
        }
        // -------------- 产品下架 --------------
        TicketFunTemplateResourceProductInfo oldProductInfo = templateResourceProductService.queryByProductId(productId, indexSuffix);
        if (oldProductInfo == null) {
            return null;
        }

        Boolean result = templateResourceProductService.saveOrUpdate(oldProductInfo, null, indexSuffix);
        if (!result) {
            return null;
        }
        return oldProductInfo;
    }

    /**
     * 套餐上架
     *
     * @param changeRo    更改ro
     * @param packageList 套餐列表
     */
    private void onPackageOnline(TicketFunResourceChangeRo changeRo, List<TicketFunTemplateResourcePackageInfo> packageList) {
        if (CollectionUtil.isEmpty(packageList)){
            return;
        }

        // -------------- 根据语言匹配索引后缀 --------------
        Map<String, List<TicketFunTemplateResourcePackageInfo>> localePackMap = packageList.stream().collect(Collectors.groupingBy(TicketFunTemplateResourcePackageInfo::getLocale));
        for (Map.Entry<String, List<TicketFunTemplateResourcePackageInfo>> localeEntry : localePackMap.entrySet()) {
            String locale = localeEntry.getKey();
            List<TicketFunTemplateResourcePackageInfo> curPackageList = localeEntry.getValue();

            LocaleEnum localeEnum = EnumUtils.getByValue(locale, LocaleEnum.class);
            if (localeEnum == null){
                throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_LOCALE_FAIL, changeRo.getProductId());
            }

            ESEnum.IndexSuffix indexSuffix = EnumUtils.getBy(localeEnum, ESEnum.IndexSuffix.class, "locale");
            if (indexSuffix == null){
                throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_LOCALE_FAIL, changeRo.getProductId());
            }

            List<String> packageIdList = curPackageList.stream().map(TicketFunTemplateResourcePackageInfo::getPackageId).collect(Collectors.toList());
            // -------------- 套餐上架 --------------
            List<TicketFunTemplateResourcePackageInfo> oldPackageList = templateResourcePackageService.query(changeRo.getProductId(), null, packageIdList, indexSuffix.getValue());
            curPackageList.forEach(pack -> pack.setDataFlag(WhetherEnum.YES.getValue()));
            Boolean result = templateResourcePackageService.saveOrUpdate(oldPackageList, curPackageList, indexSuffix.getValue());
            if (!result) {
                throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_ONLINE_FAIL);
            }
        }

    }

    /**
     * 套餐下架
     *
     * @param productId     产品id
     * @param packageIdList 套餐id
     * @param onSalePackageIdList
     */
    private List<TicketFunTemplateResourcePackageInfo> onPackageOffline(String productId, List<String> packageIdList, List<String> onSalePackageIdList) {
        if (StringUtils.isEmpty(productId)){
            return null;
        }

        List<TicketFunTemplateResourcePackageInfo> oldPackageList = new ArrayList<>();
        for (ESEnum.IndexSuffix indexSuffix : ESEnum.IndexSuffix.values()) {
            // -------------- 套餐下架 --------------
            List<TicketFunTemplateResourcePackageInfo> curOldPackageList = onPackageOffline(productId, packageIdList, onSalePackageIdList, indexSuffix.getValue());
            if (CollectionUtil.isNotEmpty(curOldPackageList)) {
                oldPackageList.addAll(curOldPackageList);
            }
        }

        if (CollectionUtil.isEmpty(oldPackageList)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_OFFLINE_FAIL);
        }

        return oldPackageList;
    }

    /**
     * 套餐下架
     *
     * @param productId     产品id
     * @param packageIdList 套餐id
     */
    private List<TicketFunTemplateResourcePackageInfo> onPackageOffline(String productId, List<String> packageIdList, List<String> onSalePackageIdList, String indexSuffix) {
        if (StringUtils.isEmpty(productId)){
            return null;
        }
        // -------------- 套餐下架 --------------
        List<TicketFunTemplateResourcePackageInfo> oldPackageList = templateResourcePackageService.query(productId, null, packageIdList, indexSuffix);
        if (CollectionUtils.isEmpty(oldPackageList)) {
            return null;
        }

        List<TicketFunTemplateResourcePackageInfo> needOfflinePackageList = oldPackageList.stream().filter(info -> CollectionUtil.isEmpty(onSalePackageIdList) || !onSalePackageIdList.contains(info.getPackageId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needOfflinePackageList)) {
            return null;
        }

        Boolean result = templateResourcePackageService.saveOrUpdate(needOfflinePackageList, null, indexSuffix);
        if (!result) {
            return null;
        }
        return needOfflinePackageList;
    }

    /**
     * sku上架
     *
     * @param changeRo 更改ro
     * @param skuList  sku列表
     */
    private void onSkuOnline(TicketFunResourceChangeRo changeRo, List<TicketFunTemplateResourceSkuInfo> skuList) {
        if (CollectionUtil.isEmpty(skuList)){
            return;
        }
        // -------------- 根据语言匹配索引后缀 --------------
        Map<String, List<TicketFunTemplateResourceSkuInfo>> localeSkuMap = skuList.stream().collect(Collectors.groupingBy(TicketFunTemplateResourceSkuInfo::getLocale));
        for (Map.Entry<String, List<TicketFunTemplateResourceSkuInfo>> localeEntry : localeSkuMap.entrySet()) {
            String locale = localeEntry.getKey();
            List<TicketFunTemplateResourceSkuInfo> curSkuList = localeEntry.getValue();
            LocaleEnum localeEnum = EnumUtils.getByValue(locale, LocaleEnum.class);
            if (localeEnum == null){
                throw new TRTransformException(TRTransformException.ErrorInfo.SKU_LOCALE_FAIL, changeRo.getProductId());
            }

            ESEnum.IndexSuffix indexSuffix = EnumUtils.getBy(localeEnum, ESEnum.IndexSuffix.class, "locale");
            if (indexSuffix == null){
                throw new TRTransformException(TRTransformException.ErrorInfo.SKU_LOCALE_FAIL, changeRo.getProductId());
            }

            List<String> skuIdList = curSkuList.stream().map(TicketFunTemplateResourceSkuInfo::getSkuId).collect(Collectors.toList());
            // -------------- SKU上架 --------------
            List<TicketFunTemplateResourceSkuInfo> oldSkuList = templateResourceSkuService.query(changeRo.getProductId(), changeRo.getPackageId(), null, null, skuIdList, indexSuffix.getValue());
            curSkuList.forEach(sku -> sku.setDataFlag(WhetherEnum.YES.getValue()));
            Boolean result = templateResourceSkuService.saveOrUpdate(oldSkuList, curSkuList, indexSuffix.getValue());
            if (!result) {
                throw new TRTransformException(TRTransformException.ErrorInfo.SKU_ONLINE_FAIL);
            }
        }

    }

    /**
     * Sku下架
     *
     * @param productId     产品id
     * @param packageIdList 包id列表
     * @param skuIdList     sku id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    private List<TicketFunTemplateResourceSkuInfo> onSkuOffline(String productId, List<String> packageIdList, List<String> skuIdList, List<String> onSaleSkuIdList) {
        if (StringUtils.isEmpty(productId) && CollectionUtil.isEmpty(packageIdList) && CollectionUtil.isEmpty(skuIdList)){
            return null;
        }

        List<TicketFunTemplateResourceSkuInfo> oldSkuList = new ArrayList<>();
        for (ESEnum.IndexSuffix indexSuffix : ESEnum.IndexSuffix.values()) {
            // -------------- 套餐下架 --------------
            List<TicketFunTemplateResourceSkuInfo> curOldSkuList = onSkuOffline(productId, packageIdList, skuIdList, onSaleSkuIdList, indexSuffix.getValue());
            if (CollectionUtil.isNotEmpty(curOldSkuList)) {
                oldSkuList.addAll(curOldSkuList);
            }

        }

        if (CollectionUtil.isEmpty(oldSkuList)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.SKU_OFFLINE_FAIL);
        }
        return oldSkuList;
    }/**
     * Sku下架
     *
     * @param productId     产品id 查询历史数据
     * @param packageIdList 包id列表 查询历史数据
     * @param skuIdList     sku id列表 查询历史数据
     * @param onSaleSkuIdList 排除操作列表
     *
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    private List<TicketFunTemplateResourceSkuInfo> onSkuOffline(String productId, List<String> packageIdList, List<String> skuIdList, List<String> onSaleSkuIdList, String indexSuffix) {
        if (StringUtils.isEmpty(productId) && CollectionUtil.isEmpty(packageIdList) && CollectionUtil.isEmpty(skuIdList)){
            return null;
        }

        // -------------- SKU下架 --------------
        List<TicketFunTemplateResourceSkuInfo> oldSkuList = templateResourceSkuService.query(productId, null, packageIdList, null, skuIdList, indexSuffix);
        if (CollectionUtils.isEmpty(oldSkuList)) {
            return null;
        }

        List<TicketFunTemplateResourceSkuInfo> needOfflineSkuList = oldSkuList.stream().filter(sku -> CollectionUtil.isEmpty(onSaleSkuIdList) || !onSaleSkuIdList.contains(sku.getSkuId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(needOfflineSkuList)){
            return null;
        }

        Boolean result = templateResourceSkuService.saveOrUpdate(needOfflineSkuList, null, indexSuffix);
        if (!result) {
            return null;
        }
        return needOfflineSkuList;
    }

    /**
     * 检查变更参数
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    private Boolean checkChangeRo(TicketFunResourceChangeRo changeRo) {
        if (changeRo == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(changeRo.getCategories())) {
            return false;
        }
        return true;
    }

    /**
     * 上架日志
     *
     * @param productId 产品id
     * @param packageId 套餐id
     * @param skuId     sku id
     * @param subTitle  副标题
     */
    private void doOnlineLog(String productId, String packageId, String skuId, String subTitle) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库产品转换成功【%s】,", subTitle));
        if (StringUtils.isNotEmpty(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU上架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.isNotEmpty(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐上架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品上架", productId));
        }
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }

    /**
     * 上架失败日志
     *
     * @param productId 产品id
     * @param packageId 套餐id
     * @param skuId     sku id
     * @param subTitle  副标题
     */
    private void doOnlineFailLog(String productId, String packageId, String skuId, String subTitle) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库产品转换成功，上架失败【%s】,", subTitle));
        if (StringUtils.isNotEmpty(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU上架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.isNotEmpty(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐上架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品上架", productId));
        }
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }

    /**
     * 下架日志
     *
     * @param productId         产品id
     * @param packageId         套餐id
     * @param skuId             sku id
     * @param subTitle          副标题
     * @param transformResultRo 变换结果ro
     */
    private void doOfflineLog(String productId, String packageId, String skuId, String subTitle, TRTransformResultRo<?> transformResultRo) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库转换失败【%s】,", subTitle));
        if (StringUtils.isNotEmpty(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU下架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.isNotEmpty(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐下架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品下架", productId));
        }
        logMessage.append(String.format("errCode：%s, errMsg：%s。", transformResultRo.getErrCode(), transformResultRo.getErrMsg()));
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }
}
