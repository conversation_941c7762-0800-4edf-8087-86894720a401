package com.ly.ticketfun.etl.service.resource.templateResource.etl.impl;

import com.ly.localactivity.framework.enums.LogOperateTypeEnum;
import com.ly.localactivity.framework.model.redis.RedisCascadeLock;
import com.ly.localactivity.framework.utils.EnumUtils;
import com.ly.localactivity.framework.utils.log.LogUtils;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.constant.RedisConstant;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourcePackageService;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceProductService;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceSkuService;
import com.ly.ticketfun.etl.dataService.templateresource.impl.TicketFunTemplateResourceSkuInfoServiceImpl;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.etl.ITREtlService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunPackageTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunProductTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunSkuPriceTransformServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * 模版资源Etl服务
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Service
public class TREtlServiceImpl implements ITREtlService {
    @Resource
    private FunProductTransformServiceImpl funProductTransformService;
    @Resource
    private FunPackageTransformServiceImpl funPackageTransformService;
    @Resource
    private FunSkuPriceTransformServiceImpl funSkuPriceTransformService;
    @Resource
    private ITicketFunTemplateResourceProductService templateResourceProductService;
    @Resource
    private ITicketFunTemplateResourcePackageService templateResourcePackageService;
    @Resource
    private ITicketFunTemplateResourceSkuService templateResourceSkuService;

    @Resource
    private ITRTransformService<TicketFunTemplateResourceSkuInfo> ticketSkuPriceTransformService;

    /**
     * 门票资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    public Boolean ticketResourceChange(TicketFunResourceChangeRo changeRo) {
        try {
            // -------------- 校验请求参数 --------------
            if (!checkChangeRo(changeRo)) {
                return false;
            }
            // -------------- 数据异构 --------------

            // -------------- 数据保存 --------------

            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 玩乐资源变更
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    public Boolean funResourceChange(TicketFunResourceChangeRo changeRo) {
        try {
            // -------------- 校验请求参数 --------------
            if (!checkChangeRo(changeRo)) {
                return false;
            }
            // -------------- 数据异构及保存 --------------
            for (String category : changeRo.getCategories()) {
                MQEnum.TicketFunResourceChangeCategory changeCategory = EnumUtils.getByValue(category, MQEnum.TicketFunResourceChangeCategory.class);
                if (changeCategory == null) {
                    continue;
                }
                switch (changeCategory) {
                    case PRODUCT_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, changeRo.getProductId())){
                            if (redisCascadeLock.lock()){
                                // -------------- 产品数据异构 --------------
                                TRTransformResultRo<TicketFunTemplateResourceProductInfo> productTransformResultRo = funProductTransformService.transformData(changeRo, changeCategory);
                                // -------------- 产品异构结果处理 --------------
                                productChangeEvent(changeRo, changeCategory, productTransformResultRo);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐产品变更同步 无需重复执行, category:{0}, productId:{1}", changeCategory.getValue(), changeRo.getProductId()));
                            }
                        }

                        break;
                    case PACKAGE_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, changeRo.getProductId(), changeRo.getPackageId())){
                            if (redisCascadeLock.lock()){
                                // -------------- 套餐数据异构 --------------
                                List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> packageTransformResultRoList = funPackageTransformService.transformDataList(changeRo, changeCategory);
                                // -------------- 套餐异构结果处理 --------------
                                packageChangeEvent(changeRo, changeCategory, packageTransformResultRoList);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐套餐变更同步 无需重复执行, category:{0}, packageId:{1}, productId:{2}", changeCategory.getValue(), changeRo.getProductId(), changeRo.getPackageId()));
                            }
                        }

                        break;
                    case SKU_PRICE_CHANGE:
                        try (RedisCascadeLock redisCascadeLock = new RedisCascadeLock(RedisConstant.FUN_PRODUCT_CHANGE_LOCK_KEY, 180000L, changeRo.getProductId(), changeRo.getPackageId(), changeRo.getSkuId())){
                            if (redisCascadeLock.lock()){
                                // -------------- SKU数据异构 --------------
                                List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> skuPriceTransformResultRoList = funSkuPriceTransformService.transformDataList(changeRo, changeCategory);
                                // -------------- SKU异构结果处理 --------------
                                skuPriceChangeEvent(changeRo, changeCategory, skuPriceTransformResultRoList);
                            }else {
                                LogUtils.info(LogOperateTypeEnum.OTHER, MessageFormat.format("玩乐套餐变更同步 无需重复执行, category:{0}, packageId:{1}, productId:{2}, skuId:{3}", changeCategory.getValue(), changeRo.getProductId(), changeRo.getPackageId(), changeRo.getSkuId()));
                            }
                        }

                        break;
                    default:
                        break;
                }
            }
            return true;
        } catch (TRTransformException ex) {
            // toDO 转换失败处理
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 产品变更事件
     *
     * @param changeRo          更改ro
     * @param changeCategory    更改类别
     * @param transformResultRo 转换结果ro
     */
    private void productChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformResultRo) {
        if (transformResultRo.getIsSuccess()) {
            // -------------- 转换成功 --------------
            onProductOnLine(transformResultRo);
            doOnlineLog(transformResultRo.getProductId(), null, null, "产品");
        } else {
            // -------------- 转换失败，产品下架 --------------
            TicketFunTemplateResourceProductInfo offlineProduct = onProductOffline(transformResultRo.getProductId());
            if (offlineProduct != null) {
                doOfflineLog(transformResultRo.getProductId(), null, null, "产品", transformResultRo);
            }
            // -------------- 转换失败，套餐下架 --------------
            List<TicketFunTemplateResourcePackageInfo> offliinePackageList = onPackageOffline(transformResultRo.getProductId(), null);
            if (!CollectionUtils.isEmpty(offliinePackageList)) {
                offliinePackageList.forEach(offlinePackage -> {
                    doOfflineLog(offlinePackage.getProductId(), offlinePackage.getPackageId(), null, "产品", transformResultRo);
                });
            }
            // -------------- 转换失败，Sku下架 --------------
            List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline(transformResultRo.getProductId(), null, null);
            if (!CollectionUtils.isEmpty(offlineSkuList)) {
                offlineSkuList.forEach(offlineSku -> {
                    doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), "产品", transformResultRo);
                });
            }
        }
    }

    /**
     * 套餐变更
     *
     * @param changeRo              更改ro
     * @param changeCategory        更改类别
     * @param transformResultRoList 转换结果ro
     */
    private void packageChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> transformResultRoList) {
        // 上架套餐
        List<TicketFunTemplateResourcePackageInfo> onLinePackageList = new ArrayList<>();
        // 下架套餐Id
        List<String> offlinePackageIdlist = new ArrayList<>();
        // 下架套餐结果映射 packageId->TRTransformResultRo
        Map<String, TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> offlinePackageResultMap = new HashMap<>();
        // -------------- 转换结果处理 --------------
        for (TRTransformResultRo<TicketFunTemplateResourcePackageInfo> transformResultRo : transformResultRoList) {
            if (transformResultRo.getIsSuccess()) {
                // -------------- 转换成功 --------------
                onLinePackageList.add(transformResultRo.getData());
            } else {
                // -------------- 转换失败 --------------
                offlinePackageIdlist.add(transformResultRo.getPackageId());
                offlinePackageResultMap.put(transformResultRo.getPackageId(), transformResultRo);
            }
        }
        // -------------- 套餐上架 --------------
        if (!CollectionUtils.isEmpty(onLinePackageList)) {
            onPackageOnline(changeRo, onLinePackageList);
            onLinePackageList.forEach(onLinePackage -> {
                doOnlineLog(onLinePackage.getProductId(), onLinePackage.getPackageId(), null, "产品");
            });
        }
        // -------------- 套餐下架 --------------
        if (!CollectionUtils.isEmpty(offlinePackageIdlist)) {
            String productId = changeRo.getProductId();
            // -------------- 转换失败，套餐下架 --------------
            List<TicketFunTemplateResourcePackageInfo> offliinePackageList = onPackageOffline(productId, offlinePackageIdlist);
            if (!CollectionUtils.isEmpty(offliinePackageList)) {
                offliinePackageList.forEach(offlinePackage -> {
                    TRTransformResultRo<TicketFunTemplateResourcePackageInfo> transformResultRo = offlinePackageResultMap.get(offlinePackage.getPackageId());
                    doOfflineLog(offlinePackage.getProductId(), offlinePackage.getPackageId(), null, "套餐", transformResultRo);
                });
            }
            // -------------- 转换失败，Sku下架 --------------
            List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline("", offlinePackageIdlist, null);
            if (!CollectionUtils.isEmpty(offlineSkuList)) {
                offlineSkuList.forEach(offlineSku -> {
                    TRTransformResultRo<TicketFunTemplateResourcePackageInfo> transformResultRo = offlinePackageResultMap.get(offlineSku.getPackageId());
                    doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), "套餐", transformResultRo);
                });
            }
        }
    }

    /**
     * sku价格变更
     *
     * @param changeRo              更改ro
     * @param changeCategory        更改类别
     * @param transformResultRoList 转换结果ro
     */
    private void skuPriceChangeEvent(TicketFunResourceChangeRo changeRo, MQEnum.TicketFunResourceChangeCategory changeCategory, List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> transformResultRoList) {
        // 上架sku
        List<TicketFunTemplateResourceSkuInfo> onlineSkuList = new ArrayList<>();
        // 下架skuId
        List<String> offlineSkuIdlist = new ArrayList<>();
        // 下架sku结果映射 skuId->TRTransformResultRo
        Map<String, TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> offlineSkuResultMap = new HashMap<>();
        // -------------- 转换结果处理 --------------
        for (TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformResultRo : transformResultRoList) {
            if (transformResultRo.getIsSuccess()) {
                // -------------- 转换成功 --------------
                onlineSkuList.add(transformResultRo.getData());
            } else {
                // -------------- 转换失败 --------------
                offlineSkuIdlist.add(transformResultRo.getPackageId());
                offlineSkuResultMap.put(transformResultRo.getSkuId(), transformResultRo);
            }
        }
        // -------------- sku上架 --------------
        if (!CollectionUtils.isEmpty(onlineSkuList)) {
            onSkuOnline(changeRo, onlineSkuList);
        }
        // -------------- sku下架 --------------
        if (!CollectionUtils.isEmpty(offlineSkuIdlist)) {
            // -------------- 转换失败，Sku下架 --------------
            List<TicketFunTemplateResourceSkuInfo> offlineSkuList = onSkuOffline("", null, offlineSkuIdlist);
            if (!CollectionUtils.isEmpty(offlineSkuList)) {
                offlineSkuList.forEach(offlineSku -> {
                    TRTransformResultRo<TicketFunTemplateResourceSkuInfo> transformResultRo = offlineSkuResultMap.get(offlineSku.getSkuId());
                    doOfflineLog(offlineSku.getProductId(), offlineSku.getPackageId(), offlineSku.getSkuId(), "SKU", transformResultRo);
                });
            }
        }
    }

    /**
     * 产品上架
     *
     * @param transformResultRo 结果ro
     */
    private void onProductOnLine(TRTransformResultRo<TicketFunTemplateResourceProductInfo> transformResultRo) {
        String productId = transformResultRo.getProductId();
        // -------------- 产品上架 --------------
        TicketFunTemplateResourceProductInfo productInfo = transformResultRo.getData();
        productInfo.setIsOnSale(WhetherEnum.YES.getValue());
        TicketFunTemplateResourceProductInfo oldProductInfo = templateResourceProductService.queryByProductId(productId);
        Boolean result = templateResourceProductService.saveOrUpdate(oldProductInfo, productInfo);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_ONLINE_FAIL);
        }
    }

    /**
     * 产品下架
     *
     * @param productId 产品id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    private TicketFunTemplateResourceProductInfo onProductOffline(String productId) {
        // -------------- 产品下架 --------------
        TicketFunTemplateResourceProductInfo oldProductInfo = templateResourceProductService.queryByProductId(productId);
        if (oldProductInfo == null) {
            return null;
        }
        Boolean result = templateResourceProductService.saveOrUpdate(oldProductInfo, null);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PRODUCT_OFFLINE_FAIL);
        }
        return oldProductInfo;
    }

    /**
     * 套餐上架
     *
     * @param changeRo    更改ro
     * @param packageList 套餐列表
     */
    private void onPackageOnline(TicketFunResourceChangeRo changeRo, List<TicketFunTemplateResourcePackageInfo> packageList) {
        // -------------- 套餐上架 --------------
        List<TicketFunTemplateResourcePackageInfo> oldPackageList = templateResourcePackageService.query(changeRo.getProductId(), changeRo.getPackageId(), null);
        Boolean result = templateResourcePackageService.saveOrUpdate(oldPackageList, packageList);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_ONLINE_FAIL);
        }
    }

    /**
     * 套餐下架
     *
     * @param productId     产品id
     * @param packageIdList 套餐id
     */
    private List<TicketFunTemplateResourcePackageInfo> onPackageOffline(String productId, List<String> packageIdList) {
        // -------------- 套餐下架 --------------
        List<TicketFunTemplateResourcePackageInfo> oldPackageList = templateResourcePackageService.query(productId, null, packageIdList);
        if (CollectionUtils.isEmpty(oldPackageList)) {
            return null;
        }
        Boolean result = templateResourcePackageService.saveOrUpdate(oldPackageList, null);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.PACKAGE_OFFLINE_FAIL);
        }
        return oldPackageList;
    }

    /**
     * sku上架
     *
     * @param changeRo 更改ro
     * @param skuList  sku列表
     */
    private void onSkuOnline(TicketFunResourceChangeRo changeRo, List<TicketFunTemplateResourceSkuInfo> skuList) {
        // -------------- SKU上架 --------------
        List<TicketFunTemplateResourceSkuInfo> oldSkuList = templateResourceSkuService.query(changeRo.getProductId(), changeRo.getPackageId(), null, changeRo.getSkuId(), null);
        Boolean result = templateResourceSkuService.saveOrUpdate(skuList, oldSkuList);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.SKU_ONLINE_FAIL);
        }
    }

    /**
     * Sku下架
     *
     * @param productId     产品id
     * @param packageIdList 包id列表
     * @param skuIdList     sku id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    private List<TicketFunTemplateResourceSkuInfo> onSkuOffline(String productId, List<String> packageIdList, List<String> skuIdList) {
        // -------------- SKU下架 --------------
        List<TicketFunTemplateResourceSkuInfo> oldSkuList = templateResourceSkuService.query(productId, "", packageIdList, "", skuIdList);
        if (CollectionUtils.isEmpty(oldSkuList)) {
            return null;
        }
        Boolean result = templateResourceSkuService.saveOrUpdate(oldSkuList, null);
        if (!result) {
            throw new TRTransformException(TRTransformException.ErrorInfo.SKU_OFFLINE_FAIL);
        }
        return oldSkuList;
    }

    /**
     * 检查变更参数
     *
     * @param changeRo 更改ro
     * @return {@link Boolean}
     */
    private Boolean checkChangeRo(TicketFunResourceChangeRo changeRo) {
        if (changeRo == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(changeRo.getCategories())) {
            return false;
        }
        return true;
    }

    /**
     * 上架日志
     *
     * @param productId 产品id
     * @param packageId 套餐id
     * @param skuId     sku id
     * @param subTitle  副标题
     */
    private void doOnlineLog(String productId, String packageId, String skuId, String subTitle) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库产品转换成功【%s】,", subTitle));
        if (StringUtils.hasText(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU上架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.hasText(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐上架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品上架", productId));
        }
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }

    /**
     * 上架失败日志
     *
     * @param productId 产品id
     * @param packageId 套餐id
     * @param skuId     sku id
     * @param subTitle  副标题
     */
    private void doOnlineFailLog(String productId, String packageId, String skuId, String subTitle) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库产品转换成功，上架失败【%s】,", subTitle));
        if (StringUtils.hasText(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU上架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.hasText(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐上架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品上架", productId));
        }
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }

    /**
     * 下架日志
     *
     * @param productId         产品id
     * @param packageId         套餐id
     * @param skuId             sku id
     * @param subTitle          副标题
     * @param transformResultRo 变换结果ro
     */
    private void doOfflineLog(String productId, String packageId, String skuId, String subTitle, TRTransformResultRo transformResultRo) {
        String filter1 = "";
        StringBuilder logMessage = new StringBuilder();
        logMessage.append(String.format("模版商品库转换失败【%s】,", subTitle));
        if (StringUtils.hasText(skuId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，skuId:%s，", "SKU下架", productId, packageId, skuId));
            filter1 = skuId;
        } else if (StringUtils.hasText(packageId)) {
            logMessage.append(String.format("【%s】。productId:%s，packageId:%s，", "套餐下架", productId, packageId));
            filter1 = packageId;
        } else {
            logMessage.append(String.format("【%s】。productId:%s，", "产品下架", productId));
        }
        logMessage.append(String.format("errCode：%s, errMsg：%s。", transformResultRo.getErrCode(), transformResultRo.getErrMsg()));
        LogUtils.info(LogOperateTypeEnum.UPDATE, logMessage.toString(), 0, filter1, productId);
    }
}
