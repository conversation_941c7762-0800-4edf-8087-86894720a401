package com.ly.ticketfun.etl.service.resource.templateResource.transfer;

import com.alibaba.fastjson2.JSON;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunPackageTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunProductTransformServiceImpl;
import com.ly.ticketfun.etl.templateresource.TemplateResourceEtlApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * FunTransformServiceTest
 *
 * <AUTHOR>
 * @date 2025/9/13
 */
@SpringBootTest(classes = TemplateResourceEtlApplication.class)
@RunWith(SpringRunner.class)
public class FunTransformServiceTest {

    @Resource
    FunProductTransformServiceImpl funProductTransformService;
    @Resource
    FunPackageTransformServiceImpl funPackageTransformService;

    @Test
    public void testProductTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
        changeRo.setProductId("123232");
        TRTransformResultRo<TicketFunTemplateResourceProductInfo> ticketFunTemplateResourceProductInfoTRTransformResultRo = funProductTransformService.transformData(changeRo, MQEnum.TicketFunResourceChangeCategory.PRODUCT_CHANGE);
        System.out.println(JSON.toJSONString(ticketFunTemplateResourceProductInfoTRTransformResultRo));

    }

    @Test
    public void  testPackageTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
        changeRo.setProductId("123232");
        List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> ticketFunTemplateResourcePackageInfoTRTransformResultRoList = funPackageTransformService.transformDataList(changeRo, MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE);
        System.out.println(JSON.toJSONString(ticketFunTemplateResourcePackageInfoTRTransformResultRoList));
    }



}