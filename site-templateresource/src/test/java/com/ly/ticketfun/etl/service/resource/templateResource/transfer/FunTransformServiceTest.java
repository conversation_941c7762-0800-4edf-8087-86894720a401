package com.ly.ticketfun.etl.service.resource.templateResource.transfer;

import com.alibaba.fastjson2.JSON;
import com.ly.ticketfun.etl.common.enums.mq.MQEnum;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSaleChannelInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TRTransformResultRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.TicketFunResourceChangeRo;
import com.ly.ticketfun.etl.service.resource.templateResource.etl.ITREtlService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.ITRTransformService;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunPackageTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunProductTransformServiceImpl;
import com.ly.ticketfun.etl.service.resource.templateResource.transform.impl.FunSkuPriceTransformServiceImpl;
import com.ly.ticketfun.etl.templateresource.TemplateResourceEtlApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * FunTransformServiceTest
 *
 * <AUTHOR>
 * @date 2025/9/13
 */
@SpringBootTest(classes = TemplateResourceEtlApplication.class)
@RunWith(SpringRunner.class)
public class FunTransformServiceTest {

    @Resource
    FunProductTransformServiceImpl funProductTransformService;
    @Resource
    FunPackageTransformServiceImpl funPackageTransformService;
    @Resource
    ITREtlService etlService;

    @Resource
    private ITRTransformService<TicketFunTemplateResourceSaleChannelInfo> ticketSaleChannelTransformService;

    @Test
    public void testSaleChannelTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
        changeRo.setPoiId(47677L);
        changeRo.setPackageId("1877227920922013696");
        List<TRTransformResultRo<TicketFunTemplateResourceSaleChannelInfo>> roList = ticketSaleChannelTransformService
                .transformDataList(changeRo, MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
        System.out.println(JSON.toJSONString(roList));
    }

    @Resource
    FunSkuPriceTransformServiceImpl funSkuPriceTransformService;

    @Test
    public void testProductTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
//        changeRo.setProductId("123232"); 日游
        changeRo.setProductId("126981");// 旅拍
        changeRo.setProductId("126993");// WIFI
        changeRo.setProductId("123232"); //日游
//        changeRo.setProductId("126981");//旅拍
//        changeRo.setProductId("126993");//WIFI
        TRTransformResultRo<TicketFunTemplateResourceProductInfo> ticketFunTemplateResourceProductInfoTRTransformResultRo = funProductTransformService.transformData(changeRo, MQEnum.TicketFunResourceChangeCategory.PRODUCT_CHANGE);
        System.out.println(JSON.toJSONString(ticketFunTemplateResourceProductInfoTRTransformResultRo));

    }

    @Test
    public void testPackageTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
//        changeRo.setProductId("126981");//旅拍
        changeRo.setProductId("126981");// 旅拍
//        changeRo.setProductId("126993");//WIFI卡
        changeRo.setProductId("123232");//日游
        List<TRTransformResultRo<TicketFunTemplateResourcePackageInfo>> ticketFunTemplateResourcePackageInfoTRTransformResultRoList = funPackageTransformService.transformDataList(changeRo, MQEnum.TicketFunResourceChangeCategory.PACKAGE_CHANGE);
        System.out.println(JSON.toJSONString(ticketFunTemplateResourcePackageInfoTRTransformResultRoList));
    }

    @Test
    public void funResourceChange() {
        TicketFunResourceChangeRo ro = new TicketFunResourceChangeRo();
        ro.setProductId("123232");
        ro.setCategories(Arrays.asList("SKU_PRICE_CHANGE", "PRODUCT_CHANGE", "PACKAGE_CHANGE"));
        etlService.funResourceChange(ro);
    }

    @Test
    public void  testSkuPriceTransformData() {
        TicketFunResourceChangeRo changeRo = new TicketFunResourceChangeRo();
        changeRo.setProductId("123232");//日游
        List<TRTransformResultRo<TicketFunTemplateResourceSkuInfo>> ticketFunTemplateResourceSkuInfoTRTransformResultRoList = funSkuPriceTransformService.transformDataList(changeRo, MQEnum.TicketFunResourceChangeCategory.SKU_PRICE_CHANGE);
        System.out.println(JSON.toJSONString(ticketFunTemplateResourceSkuInfoTRTransformResultRoList));
    }

}
