<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresource.ResourcePolicyMapper">


    <resultMap id="BaseResultMap" type="com.ly.ticketfun.etl.domain.tczbyresource.ResourcePolicy">
        <result column="RPId" property="id"/>
        <result column="RPResourceId" property="resourceId"/>
        <result column="RPProductId" property="productId"/>
        <result column="RPSupplierId" property="supplierId"/>
        <result column="RPContractId" property="contractId"/>
        <result column="RPProductIsAttribute" property="productIsAttribute"/>
        <result column="RPIsDistribution" property="isDistribution"/>
        <result column="RPInventoryId" property="inventoryId"/>
        <result column="RPBeginTime" property="beginTime"/>
        <result column="RPEndTime" property="endTime"/>
        <result column="RPSaleBeginTime" property="saleBeginTime"/>
        <result column="RPSaleEndTime" property="saleEndTime"/>
        <result column="RPBuyLimityTime" property="buyLimityTime"/>
        <result column="RPBuyLimityDays" property="buyLimityDays"/>
        <result column="RPMinTicket" property="minTicket"/>
        <result column="RPMaxTicket" property="maxTicket"/>
        <result column="RPIntervalDays" property="intervalDays"/>
        <result column="RPIntervalTimes" property="intervalTimes"/>
        <result column="RPIntervalTicketCounts" property="intervalTicketCounts"/>
        <result column="RPLengthOfStay" property="lengthOfStay"/>
        <result column="RPMinAmount" property="minAmount"/>
        <result column="RPMinSalesAmount" property="minSalesAmount"/>
        <result column="RPOrderWithinDays" property="orderWithinDays"/>
        <result column="RPBedTypeId" property="bedTypeId"/>
        <result column="RPBedTypeName" property="bedTypeName"/>
        <result column="RPHasWindows" property="hasWindows"/>
        <result column="RPNonSmoking" property="nonSmoking"/>
        <result column="RPHasBroadband" property="hasBroadband"/>
        <result column="RPOccupancyType" property="occupancyType"/>
        <result column="RPBedSize" property="bedSize"/>
        <result column="RPPInsuranceInfo" property="pInsuranceInfo"/>
        <result column="RPGetTicketModeId" property="getTicketModeId"/>
        <result column="RPGetTicketMode" property="getTicketMode"/>
        <result column="RPTicketPriceRemark" property="ticketPriceRemark"/>
        <result column="RPContainedItems" property="containedItems"/>
        <result column="RPHoardTicketPriceId" property="hoardTicketPriceId"/>
        <result column="RPOrderDateSpecial" property="orderDateSpecial"/>
        <result column="RPCommentBonus" property="commentBonus"/>
        <result column="RPBonusTypes" property="bonusTypes"/>
        <result column="RPAccountType" property="accountType"/>
        <result column="RPPackageCode" property="packageCode"/>
        <result column="RPPackageAmount" property="packageAmount"/>
        <result column="RPPlayDays" property="playDays"/>
        <result column="RPRowStatus" property="rowStatus"/>
        <result column="RPPayType" property="payType"/>
        <result column="RPIsSingleSale" property="isSingleSale"/>
        <result column="RPSTIsIntelligent" property="sTIsIntelligent"/>
        <result column="RPIsTicketMachine" property="isTicketMachine"/>
        <result column="RPIsLeaveWord" property="isLeaveWord"/>
        <result column="RPCertificateType" property="certificateType"/>
        <result column="RPCheckWay" property="checkWay"/>
        <result column="RPIsShow" property="isShow"/>
        <result column="RPIsReMark" property="isReMark"/>
        <result column="RPRemindType" property="remindType"/>
        <result column="RPRemindTypeName" property="remindTypeName"/>
        <result column="RPERefundTicketRule" property="eRefundTicketRule"/>
        <result column="RPIsPost" property="isPost"/>
        <result column="RPPostage" property="postage"/>
        <result column="RPIsScreening" property="isScreening"/>
        <result column="RPIsRealName" property="isRealName"/>
        <result column="RPOrignalTicketId" property="orignalTicketId"/>
        <result column="CreateUser" property="createUser"/>
        <result column="CreateTime" property="createTime"/>
        <result column="UpdateUser" property="updateUser"/>
        <result column="UpdateTime" property="updateTime"/>
        <result column="DbrGuid" property="dbrGuid"/>
        <result column="RPSourceFrom" property="sourceFrom"/>
        <result column="RPName" property="name"/>
        <result column="RPIsPartakeLowPrice" property="isPartakeLowPrice"/>
        <result column="RPProductMode" property="productMode"/>
        <result column="RPEIntervalExtend" property="eIntervalExtend"/>
        <result column="RPBanDate" property="banDate"/>
        <result column="RPPriceType" property="priceType"/>
        <result column="RPMerchantNumber" property="merchantNumber"/>
        <result column="RPRecommend" property="recommend"/>
        <result column="RPOrderNum" property="orderNum"/>
        <result column="RPSort" property="sort"/>
        <result column="RPCooperationType" property="cooperationType"/>
        <result column="RPFirstBookDate" property="firstBookDate"/>
        <result column="RPSecondBookDate" property="secondBookDate"/>
        <result column="RPHighestCommission" property="highestCommission"/>
        <result column="RPLowestCommission" property="lowestCommission"/>
        <result column="RPPriceChannelFlag" property="priceChannelFlag"/>
        <result column="RPPhoneNum" property="phoneNum"/>
        <result column="RPNameNum" property="nameNum"/>
        <result column="RPUpRowStatusType" property="upRowStatusType"/>
        <result column="RPUpRowStatusTime" property="upRowStatusTime"/>
        <result column="RPIncludeCrowdType" property="includeCrowdType"/>
        <result column="RPCurrencyCode" property="currencyCode"/>
        <result column="RPCurrencyName" property="currencyName"/>
        <result column="RPEnterLimitType" property="enterLimitType"/>
    </resultMap>

    <select id="selectResourcePolicyListByProductId"
            resultMap="BaseResultMap">
        SELECT rp.* FROM ResourcePolicy rp
        inner join market_mount_goods mmg on rp.RPResourceId = mmg.mount_resource_id  and rp.RpId = mmg.original_policy_id
        where rp.RPResourceId = #{resourceId}
          and mmg.mount_product_id = #{productId}
          and mmg.row_status = 1
          and rp.RPRowStatus = 1
    </select>
</mapper>
