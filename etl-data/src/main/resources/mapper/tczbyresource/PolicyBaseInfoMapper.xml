<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresource.PolicyBaseInfoMapper">
    <resultMap id="BaseResultMap" type="com.ly.ticketfun.etl.domain.tczbyresource.PolicyBaseInfo">
        <id column="DbrGuid" jdbcType="BIGINT" property="dbrGuid"/>
        <result column="policy_id" jdbcType="BIGINT" property="policyId"/>
        <result column="policy_name" jdbcType="VARCHAR" property="policyName"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="policy_original_id" jdbcType="INTEGER" property="policyOriginalId"/>
        <result column="original_price_id" jdbcType="INTEGER" property="originalPriceId"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="policy_mode" jdbcType="INTEGER" property="policyMode"/>
        <result column="has_outer_session" jdbcType="INTEGER" property="hasOuterSession"/>
        <result column="docking_method" jdbcType="INTEGER" property="dockingMethod"/>
        <result column="sales_scenarios" jdbcType="VARCHAR" property="salesScenarios"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="use_day_mode" jdbcType="INTEGER" property="useDayMode"/>
        <result column="realname" jdbcType="INTEGER" property="realname"/>
        <result column="ticket_cooperation_type" jdbcType="INTEGER" property="ticketCooperationType"/>
        <result column="order_overdue" jdbcType="INTEGER" property="orderOverdue"/>
        <result column="order_limit" jdbcType="INTEGER" property="orderLimit"/>
        <result column="sale_begin_time" jdbcType="TIMESTAMP" property="saleBeginTime"/>
        <result column="sale_end_time" jdbcType="TIMESTAMP" property="saleEndTime"/>
        <result column="effective_start_time" jdbcType="TIMESTAMP" property="effectiveStartTime"/>
        <result column="effective_end_time" jdbcType="TIMESTAMP" property="effectiveEndTime"/>
        <result column="source_from" jdbcType="INTEGER" property="sourceFrom"/>
        <result column="partner_type_id" jdbcType="INTEGER" property="partnerTypeId"/>
        <result column="special_type_id" jdbcType="INTEGER" property="specialTypeId"/>
        <result column="block_display" jdbcType="INTEGER" property="blockDisplay"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sale_price_currency_code" jdbcType="VARCHAR" property="salePriceCurrencyCode"/>
        <result column="balance_price_currency_code" jdbcType="VARCHAR" property="balancePriceCurrencyCode"/>
        <result column="listing_status" jdbcType="INTEGER" property="listingStatus"/>
        <result column="billing_date_type" jdbcType="INTEGER" property="billingDateType"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="old_new_flag" jdbcType="INTEGER" property="oldNewFlag"/>
        <result column="row_status" jdbcType="INTEGER" property="rowStatus"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="CreateUser" jdbcType="VARCHAR" property="createUser"/>
        <result column="UpdateTime" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="UpdateUser" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>



    <select id="selectListByProductId" resultMap="BaseResultMap">
        select distinct pbi.* from policy_base_info pbi
        inner join policy_product_relation ppr on pbi.policy_id = ppr.policy_id and pbi.resource_id = ppr.resource_id
        where pbi.row_status	 = 1
          and ppr.row_status = 1
          and pbi.listing_status = 1
          and ppr.product_id = #{productId}
          and ppr.resource_id = #{resourceId}
    </select>
</mapper>
