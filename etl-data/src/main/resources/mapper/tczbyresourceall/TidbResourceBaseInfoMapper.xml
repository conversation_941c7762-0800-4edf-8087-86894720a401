<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceBaseInfoMapper">

    <select id="getBasicResourceInfo"
            resultType="com.ly.ticketfun.etl.domain.tczbyresourceall.dto.BasicResourceInfoDto">
        SELECT rb.RBIID as resourceId,
               rb.RBIResourceName as resourceName,
               rbe.REResourceSubtitleName as resourceSubName,
               '10' as firstCategoryId,
               '门票' as firstCategoryName,
               '' as secondCategoryId,
               '' as secondCategoryName,
               '1' as travelDays,
               rb.RBISummary as resourceSummary,
               rb.RBIImagePath as resourceHeadImage,
               rb.RBIEstablishmentDate as establishmentDate,
               rb.RBI<PERSON>dd<PERSON> as resourceAddress,
               rb.RBIBLon as baiduLon,
               rb.RBIBLat as baiduLat,
               rb.CreateTime as effectiveTime,
               rb.UpdateTime as updateTime,
               rb.RBIRowStatus as validStatus,
               rb.RBICanBook as canBookStatus,
               rb.RBICooperationType as cooperationType,
               rb.RBIResourceGrade as resourceGrade,
               rb.RBIIsAppointment as appointment,
               rbe.RERankInfo as rankInfo,
               rsi.comments_score as commentScore,
               rsi.comments_count as commentCount,
               rsi.good_comments_count as commentGoodCount,
               rsi.mid_comments_count as commentMiddleCount,
               rsi.bad_comments_count as commentBadCount,
               rsi.comment_light_content as commentSummary,
               rsi.good_comments_rate as commentGoodRate,
               rsi.latest_bad_comments_rate as commentBadRate180,
               0 as hadGift,
               0 as timeZone,
               rb.RBITradeAreaId as tradeAreaId,
               rb.RBITradeAreaName as tradeAreaName,
               rsi.test_status as testStatus,
               rsi.show_status as showStatus,
               rsi.dates_can_sales as datesCanSales,
               rsi.days_open_time as daysOpenTime,
               rsi.commission_status as commissionStatus30,
               rsi.price_status as priceStatus,
               rsi.price_mbl_status as mblStatus,
               rsi.refundType as refundType,
               rsi.refund_time as refundTime,
               rsi.use_status as useStatus,
               rsi.maintenance_score as maintenanceScore,
               rsi.pv_half_year as pv180,
               rsi.uv_order_one_year as uvOrder365,
               rsi.user_click_7 as clickUv7,
               rsi.user_exposure_7 as exposureUv7,
               rsi.user_click_yoy_7 as clickUvYoy7,
               rsi.user_exposure_yoy_7 as exposureUvYoy7,
               rsi.user_click_30 as clickUv30,
               rsi.user_exposure_30 as exposureUv30,
               rsi.conversion_num_30 as cvr30,
               rsi.conversion_num_yoy_30 as cvrYoy30,
               rsi.order_official_7 as orderOfficial7,
               rsi.order_official_180 as orderOfficial180,
               rsi.order_official_365 as orderOfficial365,
               rsi.order_all_7 as order7,
               '0' as order30,
               rsi.order_all_180 as order180,
               rsi.order_all_channel_365  as order365,
               '0' as payUnitCount,
               rsi.hotness_score as hotnessScore,
               rsi.competitor_order as competitorOrder,
               rb.RBIBusinessStatus as businessStatus
        FROM TCZBYResourceAll.ResourceBaseInfo as rb
                 left join TCZBYResourceAll.ResourceExtend as rbe on rbe.REResourceId=rb.RBIID
                 left join TCZBYResourceAll.resource_supplementary_Info as rsi on rsi.resource_id=rb.RBIID and rsi.row_status=1
        where  RBIResourceType = 1
          AND RBIResourceCategory = 20301
          AND RBIProjectAttribute = 0
          AND RBIRowStatus = 1
          AND RBIID = #{resourceId}
        order by rb.RBIId
    </select>
</mapper>
