<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresourcebase.TagResourceWashDataMapper">


    <resultMap id="TagDetailResultMap" type="com.ly.ticketfun.etl.domain.bo.TagDetailBo">
        <result column="id" property="tagId"/>
        <result column="content_code" property="contentCode"/>
        <result column="content_value" property="contentValue"/>
        <result column="code" property="tagCode"/>
        <result column="name" property="tagName"/>
    </resultMap>

    <select id="selectListByTagIdListAndResourceId" resultMap="TagDetailResultMap">

        SELECT tbi.id,tadi.content_code,tadi.content_value,tbi.code,tbi.name
        FROM tag_base_info tbi
        inner join tag_aura_detail_info tadi on tbi.id = tadi.tag_id
        INNER join tag_base_extend_info tbei on tbi.id = tbei.tag_id and tadi.resource_id = tbei.resource_id
        inner join tag_aura_config_info taci on tadi.tag_aura_config_id = taci.id
        WHERE tbi.show_type = 1
        and tbi.bind_type in (2,3)
        and tbi.row_status = 1
        and tadi.row_status = 1
        and tbei.row_status = 1
        and tadi.resource_id in (0,#{resourceId})
        and tbi.begin_time &lt;= now() and tbi.end_time &gt;= now()
        and taci.platform_type = 0
        and taci.page_type = 2
        and tbi.id in
        <foreach collection="tagIdList" separator="," item="item" open="(" close=")" index="index">
            #{item}
        </foreach>
    </select>
</mapper>
