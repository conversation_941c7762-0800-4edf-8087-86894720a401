<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresourcebase.ResourcePlayMapper">


    <select id="queryHighLightInfoByResourceId"
            resultType="com.ly.ticketfun.etl.domain.templateResourceSearch.dto.HighLightInfoDto">
        SELECT rp.RPProjectId as resourceId,
               rp.RPPlayPics as highlightsImage,
               rpi.RPITitle as highlightsTitle,
               rpi.RPIContent as highlightsDesc
        FROM TCZBYResourceBase.ResourcePlay as rp
                 join TCZBYResourceBase.ResourcePlayIntroduce as rpi on rp.RPId=rpi.RPId
        where rp.RPProjectId = #{resourceId}
          and rp.RPIsFrontShow = 1
          and rp.RPRowStatus = 1
          and rp.RPPlayType=3
          and rpi.RPRowStatus=1
        order by rp.RPSort desc
    </select>
</mapper>
