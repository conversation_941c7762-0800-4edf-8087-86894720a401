<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.ticketfun.etl.mapper.tczbyresourcebase.ResourcePromotionRuleMapper">

    <select id="queryAllValidList" resultType="com.ly.ticketfun.etl.domain.po.ResourcePromotionBaseInfoPo">
        select RPRId,RPRTypeId,RPRTypeName,RPRRelationOldId,RPRName,RPRJoinNumber,RPRSaleChannel,RPRSaleTotalMoney,RPRRefundRule,RPRPriceType,RPRIsCode,
        RPRAutoAmountTypeId,RPRCalculateAmountTypeId,RPRBeginDate,RPREndDate,RPRDMinAmount,RPRDMaxAmount,RPRDD<PERSON>unt,
        <PERSON><PERSON><PERSON><PERSON>,RPRDIsAllUnderOwn,RPPDescription,RPRIsFrontSelect,RPRFrontCommont,RPRIsNegativeCommission
        from ResourcePromotionRule rpr
        join ResourcePromotionRuleData rprd on rprd.RPRDPromotionRuleId=rpr.RPRId
        where rpr.RPRRowStatus=1
        and rprd.RPRDRowStatus=1
        and rpr.RPRBeginDate  <![CDATA[<]]> #{beginDate,jdbcType=TIMESTAMP}
        and rpr.RPREndDate >= #{endDate,jdbcType=TIMESTAMP}
    </select>
</mapper>
