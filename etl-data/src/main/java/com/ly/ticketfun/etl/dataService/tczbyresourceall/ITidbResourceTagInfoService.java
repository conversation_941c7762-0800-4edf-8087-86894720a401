package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceTagInfo;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 搜索使用标签信息(1对多)  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceTagInfoService extends IDbBaseService<ResourceTagInfo> {

    /**
     * 根据景区id查询有效标签信息
     * @param resourceId 景区id
     * @return 标签信息
     */
    List<ResourceTagInfo> queryValidListByResourceId(Long resourceId);
}
