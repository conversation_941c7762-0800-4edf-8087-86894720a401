package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourcePolicy;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 资源政策表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface IResourcePolicyService extends IDbBaseService<ResourcePolicy> {

    List<ResourcePolicy> selectResourcePolicyList(Long resourceId,Long productId);



}
