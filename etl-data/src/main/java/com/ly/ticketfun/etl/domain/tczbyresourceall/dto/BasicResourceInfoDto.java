package com.ly.ticketfun.etl.domain.tczbyresourceall.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BasicResourceInfoDto {
    /**
     * 资源id
     */
    private Long resourceId;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 资源副标题
     */
    private String resourceSubName;
    /**
     * 品类Code
     */
    private String firstCategoryId;
    /**
     * 品类名称
     */
    private String firstCategoryName;
    /**
     * 二级品类Code
     */
    private String secondCategoryId;
    /**
     * 二级品类名称
     */
    private String secondCategoryName;
    /**
     * 游玩天数
     */
    private String travelDays;
    /**
     * 资源一句话描述
     */
    private String resourceSummary;
    /**
     * 资源首图
     */
    private String resourceHeadImage;
    /**
     * 开业时间
     */
    private LocalDateTime establishmentDate;
    /**
     * 资源地址
     */
    private String resourceAddress;
    /**
     * 经度
     */
    private BigDecimal baiduLon;
    /**
     * 纬度
     */
    private BigDecimal baiduLat;
    /**
     * 首次上架时间
     */
    private LocalDateTime effectiveTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否有效（1 有效，0 无效）
     */
    private Integer validStatus;
    /**
     * 是否可预定
     */
    private Integer canBookStatus;
    /**
     * 合作类型（1：自营，0：非自营）
     */
    private String cooperationType;
    /**
     * 资源星级 61201 1A，61202 2A，61203 3A，61204 4A，61205 5A，61206 其它
     */
    private Integer resourceGrade;
    /**
     * 是否预约 0 否 1是
     */
    private Integer appointment;
    /**
     * 榜单信息
     */
    private String rankInfo;
    /**
     * 点评分
     */
    private String commentScore;
    /**
     * 点评总数
     */
    private String commentCount;
    /**
     * 好评数
     */
    private String commentGoodCount;
    /**
     * 中评数
     */
    private String commentMiddleCount;
    /**
     * 差评数
     */
    private String commentBadCount;
    /**
     * 点评AI一句话
     */
    private String commentSummary;
    /**
     * 好评率
     */
    private BigDecimal commentGoodRate;
    /**
     * 最近180天差评率
     */
    private BigDecimal commentBadRate180;
    /**
     * 是否含赠品（1 是，0 否）
     */
    private Integer hadGift;
    /**
     * 时区
     */
    private Integer timeZone;
    /**
     * 商圈id
     */
    private Integer tradeAreaId;
    /**
     * 商圈名称
     */
    private String tradeAreaName;
    /**
     * 测试状态（1：测试，0：否）
     */
    private Integer testStatus;
    /**
     * 展示状态（1：对客，0：屏蔽对客）
     */
    private Integer showStatus;
    /**
     * 4个月的每天可定情况
     */
    private String datesCanSales;
    /**
     * 7天每天开闭园时间
     */
    private String daysOpenTime;
    /**
     * 30 天的订单正负佣金（1：正，0：平，-1：负）
     */
    private Integer commissionStatus30;
    /**
     * 出价状态
     */
    private Integer priceStatus;
    /**
     * 是否有价格优势（M B L）
     */
    private String mblStatus;
    /**
     * 退改状态（0：不可退，1：随时退 2：无损条件退，3：有损条件退）
     */
    private Integer refundType;
    /**
     * 条件退时间
     */
    private String refundTime;
    /**
     * 是否随买随用（1：是，0 否）
     */
    private Integer useStatus;
    /**
     * 信息完善度
     */
    private Integer maintenanceScore;
    /**
     * 多少个用户浏览过
     */
    private Long pv180;
    /**
     * 多少个用户购买过
     */
    private Long uvOrder365;
    /**
     * 点击7天UV
     */
    private Long clickUv7;
    /**
     * 曝光7天UV
     */
    private Long exposureUv7;
    /**
     * 点击去年同期7天UV
     */
    private Long clickUvYoy7;
    /**
     * 曝光去年同期7天UV
     */
    private Long exposureUvYoy7;
    /**
     * 点击30天UV
     */
    private Long clickUv30;
    /**
     * 曝光30天UV
     */
    private Long exposureUv30;
    /**
     * 30天CVR
     */
    private BigDecimal cvr30;
    /**
     * 去年同期30天CVR
     */
    private BigDecimal cvrYoy30;
    /**
     * 自营平台7天单量
     */
    private Long orderOfficial7;
    /**
     * 自营平台180天单量
     */
    private Long orderOfficial180;
    /**
     * 自营平台365天单量
     */
    private Long orderOfficial365;
    /**
     * 全渠道7天单量
     */
    private Long order7;
    /**
     * 全渠道30天单量
     */
    private Long order30;
    /**
     * 全渠道180天单量
     */
    private Long order180;
    /**
     * 全渠道365天单量
     */
    private Long order365;
    /**
     * 全渠道付款单销售份数
     */
    private String payUnitCount;
    /**
     * 热度分
     */
    private BigDecimal hotnessScore;
    /**
     * 竞对销量
     */
    private Long competitorOrder;
    /**
     * 景区营业状态： 0：未知，1: 营业， 2：不营业
     */
    private Integer businessStatus;
}
