package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyUseRuleService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyUseRule;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyUseRuleMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 政策核销规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyUseRuleServiceImpl extends DbBaseServiceImpl<PolicyUseRuleMapper, PolicyUseRule> implements IPolicyUseRuleService {

    @Resource
    private PolicyUseRuleMapper policyUseRuleMapper;

    @Override
    public PolicyUseRule queryByPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyUseRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyUseRule::getResourceId, resourceId)
                .eq(PolicyUseRule::getPolicyId, policyId)
                .eq(PolicyUseRule::getRowStatus, DataFlagEnum.VALID.getValue());

        return policyUseRuleMapper.selectOne(queryWrapper);
    }
}
