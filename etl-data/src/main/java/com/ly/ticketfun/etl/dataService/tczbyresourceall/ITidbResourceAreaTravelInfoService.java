package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceAreaTravelInfo;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 搜索用游玩景点+主题+别名(1对多) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceAreaTravelInfoService extends IDbBaseService<ResourceAreaTravelInfo> {
    /**
     * 根据资源id查询有效数据
     * @param resourceId 景区id
     * @return 游玩景点信息
     */
    List<ResourceAreaTravelInfo> queryValidByResourceId(Long resourceId);
}
