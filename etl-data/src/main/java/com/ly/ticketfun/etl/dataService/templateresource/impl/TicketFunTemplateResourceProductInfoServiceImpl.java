package com.ly.ticketfun.etl.dataService.templateresource.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.model.es.EsSearchResponse;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceProductService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.ProductInfoSearchRo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 门票玩乐模版产品资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourceProductInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourceProductInfo>  implements ITicketFunTemplateResourceProductService {
    /**
     * 查询
     *
     * @param productId   产品id
     * @param indexSuffix
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    @Override
    public TicketFunTemplateResourceProductInfo queryByProductId(String productId, String indexSuffix) {
        List<TicketFunTemplateResourceProductInfo> productInfoList = queryByProductId(Collections.singletonList(productId), indexSuffix);
        return CollectionUtil.isNotEmpty(productInfoList) ? productInfoList.get(0) : null;
    }

    /**
     * 查询列表
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceProductInfo> queryByProductId(List<String> productIdList, String indexSuffix) {
        if (CollectionUtil.isEmpty(productIdList)){
            return Collections.emptyList();
        }
        ProductInfoSearchRo productInfoSearchRo = new ProductInfoSearchRo();
        productInfoSearchRo.setProductIds(String.join(" OR ", productIdList));
        EsSearchResult<TicketFunTemplateResourceProductInfo> esSearchResult =  super.query("ticketfun_product_query", indexSuffix, productInfoSearchRo, new TypeReference<EsSearchResponse<TicketFunTemplateResourceProductInfo>>() {});
        return esSearchResult != null && esSearchResult.getList() != null ? esSearchResult.getList() : new ArrayList<>();
    }
}
