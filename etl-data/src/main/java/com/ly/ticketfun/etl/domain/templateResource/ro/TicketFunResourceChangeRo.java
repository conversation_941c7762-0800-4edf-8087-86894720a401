package com.ly.ticketfun.etl.domain.templateResource.ro;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 门票玩乐资源变更ro
 *
 * <AUTHOR>
 * @date 2025/09/04
 */
@Data
public class TicketFunResourceChangeRo implements Serializable {
    private static final long serialVersionUID = 7023272703158116798L;
    /**
     * poi id
     */
    private Long poiId;
    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 类别
     */
    private List<String> categories;
    /**
     * 事件时间
     */
    private Long eventTime;

}
