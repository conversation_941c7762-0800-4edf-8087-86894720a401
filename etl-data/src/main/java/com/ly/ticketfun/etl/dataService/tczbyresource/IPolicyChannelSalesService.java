package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyChannelSales;

import java.util.List;

/**
 * <p>
 * B07-政策分销渠道加价规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface IPolicyChannelSalesService extends IDbBaseService<PolicyChannelSales> {

    /**
     * 获取销售渠道列表
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 销售渠道列表
     */
    List<PolicyChannelSales> queryListByPolicyId(Long resourceId, Long policyId);
}
