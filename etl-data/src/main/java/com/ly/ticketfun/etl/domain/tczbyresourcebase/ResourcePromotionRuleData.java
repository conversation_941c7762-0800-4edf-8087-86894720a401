package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 促销规则设置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePromotionRuleData")
@TableExtend(desc = "促销规则设置")
public class ResourcePromotionRuleData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "RPRDId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键ID")
    private Long id;

    /**
     * 促销基本表ID
     */
    @TableField("RPRDPromotionRuleId")
    @TableFieldExtend(desc = "促销基本表ID")
    private Long promotionRuleId;

    /**
     * 优惠开始金额
     */
    @TableField("RPRDMinAmount")
    @TableFieldExtend(desc = "优惠开始金额")
    private BigDecimal minAmount;

    /**
     * 优惠结束金额
     */
    @TableField("RPRDMaxAmount")
    @TableFieldExtend(desc = "优惠结束金额")
    private BigDecimal maxAmount;

    /**
     * 折扣
     */
    @TableField("RPRDDiscount")
    @TableFieldExtend(desc = "折扣")
    private BigDecimal discount;

    /**
     * 涉及促销金额
     */
    @TableField("RPRDMoney")
    @TableFieldExtend(desc = "涉及促销金额")
    private BigDecimal money;

    /**
     * 规则说明
     */
    @TableField("RPRDRemarks")
    @TableFieldExtend(desc = "规则说明")
    private String remarks;

    /**
     * 排序
     */
    @TableField("RPRDSort")
    @TableFieldExtend(desc = "排序")
    private Integer sort;

    /**
     * 单次下单量限制
     */
    @TableField("RPRDLimitOrderTickets")
    @TableFieldExtend(desc = "单次下单量限制")
    private Integer limitOrderTickets;

    /**
     * 1 文旅承担 2非文旅承担
     */
    @TableField("RPRDIsAllUnderOwn")
    @TableFieldExtend(desc = "1 文旅承担 2非文旅承担")
    private Integer isAllUnderOwn;

    /**
     * 项目承担类型ID，0金额 1百分比
     */
    @TableField("RPRDUnderTakingTypeId")
    @TableFieldExtend(desc = "项目承担类型ID，0金额 1百分比")
    private Integer underTakingTypeId;

    /**
     * 项目承担金额
     */
    @TableField("RPRDUnderTaking")
    @TableFieldExtend(desc = "项目承担金额")
    private BigDecimal underTaking;

    /**
     * ？具体承担方，承担金额，到项目（Json字符串）
     */
    @TableField("RPRDOtherUnderTaking")
    @TableFieldExtend(desc = "？具体承担方，承担金额，到项目（Json字符串）")
    private String otherUnderTaking;

    /**
     * 有效性
     */
    @TableField("RPRDRowStatus")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
