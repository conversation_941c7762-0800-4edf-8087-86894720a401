package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 针对人群限制表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_traveller_crowd_limit")
@TableExtend(desc = "针对人群限制表")
public class PolicyTravellerCrowdLimit implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 本表id，唯一
     */
    @TableField("id")
    @TableFieldExtend(desc = "本表id，唯一")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 人群分类表主键id
     */
    @TableField("crowd_info_id")
    @TableFieldExtend(desc = "人群分类表主键id")
    private Long crowdInfoId;

    /**
     * 限制条件组id
     */
    @TableField("group_id")
    @TableFieldExtend(desc = "限制条件组id")
    private Long groupId;

    /**
     * 限制类型0 年龄 1性别 2特殊区域 3出生日期 4生肖 5姓名 6 学生
     */
    @TableField("limit_type")
    @TableFieldExtend(desc = "限制类型0 年龄 1性别 2特殊区域 3出生日期 4生肖 5姓名 6 学生")
    private Integer limitType;

    /**
     * 限制类型名称
     */
    @TableField("limit_type_name")
    @TableFieldExtend(desc = "限制类型名称")
    private String limitTypeName;

    /**
     * 验证规则 1所选类型可购票 2所选类型不可购票
     */
    @TableField("can_book")
    @TableFieldExtend(desc = "验证规则 1所选类型可购票 2所选类型不可购票")
    private Integer canBook;

    /**
     * 维度类型 	101年龄年 102年龄月 103 年龄天		301省份维度302城市维度303区县		501出生日年，502出生日月，503出生日日
     */
    @TableField("dimension_type")
    @TableFieldExtend(desc = "维度类型 	101年龄年 102年龄月 103 年龄天		301省份维度302城市维度303区县		501出生日年，502出生日月，503出生日日			")
    private Integer dimensionType;

    /**
     * 限制内容( json格式)
     */
    @TableField("limit_conditions")
    @TableFieldExtend(desc = "限制内容( json格式)")
    private String limitConditions;

    /**
     * 排序
     */
    @TableField("sort")
    @TableFieldExtend(desc = "排序")
    private Integer sort;

    /**
     * 有效性 1有效0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;


}
