package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.localactivity.framework.annotation.EsIndex;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.SkuResourcePriceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版SKU资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@EsIndex(value = "ticketfun-sku", token = "4710b712-1c60-42ae-859c-dc6a7e14b44c")
public class TicketFunTemplateResourceSkuInfo extends SkuResourcePriceDto {
    private static final long serialVersionUID = -2022656304123305508L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否有效
     */
    @EnumField(enumClazz = DataFlagEnum.class)
    @DataFlagField
    private Integer dataFlag;
    /**
     * 语言
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private String locale;
    /**
     * 创建时间
     */
    @CreateTimeField
    private Long createTime;
    /**
     * 更新时间
     */
    @ModifyTimeField
    private Long updateTime;
}
