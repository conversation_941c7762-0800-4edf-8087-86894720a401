package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.FeeDescEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SceneryHotelBookTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 条款信息
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ClauseTypeDto implements Serializable {
    private static final long serialVersionUID = 6965973970461926804L;
    /**
     * 条款类型Code
     */
    private String clauseTypeCode;
    /**
     * 标题
     */
    private String title;
    /**
     * 条款列表
     */
    private List<ClauseDto> clauseList;

    /**
     * 条款
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class ClauseDto {
        /**
         * 条款Code
         */
        @EnumField(enumClazz = FeeDescEnum.class)
        private String clauseCode;
        /**
         * 条款标题
         */
        private String title;
        /**
         * 明细可用数量
         */
        private Integer itemAvailableCount;
        /**
         * 内容
         */
        private String content;
        /**
         * 预约类型
         */
        @EnumField(enumClazz = SceneryHotelBookTypeEnum.class)
        private String bookType;
        /**
         * 条款明细
         */
        private List<ClauseItemDto> clauseItemList;
    }

    /**
     * 条款明细
     *
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class ClauseItemDto {
        /**
         * 条款明细Code
         */
        private String clauseItemCode;
        /**
         * 标题
         */
        private String title;
        /**
         * 内容
         */
        private String content;
        /**
         * 图标url
         */
        private String iconUrl;
        /**
         * 适用人群Ids,隔开
         */
        private String suitCrowdIds;
        /**
         * 适用人群Code 用,隔开
         */
        private String suitCrowdCodes;
        /**
         * 省份名称
         */
        private String provinceName;
        /**
         * 城市id
         */
        private String cityName;
        /**
         * 景区或者酒店id
         */
        private String sceneryHotelId;
        private String sceneryHotelName;
    }
}
