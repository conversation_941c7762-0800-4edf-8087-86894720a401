package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 预制票管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("precast_ticket_manage")
@TableExtend(desc = "预制票管理")
public class PrecastTicketManage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 维度类型 1合作系统 2景区 3协议 4政策
     */
    @TableField("dimension_type")
    @TableFieldExtend(desc = "维度类型 1合作系统 2景区 3协议 4政策")
    private Integer dimensionType;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 资源名称
     */
    @TableField("resource_name")
    @TableFieldExtend(desc = "资源名称")
    private String resourceName;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 政策短id
     */
    @TableField("ticket_id")
    @TableFieldExtend(desc = "政策短id")
    private Long ticketId;

    /**
     * 政策名称
     */
    @TableField("policy_name")
    @TableFieldExtend(desc = "政策名称")
    private String policyName;

    /**
     * 协议id
     */
    @TableField("contract_id")
    @TableFieldExtend(desc = "协议id")
    private Long contractId;

    /**
     * 合作系统id
     */
    @TableField("coop_id")
    @TableFieldExtend(desc = "合作系统id")
    private String coopId;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    @TableFieldExtend(desc = "供应商id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @TableField("supplier_name")
    @TableFieldExtend(desc = "供应商名称")
    private String supplierName;

    /**
     * 追加售卖天数
     */
    @TableField("append_sold_days")
    @TableFieldExtend(desc = "追加售卖天数")
    private Integer appendSoldDays;

    /**
     * 提前推送天数
     */
    @TableField("advance_push_days")
    @TableFieldExtend(desc = "提前推送天数")
    private Integer advancePushDays;

    /**
     * 放票时间段(HH:mm~HH:mm) ','分割多个时间段
     */
    @TableField("sale_period")
    @TableFieldExtend(desc = "放票时间段(HH:mm~HH:mm) ','分割多个时间段")
    private String salePeriod;

    /**
     * 加速包金额 ','分割（废弃）
     */
    @TableField("accelerator_pack_amount")
    @TableFieldExtend(desc = "加速包金额 ','分割（废弃）")
    private String acceleratorPackAmount;

    /**
     * 禁止推送时间段(HH:mm~HH:mm) ','分割多个时间段
     */
    @TableField("disable_push_period")
    @TableFieldExtend(desc = "禁止推送时间段(HH:mm~HH:mm) ','分割多个时间段")
    private String disablePushPeriod;

    /**
     * 有效开始时间
     */
    @TableField("begin_date")
    @TableFieldExtend(desc = "有效开始时间")
    private LocalDateTime beginDate;

    /**
     * 有效结束时间
     */
    @TableField("end_date")
    @TableFieldExtend(desc = "有效结束时间")
    private LocalDateTime endDate;

    /**
     * 数据有效性 0 无效  1 有效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性 0 无效  1 有效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 销售渠道 1全部 2自营 3分销（废弃）
     */
    @TableField("sale_channel")
    @TableFieldExtend(desc = "销售渠道 1全部 2自营 3分销（废弃）")
    private Integer saleChannel;

    /**
     * 延迟推送时间(分钟)（废弃）
     */
    @TableField("delayed_push_time")
    @TableFieldExtend(desc = "延迟推送时间(分钟)（废弃）")
    private Integer delayedPushTime;

    /**
     * 推送频率
     */
    @TableField("push_frequency")
    @TableFieldExtend(desc = "推送频率")
    private Integer pushFrequency;

    /**
     * 忽略状态 0忽略 1未忽略
     */
    @TableField("ignore_status")
    @TableFieldExtend(desc = "忽略状态 0忽略 1未忽略")
    private Integer ignoreStatus;

    /**
     * 手动更新 0系统更新 1手动更新
     */
    @TableField("manual_update")
    @TableFieldExtend(desc = "手动更新 0系统更新 1手动更新")
    private Integer manualUpdate;


}
