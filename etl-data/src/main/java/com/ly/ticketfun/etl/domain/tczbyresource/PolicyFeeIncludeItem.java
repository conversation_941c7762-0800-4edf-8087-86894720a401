package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 费用包含主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_fee_include_item")
@TableExtend(desc = "费用包含主表")
public class PolicyFeeIncludeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动编号
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "自动编号")
    private Long dbrGuid;

    /**
     * id
     */
    @TableField("id")
    @TableFieldExtend(desc = "id")
    private Long id;

    /**
     * 景区id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 类型（0费用包含项目,1是否全部可用,2包含说明,3费用不包含）
     */
    @TableField("item_type")
    @TableFieldExtend(desc = "类型（0费用包含项目,1是否全部可用,2包含说明,3费用不包含）")
    private Integer itemType;

    /**
     * 类型0：项目名称，1：是否全部可用 2：包含说明，3 费用不包含
     */
    @TableField("item_content")
    @TableFieldExtend(desc = "类型0：项目名称，1：是否全部可用 2：包含说明，3 费用不包含")
    private String itemContent;

    /**
     * 排序值
     */
    @TableField("item_sort")
    @TableFieldExtend(desc = "排序值")
    private Integer itemSort;

    /**
     * 是否是叶子节点  1是  0 不是
     */
    @TableField("is_leaf")
    @TableFieldExtend(desc = "是否是叶子节点  1是  0 不是")
    private Integer isLeaf;

    /**
     * 有效性 1有效  0 无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效  0 无效")
    private Integer rowStatus;

    /**
     * 创建用户
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新用户
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
