package com.ly.ticketfun.etl.dataService.tczbyresourceall.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresourceall.dto.BasicResourceInfoDto;
import com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceBaseInfoMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.ITidbResourceBaseInfoService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 资源表，分片键：资源ID 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
//@Service
public class TidbResourceBaseInfoServiceImpl extends DbBaseServiceImpl<TidbResourceBaseInfoMapper, ResourceBaseInfo> implements ITidbResourceBaseInfoService {


    @Override
    public BasicResourceInfoDto getBasicResourceInfo(Long resourceId) {
        return baseMapper.getBasicResourceInfo(resourceId);
    }

    @Override
    public ResourceBaseInfo queryValidByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceBaseInfo::getId, resourceId)
                .eq(ResourceBaseInfo::getRowStatus, 1);
        return this.queryOne(queryWrapper);
    }
}
