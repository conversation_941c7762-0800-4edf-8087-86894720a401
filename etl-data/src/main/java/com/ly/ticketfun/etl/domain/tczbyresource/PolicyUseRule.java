package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.ticket.EnterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 政策核销规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_use_rule")
@TableExtend(desc = "政策核销规则")
public class PolicyUseRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表id
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "表id")
    private Long dbrGuid;

    /**
     * 主键
     */
    @TableField("id")
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 是否实名0否1是
     */
    @TableField("if_real_name")
    @TableFieldExtend(desc = "是否实名0否1是")
    private Integer ifRealName;

    /**
     * 入园方式0直接入园1换票2自定义
     */
    @TableField("enter_type")
    @TableFieldExtend(desc = "入园方式0直接入园1换票2自定义")
    @EnumField(enumClazz = EnterTypeEnum.class)
    private Integer enterType;

    /**
     * 是否需要所有凭证0否1是
     */
    @TableField("voucher_all")
    @TableFieldExtend(desc = "是否需要所有凭证0否1是")
    private Integer voucherAll;

    /**
     * 补充说明
     */
    @TableField("supplement_desc")
    @TableFieldExtend(desc = "补充说明")
    private String supplementDesc;

    /**
     * 入园换票地址时间是否相同
     */
    @TableField("if_same_address")
    @TableFieldExtend(desc = "入园换票地址时间是否相同")
    private Integer ifSameAddress;

    /**
     * 入园联系电话
     */
    @TableField("enter_park_tel")
    @TableFieldExtend(desc = "入园联系电话")
    private String enterParkTel;

    /**
     * 入园次数
     */
    @TableField("enter_park_times")
    @TableFieldExtend(desc = "入园次数")
    private Integer enterParkTimes;

    /**
     * json数组：入园快捷设备 0 手持机 1自助取票机
     */
    @TableField("enter_park_quick_device")
    @TableFieldExtend(desc = "json数组：入园快捷设备 0 手持机 1自助取票机")
    private String enterParkQuickDevice;

    /**
     * 是否有效0否1是
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "是否有效0否1是")
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
