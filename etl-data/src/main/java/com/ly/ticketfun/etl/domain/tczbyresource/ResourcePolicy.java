package com.ly.ticketfun.etl.domain.tczbyresource;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;


/**
 * <p>
 * 资源政策表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePolicy")
@TableExtend(desc = "资源政策表")
public class ResourcePolicy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键18位
     */
    @TableField("RPId")
    @TableFieldExtend(desc = "主键18位")
    private Long id;

    /**
     * 资源ID，分片键
     */
    @TableField("RPResourceId")
    @TableFieldExtend(desc = "资源ID，分片键")
    private Long resourceId;

    /**
     * 产品ID
     */
    @TableField("RPProductId")
    @TableFieldExtend(desc = "产品ID")
    private Long productId;

    /**
     * 供应商ID
     */
    @TableField("RPSupplierId")
    @TableFieldExtend(desc = "供应商ID")
    private Long supplierId;

    /**
     * 协议Id
     */
    @TableField("RPContractId")
    @TableFieldExtend(desc = "协议Id")
    private Long contractId;

    /**
     * 产品属性（23901预约票 23902 固定旅游日期） 0 也表示固定旅游日期  23903创单日起计算
     */
    @TableField("RPProductIsAttribute")
    @TableFieldExtend(desc = "产品属性（23901预约票 23902 固定旅游日期） 0 也表示固定旅游日期  23903创单日起计算")
    private Integer productIsAttribute;

    /**
     * 是否可分销（0：允许，1：不允允许）
     */
    @TableField("RPIsDistribution")
    @TableFieldExtend(desc = "是否可分销（0：允许，1：不允允许）")
    private Integer isDistribution;

    /**
     * 库存组ID
     */
    @TableField("RPInventoryId")
    @TableFieldExtend(desc = "库存组ID")
    private Long inventoryId;

    /**
     * 开始时间
     */
    @TableField("RPBeginTime")
    @TableFieldExtend(desc = "开始时间")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @TableField("RPEndTime")
    @TableFieldExtend(desc = "结束时间")
    private LocalDateTime endTime;

    /**
     * 销售开始时间
     */
    @TableField("RPSaleBeginTime")
    @TableFieldExtend(desc = "销售开始时间")
    private LocalDateTime saleBeginTime;

    /**
     * 销售结束时间
     */
    @TableField("RPSaleEndTime")
    @TableFieldExtend(desc = "销售结束时间")
    private LocalDateTime saleEndTime;

    /**
     * 下单限制时间
     */
    @TableField("RPBuyLimityTime")
    @TableFieldExtend(desc = "下单限制时间")
    private String buyLimityTime;

    /**
     * 下单限制0当天 1提前一天2提前两天
     */
    @TableField("RPBuyLimityDays")
    @TableFieldExtend(desc = "下单限制0当天 1提前一天2提前两天")
    private Integer buyLimityDays;

    /**
     * 单次最小预订数量
     */
    @TableField("RPMinTicket")
    @TableFieldExtend(desc = "单次最小预订数量")
    private Integer minTicket;

    /**
     * 单次最大预订数量
     */
    @TableField("RPMaxTicket")
    @TableFieldExtend(desc = "单次最大预订数量")
    private Integer maxTicket;

    /**
     * 自定义限制计算天数，间隔类型
     */
    @TableField("RPIntervalDays")
    @TableFieldExtend(desc = "自定义限制计算天数，间隔类型")
    private Integer intervalDays;

    /**
     * 间隔次数
     */
    @TableField("RPIntervalTimes")
    @TableFieldExtend(desc = "间隔次数")
    private Integer intervalTimes;

    /**
     * 间隔总票数默认0
     */
    @TableField("RPIntervalTicketCounts")
    @TableFieldExtend(desc = "间隔总票数默认0")
    private Integer intervalTicketCounts;

    /**
     * 连住天数
     */
    @TableField("RPLengthOfStay")
    @TableFieldExtend(desc = "连住天数")
    private Integer lengthOfStay;

    /**
     * 显示最小卖价（市场价）
     */
    @TableField("RPMinAmount")
    @TableFieldExtend(desc = "显示最小卖价（市场价）")
    private BigDecimal minAmount;

    /**
     * 显示最小卖价（同程卖价）
     */
    @TableField("RPMinSalesAmount")
    @TableFieldExtend(desc = "显示最小卖价（同程卖价）")
    private BigDecimal minSalesAmount;

    /**
     * 限制几天内预订（限制购买天数）
     */
    @TableField("RPOrderWithinDays")
    @TableFieldExtend(desc = "限制几天内预订（限制购买天数）")
    private Integer orderWithinDays;

    /**
     * 床型ID,大床，双床
     */
    @TableField("RPBedTypeId")
    @TableFieldExtend(desc = "床型ID,大床，双床")
    private Integer bedTypeId;

    /**
     * 床型名称
     */
    @TableField("RPBedTypeName")
    @TableFieldExtend(desc = "床型名称")
    private String bedTypeName;

    /**
     * 是否有窗（0：无，1：有，2： 部分有窗）
     */
    @TableField("RPHasWindows")
    @TableFieldExtend(desc = "是否有窗（0：无，1：有，2： 部分有窗）")
    private Integer hasWindows;

    /**
     * 是否可以无烟处理
     */
    @TableField("RPNonSmoking")
    @TableFieldExtend(desc = "是否可以无烟处理")
    private Integer nonSmoking;

    /**
     * 是否有宽带
     */
    @TableField("RPHasBroadband")
    @TableFieldExtend(desc = "是否有宽带")
    private String hasBroadband;

    /**
     * 入住类型（0：标准；1：钟点房；2：午夜房）
     */
    @TableField("RPOccupancyType")
    @TableFieldExtend(desc = "入住类型（0：标准；1：钟点房；2：午夜房）")
    private Integer occupancyType;

    /**
     * 床宽
     */
    @TableField("RPBedSize")
    @TableFieldExtend(desc = "床宽")
    private String bedSize;

    /**
     * 保险ID:0读取默认保险,-1屏蔽保险,大于1代表保险ID
     */
    @TableField("RPPInsuranceInfo")
    @TableFieldExtend(desc = "保险ID:0读取默认保险,-1屏蔽保险,大于1代表保险ID")
    private Integer pInsuranceInfo;

    /**
     * 入园方式Id（2018-08-28 废弃）
     */
    @TableField("RPGetTicketModeId")
    @TableFieldExtend(desc = "入园方式Id（2018-08-28 废弃）")
    private Integer getTicketModeId;

    /**
     * 入园方式
     */
    @TableField("RPGetTicketMode")
    @TableFieldExtend(desc = "入园方式")
    private String getTicketMode;

    /**
     * 预订说明
     */
    @TableField("RPTicketPriceRemark")
    @TableFieldExtend(desc = "预订说明")
    private String ticketPriceRemark;

    /**
     * 包含项目
     */
    @TableField("RPContainedItems")
    @TableFieldExtend(desc = "包含项目")
    private String containedItems;

    /**
     * 囤票关联关系ID 2019-05-28启用
     */
    @TableField("RPHoardTicketPriceId")
    @TableFieldExtend(desc = "囤票关联关系ID 2019-05-28启用")
    private Integer hoardTicketPriceId;

    /**
     * 特殊策略设置（下单日期）（ 0 常规、28401  今日订、28402	改期、28403	囤票）2018-08-28 起废弃
     */
    @TableField("RPOrderDateSpecial")
    @TableFieldExtend(desc = "特殊策略设置（下单日期）（ 0 常规、28401  今日订、28402	改期、28403	囤票）2018-08-28 起废弃")
    private Integer orderDateSpecial;

    /**
     * 点评奖金2018-08-28 起废弃
     */
    @TableField("RPCommentBonus")
    @TableFieldExtend(desc = "点评奖金2018-08-28 起废弃")
    private BigDecimal commentBonus;

    /**
     * 奖金类型（0 奖金 1红包 2礼包）2018-08-28 起废弃
     */
    @TableField("RPBonusTypes")
    @TableFieldExtend(desc = "奖金类型（0 奖金 1红包 2礼包）2018-08-28 起废弃")
    private Integer bonusTypes;

    /**
     * 点评奖金账户（0后返账户 1 立返账户）2018-08-28 起废弃)
     */
    @TableField("RPAccountType")
    @TableFieldExtend(desc = "点评奖金账户（0后返账户 1 立返账户）2018-08-28 起废弃)")
    private Integer accountType;

    /**
     * 礼包编码 (27101  5元景区红包,27102  10元景区红包,27103  15元景区红包)2018-08-28 起废弃
     */
    @TableField("RPPackageCode")
    @TableFieldExtend(desc = "礼包编码 (27101  5元景区红包,27102  10元景区红包,27103  15元景区红包)2018-08-28 起废弃")
    private Integer packageCode;

    /**
     * 礼包金额(2018-08-28 起废弃)
     */
    @TableField("RPPackageAmount")
    @TableFieldExtend(desc = "礼包金额(2018-08-28 起废弃)")
    private BigDecimal packageAmount;

    /**
     * 游玩天数，游玩几天
     */
    @TableField("RPPlayDays")
    @TableFieldExtend(desc = "游玩天数，游玩几天")
    private Integer playDays;

    /**
     * 是否有效 0无效 1有效(0下架(无效)1上架(有效)2删除3待上架)
     */
    @TableField("RPRowStatus")
    @TableFieldExtend(desc = "是否有效 0无效 1有效(0下架(无效)1上架(有效)2删除3待上架)")
    private Integer rowStatus;

    /**
     * 支付方式：0 到付 1在线支付
     */
    @TableField("RPPayType")
    @TableFieldExtend(desc = "支付方式：0 到付 1在线支付")
    private Integer payType;

    /**
     * 是否单卖：0不单卖，1单卖
     */
    @TableField("RPIsSingleSale")
    @TableFieldExtend(desc = "是否单卖：0不单卖，1单卖")
    private Integer isSingleSale;

    /**
     * 是否支持智能刷卡设备,1:是
     */
    @TableField("RPSTIsIntelligent")
    @TableFieldExtend(desc = "是否支持智能刷卡设备,1:是")
    private Integer sTIsIntelligent;

    /**
     * 是否支持自助取票机
     */
    @TableField("RPIsTicketMachine")
    @TableFieldExtend(desc = "是否支持自助取票机")
    private Integer isTicketMachine;

    /**
     * 是否支持留言
     */
    @TableField("RPIsLeaveWord")
    @TableFieldExtend(desc = "是否支持留言")
    private Integer isLeaveWord;

    /**
     * 证件类型信息（多个以英文逗号隔开,数据字典60502的子类）
     */
    @TableField("RPCertificateType")
    @TableFieldExtend(desc = "证件类型信息（多个以英文逗号隔开,数据字典60502的子类）")
    private String certificateType;

    /**
     * 订单验证方式60501手机号码60502身份证
     */
    @TableField("RPCheckWay")
    @TableFieldExtend(desc = "订单验证方式60501手机号码60502身份证")
    private String checkWay;

    /**
     * 价格框是否屏蔽展示（1展示，0不展示 针对所有渠道）  2 屏蔽今日定--有票,价格框使用
     */
    @TableField("RPIsShow")
    @TableFieldExtend(desc = "价格框是否屏蔽展示（1展示，0不展示 针对所有渠道）  2 屏蔽今日定--有票,价格框使用")
    private Integer isShow;

    /**
     * 是否支持备注( 0不支持1支持)
     */
    @TableField("RPIsReMark")
    @TableFieldExtend(desc = "是否支持备注( 0不支持1支持)")
    private Integer isReMark;

    /**
     * 备注填写提醒类型（数据字典）
     */
    @TableField("RPRemindType")
    @TableFieldExtend(desc = "备注填写提醒类型（数据字典）")
    private Integer remindType;

    /**
     * 备注填写提醒名称（数据字典 备注）
     */
    @TableField("RPRemindTypeName")
    @TableFieldExtend(desc = "备注填写提醒名称（数据字典 备注）")
    private String remindTypeName;

    /**
     * 退改规则说明（生成）
     */
    @TableField("RPERefundTicketRule")
    @TableFieldExtend(desc = "退改规则说明（生成）")
    private String eRefundTicketRule;

    /**
     * 是否支持邮寄
     */
    @TableField("RPIsPost")
    @TableFieldExtend(desc = "是否支持邮寄")
    private Integer isPost;

    /**
     * 邮资费用
     */
    @TableField("RPPostage")
    @TableFieldExtend(desc = "邮资费用")
    private BigDecimal postage;

    /**
     * 是否有场次-针对有场次的景区
     */
    @TableField("RPIsScreening")
    @TableFieldExtend(desc = "是否有场次-针对有场次的景区")
    private Integer isScreening;

    /**
     * 是否支持实名制（需要游玩人全部信息 1需要 0 只需要1位）
     */
    @TableField("RPIsRealName")
    @TableFieldExtend(desc = "是否支持实名制（需要游玩人全部信息 1需要 0 只需要1位）")
    private Integer isRealName;

    /**
     * 原始鸟巢门票id（用于一个门票多策略合并时，定位具体的新策略id）
     */
    @TableField("RPOrignalTicketId")
    @TableFieldExtend(desc = "原始鸟巢门票id（用于一个门票多策略合并时，定位具体的新策略id）")
    private Integer orignalTicketId;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;

    /**
     * 政策来源（1：自签，2：携程，4：艺龙，8：Switch，9：开放平台(景区)，10：新开放平台,11：电子导览  12:美团  13:客路）
     */
    @TableField("RPSourceFrom")
    @TableFieldExtend(desc = "政策来源（1：自签，2：携程，4：艺龙，8：Switch，9：开放平台(景区)，10：新开放平台,11：电子导览  12:美团  13:客路）")
    private Long sourceFrom;

    /**
     * 政策名称
     */
    @TableField("RPName")
    @TableFieldExtend(desc = "政策名称")
    private String name;

    /**
     * 是否参与最低价计算（1-是0-否默认1）
     */
    @TableField("RPIsPartakeLowPrice")
    @TableFieldExtend(desc = "是否参与最低价计算（1-是0-否默认1）")
    private Integer isPartakeLowPrice;

    /**
     * 产品模式（数据字典232）
     */
    @TableField("RPProductMode")
    @TableFieldExtend(desc = "产品模式（数据字典232）")
    private Integer productMode;

    /**
     * 特殊下单验证（间隔验证扩展,针对整个协议）
     */
    @TableField("RPEIntervalExtend")
    @TableFieldExtend(desc = "特殊下单验证（间隔验证扩展,针对整个协议）")
    private Integer eIntervalExtend;

    /**
     * 景区禁止预定日期
     */
    @TableField("RPBanDate")
    @TableFieldExtend(desc = "景区禁止预定日期")
    private String banDate;

    /**
     * 价格类型；1：价格日历，2：价格列表
     */
    @TableField("RPPriceType")
    @TableFieldExtend(desc = "价格类型；1：价格日历，2：价格列表")
    private Integer priceType;

    /**
     * 第三方供应商ID，对应公共商户编码
     */
    @TableField("RPMerchantNumber")
    @TableFieldExtend(desc = "第三方供应商ID，对应公共商户编码")
    private String merchantNumber;

    /**
     * 是否同程推荐 1是，0不是
     */
    @TableField("RPRecommend")
    @TableFieldExtend(desc = "是否同程推荐 1是，0不是")
    private Integer recommend;

    /**
     * 订单量（订单清洗后获得）
     */
    @TableField("RPOrderNum")
    @TableFieldExtend(desc = "订单量（订单清洗后获得）")
    private Integer orderNum;

    /**
     * 人工排序 降序排列
     */
    @TableField("RPSort")
    @TableFieldExtend(desc = "人工排序 降序排列")
    private Integer sort;

    /**
     * 合作类型 72901自营 72902三方 72903携程 72904无合作 72905自营三方
     */
    @TableField("RPCooperationType")
    @TableFieldExtend(desc = "合作类型 72901自营 72902三方 72903携程 72904无合作 72905自营三方")
    private Integer cooperationType;

    /**
     * 最近一次可预订的旅游日期
     */
    @TableField("RPFirstBookDate")
    @TableFieldExtend(desc = "最近一次可预订的旅游日期")
    private LocalDateTime firstBookDate;

    /**
     * 下下次可预订的旅游日期
     */
    @TableField("RPSecondBookDate")
    @TableFieldExtend(desc = "下下次可预订的旅游日期")
    private LocalDateTime secondBookDate;

    /**
     * 最高佣金
     */
    @TableField("RPHighestCommission")
    @TableFieldExtend(desc = "最高佣金")
    private BigDecimal highestCommission;

    /**
     * 最低佣金
     */
    @TableField("RPLowestCommission")
    @TableFieldExtend(desc = "最低佣金")
    private BigDecimal lowestCommission;

    /**
     * 价格渠道配置 0各价格统一配置 1各价格独立设置
     */
    @TableField("RPPriceChannelFlag")
    @TableFieldExtend(desc = "价格渠道配置 0各价格统一配置 1各价格独立设置")
    private Integer priceChannelFlag;

    /**
     * 实名制手机号数量（默认0 全部游客 0 1位游客 1）
     */
    @TableField("RPPhoneNum")
    @TableFieldExtend(desc = "实名制手机号数量（默认0 全部游客 0 1位游客 1）")
    private Integer phoneNum;

    /**
     * 实名制姓名数量（默认0 全部游客 0 1位游客 1）
     */
    @TableField("RPNameNum")
    @TableFieldExtend(desc = "实名制姓名数量（默认0 全部游客 0 1位游客 1）")
    private Integer nameNum;

    /**
     * 0默认，1：手动上架，2：手动下架，3：自动上架，4：自动下架
     */
    @TableField("RPUpRowStatusType")
    @TableFieldExtend(desc = "0默认，1：手动上架，2：手动下架，3：自动上架，4：自动下架")
    private Integer upRowStatusType;

    /**
     * 上下架操作时间
     */
    @TableField("RPUpRowStatusTime")
    @TableFieldExtend(desc = "上下架操作时间")
    private LocalDateTime upRowStatusTime;

    /**
     * 适用包含人群类型（0：包含所有选择人群，1：包含一个选择人群）
     */
    @TableField("RPIncludeCrowdType")
    @TableFieldExtend(desc = "适用包含人群类型（0：包含所有选择人群，1：包含一个选择人群）")
    private Integer includeCrowdType;

    /**
     * 货币编号
     */
    @TableField("RPCurrencyCode")
    @TableFieldExtend(desc = "货币编号")
    private String currencyCode;

    /**
     * 货币名称
     */
    @TableField("RPCurrencyName")
    @TableFieldExtend(desc = "货币名称")
    private String currencyName;

    /**
     * 入园限制类型 1游玩日期后不可入园 2游玩日期后可入园
     */
    @TableField("RPEnterLimitType")
    @TableFieldExtend(desc = "入园限制类型 1游玩日期后不可入园 2游玩日期后可入园")
    private Integer enterLimitType;


}
