package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 促销运营设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePromotionOperate")
@TableExtend(desc = "促销运营设置表")
public class ResourcePromotionOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "RPOId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 促销Id（鸟巢）
     */
    @TableField("RPORuleId")
    @TableFieldExtend(desc = "促销Id（鸟巢）")
    private Long ruleId;

    /**
     * 有效期开始时间
     */
    @TableField("RPOBeginTime")
    @TableFieldExtend(desc = "有效期开始时间")
    private LocalDateTime beginTime;

    /**
     * 有效期截止时间
     */
    @TableField("RPOEndTime")
    @TableFieldExtend(desc = "有效期截止时间")
    private LocalDateTime endTime;

    /**
     * 自定义金额
     */
    @TableField("RPOCustomAmount")
    @TableFieldExtend(desc = "自定义金额")
    private BigDecimal customAmount;

    /**
     * 自定义金额（随机立减的最小值）
     */
    @TableField("RPOCustomMinAmount")
    @TableFieldExtend(desc = "自定义金额（随机立减的最小值）")
    private BigDecimal customMinAmount;

    /**
     * 自定义折扣
     */
    @TableField("RPOCustomDiscount")
    @TableFieldExtend(desc = "自定义折扣")
    private BigDecimal customDiscount;

    /**
     * 佣金规则：1=正佣，2=平佣，3=负佣，4=正平佣，5=平负佣，6=正平负佣
     */
    @TableField("RPOCommissionType")
    @TableFieldExtend(desc = "佣金规则：1=正佣，2=平佣，3=负佣，4=正平佣，5=平负佣，6=正平负佣")
    private Integer commissionType;

    /**
     * 会员规则：1=新会员，2=老会员，3=新老会员，4=限制人群
     */
    @TableField("RPOMemberType")
    @TableFieldExtend(desc = "会员规则：1=新会员，2=老会员，3=新老会员，4=限制人群")
    private Integer memberType;

    /**
     * 限制人群场景id(当RPOMemberType=4时需填写)
     */
    @TableField("RPOLimitMemberMapId")
    @TableFieldExtend(desc = "限制人群场景id(当RPOMemberType=4时需填写)")
    private Integer limitMemberMapId;

    /**
     * 今日订是否可用：1=可用，2=不可用
     */
    @TableField("RPOAllowToday")
    @TableFieldExtend(desc = "今日订是否可用：1=可用，2=不可用")
    private Integer allowToday;

    /**
     * 是否启用景区范围配置：0=否，1=是
     */
    @TableField("RPOUseSceneryRange")
    @TableFieldExtend(desc = "是否启用景区范围配置：0=否，1=是")
    private Integer useSceneryRange;

    /**
     * 是否启用政策范围配置：0=否，1=是
     */
    @TableField("RPOUsePolicyRange")
    @TableFieldExtend(desc = "是否启用政策范围配置：0=否，1=是")
    private Integer usePolicyRange;

    /**
     * 促销基础类别 数据字典211xx
     */
    @TableField("RPOTypeId")
    @TableFieldExtend(desc = "促销基础类别 数据字典211xx")
    private Integer typeId;

    /**
     * 来源：1=运营设置，2=商务设置
     */
    @TableField("RPOFromSource")
    @TableFieldExtend(desc = "来源：1=运营设置，2=商务设置")
    private Integer fromSource;

    /**
     * 有效性：0=无效，1=有效
     */
    @TableField("RPORowStatus")
    @TableFieldExtend(desc = "有效性：0=无效，1=有效")
    private Integer rowStatus;

    /**
     * 创建人工号[姓名]
     */
    @TableField("RPOCreateUser")
    @TableFieldExtend(desc = "创建人工号[姓名]")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "RPOCreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("RPOUpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "RPOUpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
