package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 出游人基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_traveller_base")
@TableExtend(desc = "出游人基础信息表")
public class PolicyTravellerBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 本表id
     */
    @TableField("id")
    @TableFieldExtend(desc = "本表id")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策长id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策长id")
    private Long policyId;

    /**
     * 是否需要游客信息：2全部 1一位 0否
     */
    @TableField("need_all_info")
    @TableFieldExtend(desc = "是否需要游客信息：2全部 1一位 0否")
    private Integer needAllInfo;

    /**
     * 是否需要证件 1需要 0不需要
     */
    @TableField("require_identification")
    @TableFieldExtend(desc = "是否需要证件 1需要 0不需要")
    private Integer requireIdentification;

    /**
     * 游客基础信息类型- 1姓名 2国内手机号 3国外手机号，多个','号隔开
     */
    @TableField("traveller_base_types")
    @TableFieldExtend(desc = "游客基础信息类型- 1姓名 2国内手机号 3国外手机号，多个','号隔开")
    private String travellerBaseTypes;

    /**
     * 证件类型Ids 多个','号隔开 60502开头数据字典
     */
    @TableField("identification_types")
    @TableFieldExtend(desc = "证件类型Ids 多个','号隔开 60502开头数据字典")
    private String identificationTypes;

    /**
     * 包含人群 0全部 1只需要1个
     */
    @TableField("need_all_crowd")
    @TableFieldExtend(desc = "包含人群 0全部 1只需要1个")
    private Integer needAllCrowd;

    /**
     * 是否有附加信息 1是 0否
     */
    @TableField("has_extend_info")
    @TableFieldExtend(desc = "是否有附加信息 1是 0否")
    private Integer hasExtendInfo;

    /**
     * 联系人信息 多个','号隔开 1邮箱 2国内手机号 3国外手机号
     */
    @TableField("contact_information")
    @TableFieldExtend(desc = "联系人信息 多个','号隔开 1邮箱 2国内手机号 3国外手机号")
    private String contactInformation;

    /**
     * 有效性 1有效0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;


}
