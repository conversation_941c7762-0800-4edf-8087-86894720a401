package com.ly.ticketfun.etl.mapper.tczbyresource;

import com.ly.ticketfun.etl.domain.tczbyresource.PolicyBaseInfo;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 政策基础表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
public interface PolicyBaseInfoMapper extends LaBaseMapper<PolicyBaseInfo> {
    List<PolicyBaseInfo> selectListByProductId(@Param("resourceId")Long resourceId,
                                               @Param("productId")Long productId);
}
