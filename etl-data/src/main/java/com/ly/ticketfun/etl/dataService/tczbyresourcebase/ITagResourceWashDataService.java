package com.ly.ticketfun.etl.dataService.tczbyresourcebase;

import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 标签清洗数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
public interface ITagResourceWashDataService extends IDbBaseService<TagResourceWashData> {
    List<TagDetailBo> selectTagDetailListByTagIdListAndResourceId(List<Long> tagIdList, Long resourceId);
}
