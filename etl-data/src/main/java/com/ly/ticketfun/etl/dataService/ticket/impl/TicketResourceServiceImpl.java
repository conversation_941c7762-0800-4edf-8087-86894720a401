package com.ly.ticketfun.etl.dataService.ticket.impl;

import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceBaseInfoService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketResourceAgg;
import com.ly.ticketfun.etl.dataService.ticket.ITicketResourceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TicketResourceServiceImpl implements ITicketResourceService {

    @Resource
    private IResourceBaseInfoService resourceBaseInfoService;

    @Resource
    private ITicketFunInnerApiService ticketFunInnerApiService;

    @Override
    public TicketResourceAgg fetchResource(Long resourceId) {
        if (resourceId == null || resourceId <= 0) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "景区id为空");
        }

        // 获取景区基础信息
        ResourceBaseInfo resourceBaseInfo = resourceBaseInfoService.queryByResourceId(resourceId);
        if (resourceBaseInfo == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_NOT_EXISTS);
        }

        // 获取时区信息
        GlobalRegionDateDto globalRegionDateDto = ticketFunInnerApiService.globalRegionSearch(
                resourceBaseInfo.getCountryId(),
                resourceBaseInfo.getCityId()
        );
        if (globalRegionDateDto == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_GET_FAIL,
                    String.valueOf(resourceId), "", "getTimezone"
            );
        }

        return TicketResourceAgg.builder()
                .resourceBaseInfo(resourceBaseInfo)
                .globalRegionDate(globalRegionDateDto)
                .build();
    }
}
