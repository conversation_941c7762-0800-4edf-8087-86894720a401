package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 促销基本设置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePromotionRule")
@TableExtend(desc = "促销基本设置")
public class ResourcePromotionRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "RPRId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键ID")
    private Long id;

    /**
     * 促销活动名称（外显名称）
     */
    @TableField("RPRName")
    @TableFieldExtend(desc = "促销活动名称（外显名称）")
    private String name;

    /**
     * 促销基础类别 211
     */
    @TableField("RPRTypeId")
    @TableFieldExtend(desc = "促销基础类别 211")
    private Integer typeId;

    /**
     * 促销基础类别名称
     */
    @TableField("RPRTypeName")
    @TableFieldExtend(desc = "促销基础类别名称")
    private String typeName;

    /**
     * 支付渠道ID 0全渠道
     */
    @TableField("RPRPayChannelId")
    @TableFieldExtend(desc = "支付渠道ID 0全渠道")
    private Integer payChannelId;

    /**
     * 支付渠道名称
     */
    @TableField("RPRPayChannelName")
    @TableFieldExtend(desc = "支付渠道名称")
    private String payChannelName;

    /**
     * 支付渠道产品ID 0全产品
     */
    @TableField("RPRPayProductId")
    @TableFieldExtend(desc = "支付渠道产品ID 0全产品")
    private Integer payProductId;

    /**
     * 支付渠道产品名称
     */
    @TableField("RPRPayProductName")
    @TableFieldExtend(desc = "支付渠道产品名称")
    private String payProductName;

    /**
     * 售卖渠道
     */
    @TableField("RPRSaleChannel")
    @TableFieldExtend(desc = "售卖渠道")
    private String saleChannel;

    /**
     * 是否唯一规则 0否，1是
     */
    @TableField("RPRSoleRule")
    @TableFieldExtend(desc = "是否唯一规则 0否，1是")
    private Integer soleRule;

    /**
     * 是否允许佣金为负
     */
    @TableField("RPRIsNegativeCommission")
    @TableFieldExtend(desc = "是否允许佣金为负")
    private Integer isNegativeCommission;

    /**
     * 限制活动参与次数0代表无限制，如一个人可以参加这个活动的次数
     */
    @TableField("RPRJoinNumber")
    @TableFieldExtend(desc = "限制活动参与次数0代表无限制，如一个人可以参加这个活动的次数")
    private Integer joinNumber;

    /**
     * 限制优惠总金额0代表无限制，如中信优惠20W
     */
    @TableField("RPRSaleTotalMoney")
    @TableFieldExtend(desc = "限制优惠总金额0代表无限制，如中信优惠20W")
    private BigDecimal saleTotalMoney;

    /**
     * 允许最大下单量
     */
    @TableField("RPROrderTicketLimit")
    @TableFieldExtend(desc = "允许最大下单量")
    private Integer orderTicketLimit;

    /**
     * 退改规则
     */
    @TableField("RPRRefundRule")
    @TableFieldExtend(desc = "退改规则")
    private Integer refundRule;

    /**
     * 价格促销类型，数据字典244（金额 折扣）
     */
    @TableField("RPRPriceType")
    @TableFieldExtend(desc = "价格促销类型，数据字典244（金额 折扣）")
    private Integer priceType;

    /**
     * 下单是否需要促销码
     */
    @TableField("RPRIsCode")
    @TableFieldExtend(desc = "下单是否需要促销码")
    private Integer isCode;

    /**
     * 自定义促销金额类型(走数据字典) 28301 自定义 28302 固定
     */
    @TableField("RPRAutoAmountTypeId")
    @TableFieldExtend(desc = "自定义促销金额类型(走数据字典) 28301 自定义 28302 固定")
    private Integer autoAmountTypeId;

    /**
     * 计算促销金额类型：0网上价参与计算  1协议价参与计算
     */
    @TableField("RPRCalculateAmountTypeId")
    @TableFieldExtend(desc = "计算促销金额类型：0网上价参与计算  1协议价参与计算")
    private Integer calculateAmountTypeId;

    /**
     * 促销开始时间
     */
    @TableField("RPRBeginDate")
    @TableFieldExtend(desc = "促销开始时间")
    private LocalDateTime beginDate;

    /**
     * 促销结束时间
     */
    @TableField("RPREndDate")
    @TableFieldExtend(desc = "促销结束时间")
    private LocalDateTime endDate;

    /**
     * 是否全局促销
     */
    @TableField("RPRIsGlobalPromotion")
    @TableFieldExtend(desc = "是否全局促销")
    private Integer isGlobalPromotion;

    /**
     * 项目属性
     */
    @TableField("RPRAttributionId")
    @TableFieldExtend(desc = "项目属性")
    private Integer attributionId;

    /**
     * 参与会员等级
     */
    @TableField("RPRMemberRank")
    @TableFieldExtend(desc = "参与会员等级")
    private Integer memberRank;

    /**
     * 展示文案字段
     */
    @TableField("RPPDescription")
    @TableFieldExtend(desc = "展示文案字段")
    private String rPPDescription;

    /**
     * 自由行活动模板ID（兼容老下单要用）
     */
    @TableField("RPROldTemplateId")
    @TableFieldExtend(desc = "自由行活动模板ID（兼容老下单要用）")
    private Integer oldTemplateId;

    /**
     * 原活动ID，用于老系统查询
     */
    @TableField("RPRRelationOldId")
    @TableFieldExtend(desc = "原活动ID，用于老系统查询")
    private Long relationOldId;

    /**
     * 有效性
     */
    @TableField("RPRRowStatus")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 排序值
     */
    @TableField("RPRSort")
    @TableFieldExtend(desc = "排序值")
    private Integer sort;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 屏蔽后台展示：0 不展示 1展示
     */
    @TableField("RPRScreenDisplay")
    @TableFieldExtend(desc = "屏蔽后台展示：0 不展示 1展示")
    private Integer screenDisplay;

    /**
     * 是否前台筛选 1:是  0:否
     */
    @TableField("RPRIsFrontSelect")
    @TableFieldExtend(desc = "是否前台筛选 1:是  0:否")
    private Integer isFrontSelect;

    /**
     * 前台描述
     */
    @TableField("RPRFrontCommont")
    @TableFieldExtend(desc = "前台描述")
    private String frontCommont;


}
