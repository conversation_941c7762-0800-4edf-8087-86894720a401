package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourcePolicy;
import com.ly.ticketfun.etl.mapper.tczbyresource.ResourcePolicyMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourcePolicyService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 资源政策表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class ResourcePolicyServiceImpl extends DbBaseServiceImpl<ResourcePolicyMapper, ResourcePolicy> implements IResourcePolicyService {
    @Resource
    private ResourcePolicyMapper resourcePolicyMapper;

    @Override
    public List<ResourcePolicy> selectResourcePolicyList(Long resourceId, Long productId) {
        return resourcePolicyMapper.selectResourcePolicyListByProductId(resourceId, productId);
    }
}
