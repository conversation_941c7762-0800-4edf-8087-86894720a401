package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.util.List;

@Data
public class AddressTimeDto {
    /**
     * 地址
     */
    private String address;
    /**
     * 地址类型
     */
    private String addressType;
    /**
     * 时间列表
     */
    private List<Time> timeList;

    @Data
    public static class Time {
        /**
         * 开始结束时间
         */
        private String startEndTime;
        /**
         * 时间描述
         */
        private String desc;
    }
}
