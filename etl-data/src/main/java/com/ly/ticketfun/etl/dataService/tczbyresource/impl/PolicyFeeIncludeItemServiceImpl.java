package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.PolicyFeeIncludeItem;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyFeeIncludeItemMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyFeeIncludeItemService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 费用包含主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Service
public class PolicyFeeIncludeItemServiceImpl extends DbBaseServiceImpl<PolicyFeeIncludeItemMapper, PolicyFeeIncludeItem> implements IPolicyFeeIncludeItemService {

}
