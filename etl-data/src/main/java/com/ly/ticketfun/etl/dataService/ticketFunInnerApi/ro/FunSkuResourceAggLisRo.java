package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ro;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class FunSkuResourceAggLisRo implements Serializable {
    private static final long serialVersionUID = 5483260801181537461L;

    private List<String> mainResourceSerialIdList;

    private List<String> subResourceSerialIdList;

    private List<String> skuResourceSerialId;

    private Boolean needCanAdvance;

    private Boolean needPrice;
}
