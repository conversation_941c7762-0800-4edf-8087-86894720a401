package com.ly.ticketfun.etl.domain.templateMarketing.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@Data
public class MarketingInfoDto implements Serializable {
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 套餐id
     */
    private String packageId;

    /**
     * 资源id
     */
    private String skuId;

    /**
     * 销售终端Code todo 枚举
     */
    private String salePointCode;

    /**
     * 销售终端名称
     */
    private String salePointName;

    /**
     * 销售渠道id TODO 枚举
     */
    private String saleChannelId;
    /**
     * 销售渠道名称
     */
    private String saleChannelName;

    /**
     * 营销详情
     */
    private List<MarketingDetailInfoDto> marketingDetailInfoList;
}
