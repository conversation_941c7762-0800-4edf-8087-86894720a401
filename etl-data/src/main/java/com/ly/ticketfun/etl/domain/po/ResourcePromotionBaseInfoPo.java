package com.ly.ticketfun.etl.domain.po;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-9-15
 * @note
 */
@Data
public class ResourcePromotionBaseInfoPo {
    /**
     * 促销主键ID
     */
    private Long RPRId;

    /**
     * 促销基础类别 211
     */
    private Integer RPRTypeId;

    /**
     * 促销基础类别名称
     */
    private String RPRTypeName;

    /**
     * 原活动ID，用于老系统查询
     */
    private Long RPRRelationOldId;

    /**
     * 促销名称
     */
    private String RPRName;

    /**
     * 售卖渠道
     */
    private String RPRSaleChannel;

    /**
     * 限制活动参与次数0代表无限制，如一个人可以参加这个活动的次数
     */
    private Integer RPRJoinNumber;

    /**
     * 限制优惠总金额0代表无限制，如中信优惠20W
     */
    private BigDecimal RPRSaleTotalMoney;

    /**
     * 退改规则 24102 按价格退款,24101 按票数退款
     */
    private Integer RPRRefundRule;

    /**
     * 价格促销类型 24301折扣, 24302金额
     */
    private Integer RPRPriceType;

    /**
     * 下单是否需要促销码
     */
    private Integer RPRIsCode;

    /**
     * 自定义促销金额类型(走数据字典) 28301 自定义 28302 固定
     */
    private Integer RPRAutoAmountTypeId;

    /**
     * 计算促销金额类型：0网上价参与计算 1协议价参与计算
     */
    private Integer RPRCalculateAmountTypeId;

    /**
     * 促销开始时间
     */
    private Date RPRBeginDate;

    /**
     * 促销结束时间
     */
    private Date RPREndDate;

    /**
     * 优惠开始金额
     */
    private BigDecimal RPRDMinAmount;

    /**
     * 优惠结束金额
     */
    private BigDecimal RPRDMaxAmount;

    /**
     * 折扣
     */
    private BigDecimal RPRDDiscount;

    /**
     * 涉及促销金额
     */
    private BigDecimal RPRDMoney;

    /**
     * 1文旅承担 2非文旅承担
     */
    private Integer RPRDIsAllUnderOwn;

    /**
     * 文案描述
     */
    private String RPPDescription;

    /**
     * 是否前台筛选 1:是 0:否
     */
    private Integer RPRIsFrontSelect;

    /**
     * 前台一句话描述
     */
    private String RPRFrontCommont;

    /**
     * 是否允许佣金为负1是 0否
     */
    private Integer RPRIsNegativeCommission;


}
