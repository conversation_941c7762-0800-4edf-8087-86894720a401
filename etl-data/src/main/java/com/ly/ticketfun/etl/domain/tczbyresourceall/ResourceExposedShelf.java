package com.ly.ticketfun.etl.domain.tczbyresourceall;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 搜索使用货架辅助外显状态（1对多）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_exposed_shelf")
@TableExtend(desc = "搜索使用货架辅助外显状态（1对多）")
public class ResourceExposedShelf implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DbrGuid")
    @TableFieldExtend(desc = "")
    private Long dbrguid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 外显可有的货架信息
（29905日游，29910讲解，29909人气酒店等）
     */
    @TableField("show_shelf_type")
    @TableFieldExtend(desc = "外显可有的货架信息（29905日游，29910讲解，29909人气酒店等）")
    private Integer showShelfType;

    /**
     * 类型名称
     */
    @TableField("show_shelf_type_name")
    @TableFieldExtend(desc = "类型名称")
    private String showShelfTypeName;

    /**
     * 外显个数（>0即代表有数据）
     */
    @TableField("show_Count")
    @TableFieldExtend(desc = "外显个数（>0即代表有数据）")
    private Integer showCount;

    /**
     * 有效性 1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "更新时间")
    private LocalDateTime updatetime;


}
