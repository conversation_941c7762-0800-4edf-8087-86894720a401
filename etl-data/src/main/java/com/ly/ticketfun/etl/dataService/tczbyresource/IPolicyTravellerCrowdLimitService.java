package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdLimit;

import java.util.List;

/**
 * <p>
 * 针对人群限制表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public interface IPolicyTravellerCrowdLimitService extends IDbBaseService<PolicyTravellerCrowdLimit> {

    /**
     * 获取出游人人群限制列表
     *
     * @param resourceId  景区id
     * @param policyId    政策id
     * @param crowdIdList 人群主键列表
     * @return 人群限制列表
     */
    List<PolicyTravellerCrowdLimit> queryListByPolicyId(Long resourceId, Long policyId, List<Long> crowdIdList);
}
