package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.LogField;
import com.ly.localactivity.framework.annotation.model.UniqueCompareField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.base.LanguageEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ProductResourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ProductResourceDto implements Serializable {
    private static final long serialVersionUID = 8637804966369000109L;
    /**
     * 产品id
     */
    @UniqueCompareField
    @LogField
    private String productId;
    /**
     * 品类
     */
    private ResourceCategoryDto category;
    /**
     * 分类
     */
    private ResourceClassifyDto classify;
    /**
     * 行程天数
     */
    private List<String> journeyDays;
    /**
     * 名称
     */
    private String name;
    /**
     * 副名称
     */
    private String subName;
    /**
     * 出发地列表
     */
    private List<CountryAreaDto> departureList;
    /**
     * 目地列表
     */
    private List<CountryAreaDto> destinationList;
    /**
     * poiId（景点ID）列表
     */
    private List<Long> poiIdList;
    /**
     * 时区
     */
    private Integer timeZone;
    /**
     * 服务语言列表
     */
    @EnumField(enumClazz = LanguageEnum.class)
    private List<String> serviceLanguageList;
    /**
     * 产品头图地址
     */
    private String headImageUrl;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 视频压缩地址
     */
    private String videoCompressUrl;
    /**
     * 视频封面地址
     */
    private String videoCoverUrl;
    /**
     * 图文详情
     */
    private String description;
    /**
     * 文件列表
     */
    private List<ResourceFileDto> fileList;
    /**
     * 产品亮点
     */
    private List<ResourceSalePointDto> salePointList;
    /**
     * 产品标签
     */
    private List<ResourceLabelDto> productLabels;
    /**
     * 销售属性列表
     */
    private List<ProductSalePropertyDto> salePropertyList;
    /**
     * 资源分类（人群）
     */
    private List<ResourceBandInfoDto> bandInfoList;
    /**
     * 条款列表
     */
    private List<ClauseTypeDto> clauseList;
    /**
     * 逻辑扩展
     */
    private List<LogicExtendDto> logicExtendList;
    /**
     * 是否参与最小价外显计算
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer partakeMinPrice;
    /**
     * 售卖状态
     */
    @EnumField(enumClazz = ProductResourceEnum.SaleStatus.class)
    private String saleStatus;
    /**
     * 是否有售卖时间限制
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer saleTimeLimit;
    /**
     * 售卖开始时间，套餐最早时间清洗
     */
    private Long saleBeginTime;
    /**
     * 售卖结束时间，套餐最晚时间清洗
     */
    private Long saleEndTime;
    /**
     * 排序值
     */
    private Integer productSort;
    /**
     * 产品限制条件
     */
    private List<ProductShowLimitVo> showLimitList;

    /**
     * 产品外显限制条件
     */
    @Data
    public class ProductShowLimitVo {
        /**
         * 分类id(唯一id)
         */
        private Integer classifyId;
        /**
         * 分类枚举
         */
        private String classifyCode;
        /**
         * 适用人群
         */
        private String suitCrowd;
        /**
         * 标题
         */
        private String title;
        /**
         * 描述
         */
        private String description;
        /**
         * 短说明
         */
        private String shortDescription;
    }
}
