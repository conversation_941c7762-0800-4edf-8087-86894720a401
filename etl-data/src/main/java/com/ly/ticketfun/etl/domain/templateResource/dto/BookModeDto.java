package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.BookModeEnum;
import com.ly.ticketfun.etl.common.enums.ticket.UseLimitDateEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 预订配置
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class BookModeDto implements Serializable {
    private static final long serialVersionUID = -1331033596009926433L;
    /**
     * 价格模式
     */
    @EnumField(enumClazz = BookModeEnum.PriceMode.class)
    private String priceMode;

    /**
     * 使用时限类型
     */
    @EnumField(enumClazz = BookModeEnum.UsagePeriodType.class)
    private String usagePeriodType;
    /**
     * 有效天数
     */
    private String validityDays;
    /**
     * 有效期开始时间
     */
    private Long validityPeriodStart;
    /**
     * 有效期结束时间
     */
    private Long validityPeriodEnd;
    /**
     * 超有效期可以使用
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer beyondValidityPeriodCanUse;
    //已放置saleChannel
    //    /**
    //     * 是否有售卖时间限制
    //     */
    //    @EnumField(enumClazz = WhetherEnum.class)
    //    private Integer saleTimeLimit;
    //    /**
    //     * 售卖开始时间
    //     */
    //    private Long saleBeginTime;
    //    /**
    //     * 售卖结束时间
    //     */
    //    private Long saleEndTime;
    /**
     * 是否预约抢
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer precastBook;
    /**
     * 预约抢天数
     */
    private Integer precastBookDays;

    /**
     * 日期限制
     */
    @EnumField(enumClazz = UseLimitDateEnum.class)
    private Integer useLimitType;
    /**
     * 日期限制明细:周，json数组
     */
    private String useLimitWeekList;
    /**
     * 日期限制明细:日期，json数组
     */
    private String useLimitDateList;

    /**
     * 使用次数限制
     */
    private Integer useLimitTimes;
}
