package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <p>
 * B07-政策分销渠道加价规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PolicyChannelSales")
@TableExtend(desc = "B07-政策分销渠道加价规则")
public class PolicyChannelSales implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键18位
     */
    @TableField("PCSId")
    @TableFieldExtend(desc = "主键18位")
    private Long id;

    /**
     * 资源ID，分片键
     */
    @TableField("PCSResourceId")
    @TableFieldExtend(desc = "资源ID，分片键")
    private Long resourceId;

    /**
     * 门票产品策略ID
     */
    @TableField("PCSPolicyID")
    @TableFieldExtend(desc = "门票产品策略ID")
    private Long policyID;

    /**
     * 产品渠道ID
     */
    @TableField("PCSChannelID")
    @TableFieldExtend(desc = "产品渠道ID")
    private Integer channelID;

    /**
     * 渠道加价
     */
    @TableField("PCSRevisedAmount")
    @TableFieldExtend(desc = "渠道加价")
    private BigDecimal revisedAmount;

    /**
     * 是否有效 1有效 0无效
     */
    @TableField("PCSRowStatus")
    @TableFieldExtend(desc = "是否有效 1有效 0无效")
    private Integer rowStatus;

    /**
     * 1：网上价 2：协议价 3：包票价
     */
    @TableField("PCSPriceType")
    @TableFieldExtend(desc = "1：网上价 2：协议价 3：包票价")
    private Integer priceType;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;


}
