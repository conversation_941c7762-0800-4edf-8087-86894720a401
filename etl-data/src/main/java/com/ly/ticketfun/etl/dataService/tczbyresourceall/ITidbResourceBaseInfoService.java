package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresourceall.dto.BasicResourceInfoDto;

/**
 * <p>
 * 资源表，分片键：资源ID 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceBaseInfoService extends IDbBaseService<ResourceBaseInfo> {

    /**
     * 获取基础资源信息
     * @param resourceId 景区ID
     * @return 基础资源信息
     */
    BasicResourceInfoDto getBasicResourceInfo(Long resourceId);

    /**
     * 获取有效资源信息
     * @param resourceId 景区ID
     * @return 有效资源信息
     */
    ResourceBaseInfo queryValidByResourceId(Long resourceId);
}
