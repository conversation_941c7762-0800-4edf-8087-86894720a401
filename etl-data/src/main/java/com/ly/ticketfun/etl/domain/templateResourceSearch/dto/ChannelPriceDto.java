package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 景区渠道价格
 */
@Data
public class ChannelPriceDto {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 销售终端Code todo 枚举
     */
    private String salePointCode;
    /**
     * 销售终端名称
     */
    private String salePointName;
    /**
     * 销售渠道id TODO 枚举
     */
    private String saleChannelId;
    /**
     * 销售渠道名称
     */
    private String saleChannelName;
    /**
     * 销售RefId
     */
    private String saleRefId;

    /**
     * 价格类型
     * 0无外显 1 网络卖价 2门市价 3 文案描述
     */
    @EnumField(enumClazz = ResourceSearchEnum.PriceTypeEnum.class)
    private String priceType;

    /**
     * 外显价格(RMB)
     */
    private String showPriceDesc;

    /**
     * 减价(RMB)
     */
    private BigDecimal reducePrice;

    /**
     * 外显价格前缀
     */
    private String prefixDesc;

    /**
     * 渠道可用红包批次号 todo 待定，是否放这边
     */
    private String batchNos;

}
