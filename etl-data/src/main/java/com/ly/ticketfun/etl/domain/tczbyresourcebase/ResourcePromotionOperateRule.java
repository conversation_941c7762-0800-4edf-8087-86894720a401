package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 促销运营设置操作表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePromotionOperateRule")
@TableExtend(desc = "促销运营设置操作表")
public class ResourcePromotionOperateRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "RPORId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 促销运营设置主表Id
     */
    @TableField("RPORObjectId")
    @TableFieldExtend(desc = "促销运营设置主表Id")
    private Long objectId;

    /**
     * 类型：1=景区，2=政策
     */
    @TableField("RPORType")
    @TableFieldExtend(desc = "类型：1=景区，2=政策")
    private Integer type;

    /**
     * 操作类型：1=新增，2=排除
     */
    @TableField("RPOROperateType")
    @TableFieldExtend(desc = "操作类型：1=新增，2=排除")
    private Integer operateType;

    /**
     * 景区Id
     */
    @TableField("RPORSceneryId")
    @TableFieldExtend(desc = "景区Id")
    private Long sceneryId;

    /**
     * 政策Id
     */
    @TableField("RPORPolicyId")
    @TableFieldExtend(desc = "政策Id")
    private Long policyId;

    /**
     * 政策短Id（门票Id）
     */
    @TableField("RPORTicketId")
    @TableFieldExtend(desc = "政策短Id（门票Id）")
    private Long ticketId;

    /**
     * 有效性：0=无效，1=有效
     */
    @TableField("RPORRowStatus")
    @TableFieldExtend(desc = "有效性：0=无效，1=有效")
    private Integer rowStatus;

    /**
     * 创建人工号[姓名]
     */
    @TableField("RPORCreateUser")
    @TableFieldExtend(desc = "创建人工号[姓名]")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "RPORCreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("RPORUpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "RPORUpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
