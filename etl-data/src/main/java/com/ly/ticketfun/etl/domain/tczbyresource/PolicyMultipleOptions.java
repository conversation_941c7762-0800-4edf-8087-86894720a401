package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 政策多选项关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_multiple_options")
@TableExtend(desc = "政策多选项关联表")
public class PolicyMultipleOptions implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 资源id（分片键）
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id（分片键）")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 关联id
     */
    @TableField("relation_id")
    @TableFieldExtend(desc = "关联id")
    private Long relationId;

    /**
     * 选项类型
     * 1游客信息证件类型
     * 2游客信息基础信息
     * 3电子凭证
     * 4支持证件
     * 5短信信息8入园凭证
     * 9购票人信息  10销售场景
     */
    @TableField("select_type_id")
    @TableFieldExtend(desc = "选项类型 1游客信息证件类型 2游客信息基础信息 3电子凭证 4支持证件 5短信信息8入园凭证 9购票人信息  10销售场景 ")
    private Integer selectTypeId;

    /**
     * 选项名称
     */
    @TableField("select_type_name")
    @TableFieldExtend(desc = "选项名称")
    private String selectTypeName;

    /**
     * 选项值
     */
    @TableField("select_value")
    @TableFieldExtend(desc = "选项值")
    private Integer selectValue;

    /**
     * 选项值说明
     */
    @TableField("select_value_des")
    @TableFieldExtend(desc = "选项值说明")
    private String selectValueDes;

    /**
     * 有效性 1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;


}
