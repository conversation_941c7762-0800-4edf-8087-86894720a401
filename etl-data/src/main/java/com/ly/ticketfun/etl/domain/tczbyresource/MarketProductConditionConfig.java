package com.ly.ticketfun.etl.domain.tczbyresource;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 产品（货架）适用条件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("market_product_condition_config")
@TableExtend(desc = "产品（货架）适用条件")
public class MarketProductConditionConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合使用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合使用")
    private Long dbrGuid;

    /**
     * 唯一id
     */
    @TableField("id")
    @TableFieldExtend(desc = "唯一id")
    private Long id;

    /**
     * 景区id(分片键)
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id(分片键)")
    private Long resourceId;

    /**
     * 产品(货架)id
     */
    @TableField("product_id")
    @TableFieldExtend(desc = "产品(货架)id")
    private Long productId;

    /**
     * 人群分类（数据字典603）
     */
    @TableField("crowd_category")
    @TableFieldExtend(desc = "人群分类（数据字典603）")
    private Integer crowdCategory;

    /**
     * 人群分类名称
     */
    @TableField("crowd_category_name")
    @TableFieldExtend(desc = "人群分类名称")
    private String crowdCategoryName;

    /**
     * 产品与人群分类关联id
     */
    @TableField("product_crowd_relation_id")
    @TableFieldExtend(desc = "产品与人群分类关联id")
    private Long productCrowdRelationId;

    /**
     * 适用人群（数据字典233）
     */
    @TableField("apply_crowd")
    @TableFieldExtend(desc = "适用人群（数据字典233）")
    private Integer applyCrowd;

    /**
     * 适用人群名称
     */
    @TableField("apply_crowd_name")
    @TableFieldExtend(desc = "适用人群名称")
    private String applyCrowdName;

    /**
     * 类型：0年龄  1身高
     */
    @TableField("type_id")
    @TableFieldExtend(desc = "类型：0年龄  1身高")
    private Integer typeId;

    /**
     * 限制区间开始值
     */
    @TableField("begin_value")
    @TableFieldExtend(desc = "限制区间开始值")
    private BigDecimal beginValue;

    /**
     * 限制区间开始是否包含 （0 不包含  1包含）
     */
    @TableField("begin_include")
    @TableFieldExtend(desc = "限制区间开始是否包含 （0 不包含  1包含）")
    private Integer beginInclude;

    /**
     * 限制区间结束值
     */
    @TableField("end_value")
    @TableFieldExtend(desc = "限制区间结束值")
    private BigDecimal endValue;

    /**
     * 限制区间结束否包含（0 不包含 1包含）
     */
    @TableField("end_include")
    @TableFieldExtend(desc = "限制区间结束否包含（0 不包含 1包含）")
    private Integer endInclude;

    /**
     * 计算精度（0 精确到年   1 精确到日）
     */
    @TableField("compute_precision")
    @TableFieldExtend(desc = "计算精度（0 精确到年   1 精确到日）")
    private Integer computePrecision;

    /**
     * 限制条件
     */
    @TableField("limit_condition")
    @TableFieldExtend(desc = "限制条件")
    private String limitCondition;

    /**
     * 限制描述
     */
    @TableField("limit_desc")
    @TableFieldExtend(desc = "限制描述")
    private String limitDesc;

    /**
     * 性别限制：0无限制  1男  2女
     */
    @TableField("gender_type")
    @TableFieldExtend(desc = "性别限制：0无限制  1男  2女")
    private Integer genderType;

    /**
     * 数据有效性（0无效  1有效）
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性（0无效  1有效）")
    private Integer rowStatus;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
