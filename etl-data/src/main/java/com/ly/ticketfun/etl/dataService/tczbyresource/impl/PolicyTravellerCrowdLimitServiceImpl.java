package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerCrowdLimitService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdLimit;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyTravellerCrowdLimitMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 针对人群限制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyTravellerCrowdLimitServiceImpl extends DbBaseServiceImpl<PolicyTravellerCrowdLimitMapper, PolicyTravellerCrowdLimit>
        implements IPolicyTravellerCrowdLimitService {

    @Resource
    private PolicyTravellerCrowdLimitMapper policyTravellerCrowdLimitMapper;

    @Override
    public List<PolicyTravellerCrowdLimit> queryListByPolicyId(Long resourceId, Long policyId, List<Long> crowdIdList) {
        LambdaQueryWrapper<PolicyTravellerCrowdLimit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyTravellerCrowdLimit::getResourceId, resourceId)
                .eq(PolicyTravellerCrowdLimit::getPolicyId, policyId)
                .eq(PolicyTravellerCrowdLimit::getRowStatus, DataFlagEnum.VALID.getValue());
        if (CollectionUtils.isNotEmpty(crowdIdList)) {
            queryWrapper.in(PolicyTravellerCrowdLimit::getCrowdInfoId, crowdIdList);
        }

        return policyTravellerCrowdLimitMapper.selectList(queryWrapper);
    }
}
