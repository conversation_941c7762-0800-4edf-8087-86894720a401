package com.ly.ticketfun.etl.dataService.tczbyresourceall.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceTagInfo;
import com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceTagInfoMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.ITidbResourceTagInfoService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 搜索使用标签信息(1对多)  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
//@Service
public class TidbResourceTagInfoServiceImpl extends DbBaseServiceImpl<TidbResourceTagInfoMapper, ResourceTagInfo> implements ITidbResourceTagInfoService {

    @Override
    public List<ResourceTagInfo> queryValidListByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceTagInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceTagInfo::getResourceId, resourceId)
                .eq(ResourceTagInfo::getRowStatus, DataFlagEnum.VALID.getValue())
                .le(ResourceTagInfo::getBeginTime, DateUtil.now())
                .ge(ResourceTagInfo::getEndTime, DateUtil.now())
                .orderByDesc(ResourceTagInfo::getTagParentSort, ResourceTagInfo::getTagSort);
        return this.queryList(queryWrapper);
    }
}
