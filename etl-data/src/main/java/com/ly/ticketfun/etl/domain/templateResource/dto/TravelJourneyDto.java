package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.base.CurrencyEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceGiftEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.TravelJourneyDetailEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 行程
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class TravelJourneyDto implements Serializable {
    private static final long serialVersionUID = -1165585683410703380L;
    /**
     * 行程id
     */
    private Long journeyId;
    /**
     * 行程名称
     */
    private String journeyName;
    /**
     * 行程天数
     */
    private Integer journeyDays;
    /**
     * 出发地区id
     */
    private Long departureCountryAreaId;
    /**
     * 出发地区名称
     */
    private String departureCountryAreaName;
    /**
     * 线路明细
     */
    private List<TravelJourneyDetailDto> travelJourneyDetailList;

    /**
     * 线路明细
     *
     * <AUTHOR>
     * @date 2025/09/01
     */
    @Data
    public static class TravelJourneyDetailDto implements Serializable {
        private static final long serialVersionUID = -697295136729558406L;
        /**
         * 一级类型
         */
        private Integer firstType;
        /**
         * 一级类型描述
         */
        private String firstTypeDesc;
        /**
         * 二级类型
         */
        private Integer secondType;
        /**
         * 二级类型描述
         */
        private String secondTypeDesc;
        /**
         * 三级类型
         */
        private Integer thirdType;
        /**
         * 三级类型描述
         */
        private String thirdTypeDesc;
        /**
         * 开始时间
         */
        private String startTime;
        /**
         * 活动小时数
         */
        private Integer activityHours;
        /**
         * 活动分钟数
         */
        private Integer activityMinutes;
        /**
         * 详细信息 原 typeDetailInfo
         */
        private String detailInfos;
        /**
         * 描述信息
         */
        private String descriptions;


        /**
         * 关联poi
         */
        private List<TravelJourneyPoiDto> travelJourneyPoiList;
    }


    /**
     * 线路poi明细
     *
     * <AUTHOR>
     * @date 2025/09/01
     */
    @Data
    public static class TravelJourneyPoiDto implements Serializable {
        private static final long serialVersionUID = -697295136729558406L;
        /**
         * poiId
         */
        private Long poiId;
        /**
         * poi名称
         */
        private String poiName;
        /**
         * 费用包含类型
         */
        @EnumField(enumClazz = TravelJourneyDetailEnum.PaidServiceType.class)
        private String paidServiceType;
        /**
         * 货币类型
         */
        @EnumField(enumClazz = CurrencyEnum.class)
        private String currencyType;
        /**
         * 花费金额
         */
        private BigDecimal costAmount;
        /**
         * 花费单位
         */
        @EnumField(enumClazz = TravelJourneyDetailEnum.CostUnitEnum.class)
        private String costUnit;
        /**
         * 参观类型
         */
        @EnumField(enumClazz = TravelJourneyDetailEnum.VisitType.class)
        private String visitType;
        /**
         * 预约类型
         */
        @EnumField(enumClazz = TravelJourneyDetailEnum.AdvanceReservation.class)
        private String advanceReservation;
        /**
         * 是否含讲解
         */
        @EnumField(enumClazz = TravelJourneyDetailEnum.IncludeExplain.class)
        private String includeExplain;

    }
}
