package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.ResourceFileDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 搜索需要的资源POI信息，导航栏信息走API接口获取，POI游玩指南走接口获取
 */
@Data
public class ResourceSearchInfoDto implements Serializable {

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源副标题
     */
    private String resourceSubName;

    /**
     * 品类 Code
     */
    private String firstCategoryId="10";

    /**
     * 品类名称
     */
    private String firstCategoryName="门票";

    /**
     * 二级品类 Code
     */
    private String secondCategoryId="";

    /**
     * 二级品类名称
     */
    private String secondCategoryName="";

    /**
     * 游玩天数
     */
    private String travelDays="1";

    /**
     * 资源一句话描述
     */
    private String resourceSummary;

    /**
     * 资源首图
     */
    private String resourceHeadImage;

    /**
     * 资源视频
     */
    private String resourceVideo;

    /**
     * 资源视频压缩后地址
     */
    private String resourceVideo360;

    /**
     * 资源视频封面
     */
    private String resourceVideoCover;

    /**
     * 开业时间
     */
    private String establishmentDate;

    /**
     * 资源地址
     */
    private String resourceAddress;

    /**
     * 经度
     */
    private String baiduLon;

    /**
     * 纬度
     */
    private String baiduLat;

    /**
     * 首次上架时间
     */
    private Long effectiveTime;

    /**
     * 点评AI一句话
     */
    private String commentSummary;

    /**
     * 是否含赠品（1 是，0 否）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer hadGift;

    /**
     * 时区
     */
    private Integer timeZone=0;

    /**
     * 商圈id
     */
    private Long tradeAreaId;

    /**
     * 商圈名称
     */
    private String tradeAreaName;

    /**
     * 是否有效（1 有效，0 无效）
     */
    @EnumField(enumClazz = DataFlagEnum.class)
    private Integer validStatus;

    /**
     * 是否可预定
     */
    private Integer canBookStatus;

    /**
     * 测试状态（1：是，0：否）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer testStatus;

    /**
     * 对客展示状态（1：是对客，0：屏蔽对客）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer showStatus;

    /**
     * 合作类型是否自营（1：自营，0：非自营）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer cooperationType;

    /**
     * 出价状态
     */
    private Integer priceStatus;

    /**
     * 是否有价格优势（枚举：M B L）
     */
    private String mblStatus;

    /**
     * 退改状态
     */
    @EnumField(enumClazz = ResourceSearchEnum.RefundTypeEnum.class)
    private String refundType;

    /**
     * 条件退时间（门票：2，代表提前2天退）
     */
    private Integer refundTime;

    /**
     * 是否随买随用（1：是，0 否）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer useStatus;

    /**
     * 信息完善度
     */
    private BigDecimal maintenanceScore;

    /**
     * 榜单信息
     */
    private String rankInfo;

    /**
     * 资源星级（61201 1A，61202 2A，61203 3A，61204 4A，61205 5A，61206 其它）
     */
    @EnumField(enumClazz = ResourceSearchEnum.ResourceGradeEnum.class)
    private String resourceGrade;

    /**
     * 是否预约（0 否 1是）
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer appointment;

    /**
     * 景区营业状态
     * todo
     */
    @EnumField(enumClazz = ResourceSearchEnum.BusinessStatusEnum.class)
    private Integer businessStatus;

    /**
     * 供应商Id
     */
    private Long supplierId;

    /**
     * 4个月的每天可订情况
     * 格式：20250601:1235464733[最晚可订时间戳]）
     * 多数据用'|'分隔
     */
    private String datesCanSales;

    /**
     * 每天开闭园时间
     */
    private String daysOpenTime;

    /**
     * 是否红色景区
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer revolutionResource;


    /**
     * 别名主题数据
     */
    private List<SubjectAliasesDto> subjectAliasesList;

    /**
     * 地理位置数据
     */
    private List<LocationDto> locationList;

    /**
     * 逻辑类数据
     */
    private List<SearchLogicExtendDto> logicalData;

    /**
     * 渠道金额信息
     */
    private List<ChannelPriceDto> channelPriceList;

    /**
     * 搜索标签信息
     */
    private List<SearchLabelInfoDto> searchLabelInfoList;

    /**
     * 亮点信息
     */
    private List<HighLightInfoDto> highLightInfoList;

    /**
     * 文件列表(图片视频列表)
     */
    private List<ResourceFileDto> fileList;

    /**
     * 设施信息
     */
    private List<FacilityInfoDto> facilityInfoList;

    /**
     * 热门线路（日游独有）
     * todo @徐枫
     */
    private List<PopularRoutesDto> popularRoutesList;

    /**
     * 货架外显信息(门票独有)
     */
    private List<MpShelfShowDto> shelfShowList;

}
