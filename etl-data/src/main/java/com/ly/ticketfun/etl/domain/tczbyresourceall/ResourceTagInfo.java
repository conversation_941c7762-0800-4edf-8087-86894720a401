package com.ly.ticketfun.etl.domain.tczbyresourceall;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 搜索使用标签信息(1对多) 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_tag_info")
@TableExtend(desc = "搜索使用标签信息(1对多) ")
public class ResourceTagInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DbrGuid")
    @TableFieldExtend(desc = "")
    private Long dbrguid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 标签大类id
     */
    @TableField("tag_parent_id")
    @TableFieldExtend(desc = "标签大类id")
    private Long tagParentId;

    /**
     * 父标签Code
     */
    @TableField("tag_parent_code")
    @TableFieldExtend(desc = "父标签Code")
    private String tagParentCode;

    /**
     * 父标签名称
     */
    @TableField("tag_parent_name")
    @TableFieldExtend(desc = "父标签名称")
    private String tagParentName;

    /**
     * 父类排序（倒序）
     */
    @TableField("tag_parent_sort")
    @TableFieldExtend(desc = "父类排序（倒序）")
    private Integer tagParentSort;

    /**
     * 标签id
     */
    @TableField("tag_id")
    @TableFieldExtend(desc = "标签id")
    private Long tagId;

    /**
     * 标签code
     */
    @TableField("tag_code")
    @TableFieldExtend(desc = "标签code")
    private String tagCode;

    /**
     * 标签名称
     */
    @TableField("tag_name")
    @TableFieldExtend(desc = "标签名称")
    private String tagName;

    /**
     * 标签类型
     */
    @TableField("tag_type")
    @TableFieldExtend(desc = "标签类型")
    private Integer tagType;

    /**
     * 氛围图一
     */
    @TableField("tag_first_url")
    @TableFieldExtend(desc = "氛围图一")
    private String tagFirstUrl;

    /**
     * firstImage上的文案
     */
    @TableField("tag_first_content")
    @TableFieldExtend(desc = "firstImage上的文案")
    private String tagFirstContent;

    /**
     * 氛围图二
     */
    @TableField("tag_second_url")
    @TableFieldExtend(desc = "氛围图二")
    private String tagSecondUrl;

    /**
     * 独立标签0否1是
     */
    @TableField("tag_single_show")
    @TableFieldExtend(desc = "独立标签0否1是")
    private Integer tagSingleShow;

    /**
     * 渠道平台（多个，","号隔开）
     */
    @TableField("tag_channel_code")
    @TableFieldExtend(desc = "渠道平台（多个，“,”号隔开）")
    private String tagChannelCode;

    /**
     * 标签排序（倒序）
     */
    @TableField("tag_sort")
    @TableFieldExtend(desc = "标签排序（倒序）")
    private Integer tagSort;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    @TableFieldExtend(desc = "开始时间")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @TableFieldExtend(desc = "结束时间")
    private LocalDateTime endTime;

    /**
     * 有效性1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "更新时间")
    private LocalDateTime updatetime;


}
