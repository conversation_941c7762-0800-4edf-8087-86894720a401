package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 智能排序
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SmartPageSort")
@TableExtend(desc = "智能排序")
public class SmartPageSort implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "SPSId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键ID")
    private Long id;

    /**
     * 规则名称
     */
    @TableField("SPSName")
    @TableFieldExtend(desc = "规则名称")
    private String name;

    /**
     * 省份ID
     */
    @TableField("SPSProvinceId")
    @TableFieldExtend(desc = "省份ID")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @TableField("SPSProvinceName")
    @TableFieldExtend(desc = "省份名称")
    private String provinceName;

    /**
     * 城市ID
     */
    @TableField("SPSCityId")
    @TableFieldExtend(desc = "城市ID")
    private Integer cityId;

    /**
     * 城市名称
     */
    @TableField("SPSCityName")
    @TableFieldExtend(desc = "城市名称")
    private String cityName;

    /**
     * 县ID
     */
    @TableField("SPSCountyId")
    @TableFieldExtend(desc = "县ID")
    private Integer countyId;

    /**
     * 县名称
     */
    @TableField("SPSCountyName")
    @TableFieldExtend(desc = "县名称")
    private String countyName;

    /**
     * 项目属性(0国内景区 24702 吃玩景区 )
     */
    @TableField("SPSAttribute")
    @TableFieldExtend(desc = "项目属性(0国内景区 24702 吃玩景区 )")
    private Integer attribute;

    /**
     * 是否关键词  0否 1是
     */
    @TableField("SPIsKeyWord")
    @TableFieldExtend(desc = "是否关键词  0否 1是")
    private Integer sPIsKeyWord;

    /**
     * 搜索关键词
     */
    @TableField("SPSKeyWord")
    @TableFieldExtend(desc = "搜索关键词")
    private String keyWord;

    /**
     * 开始时间
     */
    @TableField("SPSStartDate")
    @TableFieldExtend(desc = "开始时间")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @TableField("SPSEndDate")
    @TableFieldExtend(desc = "结束时间")
    private LocalDateTime endDate;

    /**
     * 有效性  1：有效
     */
    @TableField("SPSIfValid")
    @TableFieldExtend(desc = "有效性  1：有效")
    private Integer ifValid;

    /**
     * 添加时间
     */
    @TableField("SPSAddTime")
    @TableFieldExtend(desc = "添加时间")
    private LocalDateTime addTime;

    /**
     * 页面板块ID:景区搜索结果页->1 首页->2 列表->3 周边
     */
    @TableField("SPSPageNameId")
    @TableFieldExtend(desc = "页面板块ID:景区搜索结果页->1 首页->2 列表->3 周边")
    private Integer pageNameId;

    /**
     * 景区顺序（id1,id2,id3）
     */
    @TableField("SPSSceneryIds")
    @TableFieldExtend(desc = "景区顺序（id1,id2,id3）")
    private String sceneryIds;

    /**
     * 渠道key
     */
    @TableField("SPSChannelKey")
    @TableFieldExtend(desc = "渠道key")
    private String channelKey;

    /**
     * 前台展示数量
     */
    @TableField("SPSShowCount")
    @TableFieldExtend(desc = "前台展示数量")
    private Integer showCount;


}
