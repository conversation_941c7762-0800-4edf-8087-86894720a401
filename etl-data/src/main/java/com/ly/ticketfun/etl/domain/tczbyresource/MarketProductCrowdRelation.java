package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 产品（货架）与人群分类关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("market_product_crowd_relation")
@TableExtend(desc = "产品（货架）与人群分类关联表")
public class MarketProductCrowdRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;

    /**
     * 不重复id(第二主键id)
     */
    @TableField("id")
    @TableFieldExtend(desc = "不重复id(第二主键id)")
    private Long id;

    /**
     * 景区id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id")
    private Long resourceId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @TableFieldExtend(desc = "产品id")
    private Long productId;

    /**
     * 人群分类（数据字典603）
     */
    @TableField("crowd_category")
    @TableFieldExtend(desc = "人群分类（数据字典603）")
    private Long crowdCategory;

    /**
     * 人群分类名称
     */
    @TableField("crowd_category_name")
    @TableFieldExtend(desc = "人群分类名称")
    private String crowdCategoryName;

    /**
     * 数据有效性（0无效 1有效）
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性（0无效 1有效）")
    private Integer rowStatus;

    /**
     * 创建人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人工号[姓名]")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新人工号[姓名]")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 人群外显名称
     */
    @TableField("crowd_outer_name")
    @TableFieldExtend(desc = "人群外显名称")
    private String crowdOuterName;


}
