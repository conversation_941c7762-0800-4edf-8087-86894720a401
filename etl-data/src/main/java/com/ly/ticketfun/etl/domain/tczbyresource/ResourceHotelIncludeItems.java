package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 景酒包含项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_hotel_include_items")
@TableExtend(desc = "景酒包含项目表")
public class ResourceHotelIncludeItems implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrGuid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 主类型：0 包含项目配置 1二次预约配置 2景酒套餐主图Url
     */
    @TableField("main_type")
    @TableFieldExtend(desc = "主类型：0 包含项目配置 1二次预约配置 2景酒套餐主图Url")
    private Integer mainType;

    /**
     * 政策长Id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策长Id")
    private Long policyId;

    /**
     * 类型：1景区 101景区维护可选项 2酒店 201酒店维护可选项 3补充说明 4酒店额外享受数据
     */
    @TableField("include_type")
    @TableFieldExtend(desc = "类型：1景区 101景区维护可选项 2酒店 201酒店维护可选项 3补充说明 4酒店额外享受数据")
    private Integer includeType;

    /**
     * 包含资源id（可以是酒店id,也可以是景区id，include_type=4存唯一值id）
     */
    @TableField("item_resource_id")
    @TableFieldExtend(desc = "包含资源id（可以是酒店id,也可以是景区id，include_type=4存唯一值id）")
    private Long itemResourceId;

    /**
     * 包含资源名称
     */
    @TableField("item_resource_name")
    @TableFieldExtend(desc = "包含资源名称")
    private String itemResourceName;

    /**
     * 省份id
     */
    @TableField("province_id")
    @TableFieldExtend(desc = "省份id")
    private Integer provinceId;

    /**
     * 省份名称
     */
    @TableField("province_name")
    @TableFieldExtend(desc = "省份名称")
    private String provinceName;

    /**
     * 城市id
     */
    @TableField("city_id")
    @TableFieldExtend(desc = "城市id")
    private Integer cityId;

    /**
     * 城市名称
     */
    @TableField("city_name")
    @TableFieldExtend(desc = "城市名称")
    private String cityName;

    /**
     * 票型项目（房型）
     */
    @TableField("item_name")
    @TableFieldExtend(desc = "票型项目（房型）")
    private String itemName;

    /**
     * 景区-数量(酒店-餐食份数)
     */
    @TableField("item_num")
    @TableFieldExtend(desc = "景区-数量(酒店-餐食份数)")
    private Integer itemNum;

    /**
     * 景区-单位(酒店-餐食名称)
     */
    @TableField("item_extend")
    @TableFieldExtend(desc = "景区-单位(酒店-餐食名称)")
    private String itemExtend;

    /**
     * 全部或几选几 0全部 1选择数量
     */
    @TableField("select_num")
    @TableFieldExtend(desc = "全部或几选几 0全部 1选择数量")
    private Integer selectNum;

    /**
     * 选择项 (201酒店维护可选项:0-用户可选 1-商家指定)
     */
    @TableField("select_option")
    @TableFieldExtend(desc = "选择项 (201酒店维护可选项:0-用户可选 1-商家指定)")
    private Integer selectOption;

    /**
     * 间夜-间
     */
    @TableField("between_num")
    @TableFieldExtend(desc = "间夜-间")
    private Integer betweenNum;

    /**
     * 间夜-夜
     */
    @TableField("night_num")
    @TableFieldExtend(desc = "间夜-夜")
    private Integer nightNum;

    /**
     * 是否需要二次预约 0不需要 1在线预约 2电话预约 3短信链接预约
     */
    @TableField("need_times_book")
    @TableFieldExtend(desc = "是否需要二次预约 0不需要 1在线预约 2电话预约 3短信链接预约")
    private Integer needTimesBook;

    /**
     * 酒店地址
     */
    @TableField("hotel_address")
    @TableFieldExtend(desc = "酒店地址")
    private String hotelAddress;

    /**
     * 排序
     */
    @TableField("sort_num")
    @TableFieldExtend(desc = "排序")
    private Integer sortNum;

    /**
     * main_type 0补充说明 1预约电话号码 2景酒套餐主图Url
     */
    @TableField("more_remarks")
    @TableFieldExtend(desc = "main_type 0补充说明 1预约电话号码 2景酒套餐主图Url")
    private String moreRemarks;

    /**
     * 有效性 1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效 0无效")
    private Integer rowStatus;

    /**
     * 新增人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;

    /**
     * 改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
