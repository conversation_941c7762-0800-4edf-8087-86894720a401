package com.ly.ticketfun.etl.mapper.tczbyresourcebase;

import com.ly.ticketfun.etl.domain.po.ResourcePromotionBaseInfoPo;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.ResourcePromotionRule;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 促销基本设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ResourcePromotionRuleMapper extends LaBaseMapper<ResourcePromotionRule> {


    /**
     * 查询所有促销基础信息的有效数据
     * @param beginDate
     * @param endDate
     * @return
     */
    List<ResourcePromotionBaseInfoPo> queryAllValidList(@Param("beginDate")Date beginDate, @Param("endDate") Date endDate);

}
