package com.ly.ticketfun.etl.dataService.templateresource;

import com.ly.localactivity.framework.service.IEsBaseService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;

import java.util.List;

/**
 * 门票玩乐模版SKU资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ITicketFunTemplateResourceSkuService extends IEsBaseService<TicketFunTemplateResourceSkuInfo> {
    /**
     * 按产品id查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    List<TicketFunTemplateResourceSkuInfo> queryByProductId(String productId);

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    List<TicketFunTemplateResourceSkuInfo> queryByProductId(List<String> productIdList);

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    List<TicketFunTemplateResourceSkuInfo> queryByPackageId(String packageId);

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    List<TicketFunTemplateResourceSkuInfo> queryByPackageId(List<String> packageIdList);

    default List<TicketFunTemplateResourceSkuInfo> query(String productId, String packageId, List<String> packageIds, String skuId, List<String> skuIds) {
        return null;
    };
}
