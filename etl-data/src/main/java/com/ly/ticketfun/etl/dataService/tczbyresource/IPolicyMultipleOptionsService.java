package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyMultipleOptions;

import java.util.List;

/**
 * <p>
 * 政策多选项关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface IPolicyMultipleOptionsService extends IDbBaseService<PolicyMultipleOptions> {

    /**
     * 获取多选项列表
     *
     * @param resourceId     资源id
     * @param policyId       政策id
     * @param selectTypeList 选项类型 1游客信息证件类型 2游客信息基础信息 3电子凭证 4支持证件 5短信信息8入园凭证 9购票人信息  10销售场景
     * @return 多选项列表
     */
    List<PolicyMultipleOptions> queryListByPolicyId(Long resourceId, Long policyId, List<Integer> selectTypeList);
}
