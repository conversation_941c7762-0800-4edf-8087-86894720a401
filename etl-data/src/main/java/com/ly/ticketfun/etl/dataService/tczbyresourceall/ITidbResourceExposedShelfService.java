package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceExposedShelf;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 搜索使用货架辅助外显状态（1对多） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceExposedShelfService extends IDbBaseService<ResourceExposedShelf> {

    List<ResourceExposedShelf> queryResourceExposedShelfListByResourceId(Long resourceId);
}
