package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note
 */
@Data
public class SearchLabelInfoDto implements Serializable {
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 标签大类id
     */
    private Long tagParentId;

    /**
     * 父标签Code
     */
    private String tagParentCode;

    /**
     * 标签大类名称
     */
    private String tagParentName;

    /**
     * 父标签排序值（倒序）
     */
    private Integer tagParentSort;

    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 标签code
     */
    private String tagCode;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签自定义内容
     */
    private String tagContent;

    /**
     * 标签排序值（倒序）
     */
    private Integer tagSort;

    /**
     * 标签类型
     */
    private Integer tagType;

    /**
     * 氛围图一
     */
    private String tagFirstUrl;

    /**
     * 氛围图二
     */
    private String taglSecondUrl;

    /**
     * 独立标签标识
     * 0-非独立标签 1-独立标签
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer tagSingleShow;

    /**
     * 渠道平台（多个，","号隔开）
     */
    private String tagChannelCode;

}
