package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 目的地-游玩
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePlay")
@TableExtend(desc = "目的地-游玩")
public class ResourcePlay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "RPId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 项目id
     */
    @TableField("RPProjectId")
    @TableFieldExtend(desc = "项目id")
    private Long projectId;

    /**
     * 游玩项目类型 0园内项目 1园内景点 2园内线路 3亮点介绍
     */
    @TableField("RPPlayType")
    @TableFieldExtend(desc = "游玩项目类型 0园内项目 1园内景点 2园内线路 3亮点介绍")
    private Integer playType;

    /**
     * 游玩项目名称
     */
    @TableField("RPPlayName")
    @TableFieldExtend(desc = "游玩项目名称")
    private String playName;

    /**
     * 视频地址
     */
    @TableField("RPVideoUrl")
    @TableFieldExtend(desc = "视频地址")
    private String videoUrl;

    /**
     * 位置描述
     */
    @TableField("RPPlayAddressDescribe")
    @TableFieldExtend(desc = "位置描述")
    private String playAddressDescribe;

    /**
     * 图片
     */
    @TableField("RPPlayPics")
    @TableFieldExtend(desc = "图片")
    private String playPics;

    /**
     * 标签id
     */
    @TableField("RPPlayTagId")
    @TableFieldExtend(desc = "标签id")
    private String playTagId;

    /**
     * 标签名称
     */
    @TableField("RPPlayTagName")
    @TableFieldExtend(desc = "标签名称")
    private String playTagName;

    /**
     * 排序
     */
    @TableField("RPSort")
    @TableFieldExtend(desc = "排序")
    private Integer sort;

    /**
     * 备注
     */
    @TableField("RPRemarks")
    @TableFieldExtend(desc = "备注")
    private String remarks;

    /**
     * 行状态 
     */
    @TableField("RPRowStatus")
    @TableFieldExtend(desc = "行状态 ")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "RPCreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("RPCreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "RPUpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("RPUpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;

    /**
     * 是否人工手动更新（0否，1是，默认1）
     */
    @TableField("RPIsManualUpdate")
    @TableFieldExtend(desc = "是否人工手动更新（0否，1是，默认1）")
    private Integer isManualUpdate;

    /**
     * 是否前台展示：0否，1是
     */
    @TableField("RPIsFrontShow")
    @TableFieldExtend(desc = "是否前台展示：0否，1是")
    private Integer isFrontShow;

    /**
     * 关联项 0-无 1-景区 2-政策
     */
    @TableField("RPRelatedItem")
    @TableFieldExtend(desc = "关联项 0-无 1-景区 2-政策")
    private Integer relatedItem;

    /**
     * 关联项值（景区ID）
     */
    @TableField("RPRelatedItemValue")
    @TableFieldExtend(desc = "关联项值（景区ID）")
    private Long relatedItemValue;

    /**
     * 关联项值（政策短ID）
     */
    @TableField("RPRelatedPolicyId")
    @TableFieldExtend(desc = "关联项值（政策短ID）")
    private Long relatedPolicyId;

    /**
     * 视频封面图片
     */
    @TableField("RPVideoImage")
    @TableFieldExtend(desc = "视频封面图片")
    private String videoImage;


}
