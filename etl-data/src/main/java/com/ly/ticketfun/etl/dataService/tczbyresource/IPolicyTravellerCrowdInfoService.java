package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdInfo;

import java.util.List;

/**
 * <p>
 * 政策针对人群表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public interface IPolicyTravellerCrowdInfoService extends IDbBaseService<PolicyTravellerCrowdInfo> {

    /**
     * 获取游玩人人群列表
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 游玩人人群列表
     */
    List<PolicyTravellerCrowdInfo> queryListByPolicyId(Long resourceId, Long policyId);
}
