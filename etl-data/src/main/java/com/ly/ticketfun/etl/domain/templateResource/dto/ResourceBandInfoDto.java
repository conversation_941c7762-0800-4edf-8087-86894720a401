package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.base.GenderEnum;
import com.ly.ticketfun.etl.common.enums.base.ZodiacEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBandInfoEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ResourceBookPassengerInfoEnum;
import com.ly.ticketfun.etl.common.enums.ticket.RegionDimensionEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 资源分类（人群）
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class ResourceBandInfoDto implements Serializable {
    private static final long serialVersionUID = 3777467628639209197L;
    /**
     * 分类（人群）id
     */
    private Long bandId;
    /**
     * 分类（人群id）code
     */
    @EnumField(enumClazz = ResourceBandInfoEnum.BandCode.class)
    private String bandCode;
    /**
     * 分类（人群id）标题
     */
    private String bandTitle;
    /**
     * 人数
     */
    private Integer personQuantity;
    /**
     * 适用人群
     */
    @EnumField(enumClazz = ResourceBookPassengerInfoEnum.PassengerRequiredType.class)
    private String passengerRequiredType;
    /**
     * 限制条件列表
     */
    private List<BandLimitRuleDto> bandLimitRuleList;

    /**
     * 分类（人群）限制规则
     *
     * <AUTHOR>
     * @date 2025/09/03
     */
    @Data
    public static class BandLimitRuleDto implements Serializable {
        private static final long serialVersionUID = 3311529580187550580L;
        /**
         * 年龄配置列表
         */
        private List<AgeLimitRuleDto> ageLimitRuleList = new ArrayList<>();
        /**
         * 身高配置列表
         */
        private List<HeightLimitRuleDto> heightLimitRuleList = new ArrayList<>();
        /**
         * 生日配置列表
         */
        private List<BirthdayLimitRuleDto> birthdayLimitRuleList = new ArrayList<>();
        /**
         * 性别配置列表
         */
        private List<GenderLimitRuleDto> genderLimitRuleList = new ArrayList<>();
        /**
         * 区域配置列表
         */
        private List<AreaLimitRuleDto> areaLimitRuleList = new ArrayList<>();
        /**
         * 生肖配置列表
         */
        private List<ZodiacLimitRuleDto> zodiacLimitRuleList = new ArrayList<>();
        /**
         * 姓名配置列表
         */
        private List<NameLimitRuleDto> nameLimitRuleList = new ArrayList<>();
        /**
         * 身份认证限制规则列表
         */
        private List<IdentityAuthLimitRuleDto> identityAuthLimitRuleDtoList = new ArrayList<>();
    }


    /**
     * 高度限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/15
     */
    @Data
    public static class HeightLimitRuleDto {
        /**
         * 是否可定
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 开始身高 单位cm
         */
        private Integer beginHeight;

        /**
         * 是否包含开始身高
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer beginInclude;

        /**
         * 结束身高 单位cm
         */
        private Integer endHeight;

        /**
         * 是否包含结束身高
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer endInclude;

    }

    /**
     * 年龄限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class AgeLimitRuleDto {
        /**
         * 是否可定
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 生日计算维度
         */
        @EnumField(enumClazz = ResourceBandInfoEnum.BirthdayDimension.class)
        private String ageCalcBirthdayDimension;

        /**
         * 生日计算比较维度
         */
        @EnumField(enumClazz = ResourceBandInfoEnum.AgeCalcCompareDimension.class)
        private String ageCalcCompareDimension;

        /**
         * 年龄范围
         */
        private List<AgeRangeDto> ageRangeList = new ArrayList<>();
    }

    /**
     * 年龄范围dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AgeRangeDto {
        /**
         * 开始年龄
         */
        private Integer beginAge;

        /**
         * 是否包含开始年龄
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer beginInclude;

        /**
         * 结束年龄
         */
        private Integer endAge;

        /**
         * 是否包含结束年龄
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer endInclude;

    }

    /**
     * 生日限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class BirthdayLimitRuleDto {
        /**
         * 是否可定
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 日维度
         */
        @EnumField(enumClazz = ResourceBandInfoEnum.BirthdayDimension.class)
        private String dayDimension;

        /**
         * 值列表
         */
        private List<String> valueList;
    }

    /**
     * 性别限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GenderLimitRuleDto {
        /**
         * 是否可定
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 性别
         */
        @EnumField(enumClazz = GenderEnum.class)
        private String gender;
    }

    /**
     * 区域限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class AreaLimitRuleDto {
        /**
         * 是否可定
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 身份证前缀列表
         */
        private List<IdCardPrefixDto> idCardPrefixList = new ArrayList<>();

        /**
         * 日维度
         */
        @EnumField(enumClazz = RegionDimensionEnums.class)
        private String AreaDimension;

        /**
         * 值列表
         */
        private List<String> valueList;
    }

    /**
     * 身份证前缀dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class IdCardPrefixDto {
        /**
         * 标签
         */
        private String label;

        /**
         * 身份证前缀
         */
        private String idPrefix;
    }

    /**
     * 生肖限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class ZodiacLimitRuleDto {
        /**
         * 可以预订
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 生肖类型
         */
        @EnumField(enumClazz = ZodiacEnum.class)
        private String zodiacType;

        /**
         * 开始年份
         */
        private String beginYear;

        /**
         * 生肖类型集合
         */
        @EnumField(enumClazz = ZodiacEnum.class)
        private List<String> zodiacTypeList;

    }

    /**
     * 名称限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class NameLimitRuleDto {
        /**
         * 可以预订
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 值列表
         */
        private List<String> valueList;
    }

    /**
     * 身份认证限制规则dto
     *
     * <AUTHOR>
     * @date 2025/09/05
     */
    @Data
    public static class IdentityAuthLimitRuleDto {
        /**
         * 可以预订
         */
        @EnumField(enumClazz = WhetherEnum.class)
        private Integer canBook;

        /**
         * 身份类型
         */
        @EnumField(enumClazz = ResourceBandInfoEnum.IdentityType.class)
        private String identityType;

    }

}
