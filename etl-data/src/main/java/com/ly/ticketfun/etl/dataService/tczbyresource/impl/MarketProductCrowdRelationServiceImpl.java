package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.MarketProductCrowdRelation;
import com.ly.ticketfun.etl.mapper.tczbyresource.MarketProductCrowdRelationMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IMarketProductCrowdRelationService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品（货架）与人群分类关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Service
public class MarketProductCrowdRelationServiceImpl extends DbBaseServiceImpl<MarketProductCrowdRelationMapper, MarketProductCrowdRelation> implements IMarketProductCrowdRelationService {

}
