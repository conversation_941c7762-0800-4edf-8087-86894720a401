package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 资源分类
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceClassifyDto implements Serializable {
    private static final long serialVersionUID = -5193578973590967263L;
    /**
     * 分类Id
     */
    private String classifyId;
    /**
     * 分类名称
     */
    private String classifyName;
    /**
     * 分类标题
     */
    private String classifyTitle;
    /**
     * 分类说明
     */
    private String classifyContent;
    /**
     * 分类小贴士
     */
    private ResourceClassifyTipDto classifyTip;

    /**
     * 资源分类贴士
     */
    @Data
    public class ResourceClassifyTipDto {
        /**
         * 标题
         */
        private String title;
        /**
         * 文本内容
         */
        private String content;
        /**
         * 图片内容
         */
        private List<FileSimpleDto> imageList;
    }
}
