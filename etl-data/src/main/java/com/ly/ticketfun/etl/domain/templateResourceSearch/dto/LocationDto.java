package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 国家地区
 */
@Data
public class LocationDto implements Serializable {
    /**
     * 位置类型 (0:出发地,1:目的地)
     */
    @EnumField(enumClazz = ResourceSearchEnum.PlaceTypeEnum.class)
    private String placeType;
    /**
     * 关联资源ID
     */
    private Long resourceId;
    /**
     * 洲ID
     */
    private Long continetId;
    /**
     * 洲名称
     */
    private String continetName;
    /**
     * 国家ID
     */
    private Long countryId;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 省份ID
     */
    private Long provinceId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市ID
     */
    private Long cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县ID
     */
    private Long sectionId;
    /**
     * 区县名称
     */
    private String sectionName;
}
