package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.ticket.EnterTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class UseRuleDto {
    /**
     * 入园方式
     */
    @EnumField(enumClazz = EnterTypeEnum.class)
    private Integer enterType;
    /**
     * 地址信息
     */
    private List<AddressTimeDto> enterAddressList;
    private List<AddressTimeDto> changeAddressList;
    /**
     * 入园换票地址时间是否相同
     */
    private Integer ifSameAddress = 0;
    /**
     * 是否需要所有凭证0否1是
     */
    private Integer voucherAll;
    /**
     * 核销方式补充说明
     */
    private String supplementDesc;
}
