package com.ly.ticketfun.etl.mapper.tczbyresourcebase;

import com.ly.ticketfun.etl.domain.tczbyresourcebase.ResourcePlay;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import com.ly.ticketfun.etl.domain.templateResourceSearch.dto.HighLightInfoDto;

import java.util.List;

/**
 * <p>
 * 目的地-游玩 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ResourcePlayMapper extends LaBaseMapper<ResourcePlay> {

    List<HighLightInfoDto> queryHighLightInfoByResourceId(Long resourceId);
}
