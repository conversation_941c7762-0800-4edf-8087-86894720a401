package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerBase;

/**
 * <p>
 * 出游人基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public interface IPolicyTravellerBaseService extends IDbBaseService<PolicyTravellerBase> {

    /**
     * 获取政策出游人基础信息
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 政策出游人基础信息
     */
    PolicyTravellerBase queryByPolicyId(Long resourceId, Long policyId);
}
