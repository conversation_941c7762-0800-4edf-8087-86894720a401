package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 货架推荐清洗数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("shelf_recommend_wash")
@TableExtend(desc = "货架推荐清洗数据表")
public class ShelfRecommendWash implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * uuid主键
     */
    @TableId("id")
    @TableFieldExtend(desc = "uuid主键")
    private String id;

    /**
     * 景区id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id")
    private Long resourceId;

    /**
     * 景区名称
     */
    @TableField("resource_name")
    @TableFieldExtend(desc = "景区名称")
    private String resourceName;

    /**
     * 货架id
     */
    @TableField("shelf_id")
    @TableFieldExtend(desc = "货架id")
    private Long shelfId;

    /**
     * 货架名称
     */
    @TableField("shelf_name")
    @TableFieldExtend(desc = "货架名称")
    private String shelfName;

    /**
     * 人群分类id
     */
    @TableField("crowd_category")
    @TableFieldExtend(desc = "人群分类id")
    private Integer crowdCategory;

    /**
     * 单量
     */
    @TableField("order_num")
    @TableFieldExtend(desc = "单量")
    private Long orderNum;

    /**
     * 推荐货架id
     */
    @TableField("sub_shelf_id")
    @TableFieldExtend(desc = "推荐货架id")
    private Long subShelfId;

    /**
     * 推荐货架名称
     */
    @TableField("sub_shelf_name")
    @TableFieldExtend(desc = "推荐货架名称")
    private String subShelfName;

    /**
     * 推荐人群分类
     */
    @TableField("sub_crowd_category")
    @TableFieldExtend(desc = "推荐人群分类")
    private Integer subCrowdCategory;

    /**
     * 推荐货架单量
     */
    @TableField("sub_order_num")
    @TableFieldExtend(desc = "推荐货架单量")
    private Long subOrderNum;

    /**
     * 会员数量
     */
    @TableField("member_count")
    @TableFieldExtend(desc = "会员数量")
    private Long memberCount;

    /**
     * 有效性
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 更新时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "更新时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;


}
