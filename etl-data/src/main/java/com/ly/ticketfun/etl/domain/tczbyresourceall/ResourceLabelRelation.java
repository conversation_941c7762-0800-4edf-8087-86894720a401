package com.ly.ticketfun.etl.domain.tczbyresourceall;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;


/**
 * <p>
 * 资源标签关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourceLabelRelation")
@TableExtend(desc = "资源标签关系表")
public class ResourceLabelRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合用")
    private Long dbrguid;

    /**
     * 主键18位
     */
    @TableField("RLRId")
    @TableFieldExtend(desc = "主键18位")
    private Long id;

    /**
     * 资源ID，分片键
     */
    @TableField("RLRResourceId")
    @TableFieldExtend(desc = "资源ID，分片键")
    private Long resourceId;

    /**
     * 标签ID
     */
    @TableField("RLRLabelId")
    @TableFieldExtend(desc = "标签ID")
    private Long labelId;

    /**
     * 标签名称
     */
    @TableField("RLRLabelIdName")
    @TableFieldExtend(desc = "标签名称")
    private String labelIdName;

    /**
     * 标签类型ID
     */
    @TableField("RLRLabelTypeId")
    @TableFieldExtend(desc = "标签类型ID")
    private Long labelTypeId;

    /**
     * 标签类型名称
     */
    @TableField("RLRLabelTypeName")
    @TableFieldExtend(desc = "标签类型名称")
    private String labelTypeName;

    /**
     * 有效期开始时间
     */
    @TableField("RLRBeginDate")
    @TableFieldExtend(desc = "有效期开始时间")
    private LocalDateTime beginDate;

    /**
     * 有效期结束时间
     */
    @TableField("RLREndDate")
    @TableFieldExtend(desc = "有效期结束时间")
    private LocalDateTime endDate;

    /**
     * 使用月份
     */
    @TableField("RLRUseMonth")
    @TableFieldExtend(desc = "使用月份")
    private String useMonth;

    /**
     * 标签图片URL
     */
    @TableField("RLRLabelUrl")
    @TableFieldExtend(desc = "标签图片URL")
    private String labelUrl;

    /**
     * 备注
     */
    @TableField("RLRRemark")
    @TableFieldExtend(desc = "备注")
    private String remark;

    /**
     * 有效性
     */
    @TableField("RLRRowStatus")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 新增时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "新增时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 标签来源,0人工,1标签清洗job
     */
    @TableField("RLRLabelSource")
    @TableFieldExtend(desc = "标签来源,0人工,1标签清洗job")
    private Integer labelSource;


}
