package com.ly.ticketfun.etl.dataService.templateresource;


import com.ly.localactivity.framework.service.IEsBaseService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;

import java.util.List;

/**
 * 门票玩乐模版套餐资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ITicketFunTemplateResourcePackageService extends IEsBaseService<TicketFunTemplateResourcePackageInfo> {
    /**
     * 按产品id查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    default List<TicketFunTemplateResourcePackageInfo> queryByProductId(String productId, String indexSuffix) {
        return null;
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    default List<TicketFunTemplateResourcePackageInfo> queryByProductId(List<String> productIdList, String indexSuffix) {
        return null;
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    default TicketFunTemplateResourcePackageInfo queryByPackageId(String packageId, String indexSuffix) {
        return null;
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    default List<TicketFunTemplateResourcePackageInfo> queryByPackageId(List<String> packageIdList, String indexSuffix) {
        return null;
    }

    /**
     * 查询
     *
     * @param productId 产品id
     * @param indexSuffix
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    default List<TicketFunTemplateResourcePackageInfo> query(String productId, String packageId, List<String> packageIdList, String indexSuffix) {
        return null;
    }
}
