package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.common.enums.base.SaleSiteEnum;
import com.ly.ticketfun.etl.common.enums.base.SalePointCodeEnum;
import com.ly.ticketfun.etl.common.enums.base.PlatformEnum;
import com.ly.ticketfun.etl.common.enums.base.SaleSceneEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售渠道
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class SaleChannelDto implements Serializable {
    private static final long serialVersionUID = 8207497206057459009L;
    /**
     * 销售终端Code
     */
    @EnumField(enumClazz = SalePointCodeEnum.class)
    private String salePointCode;
    /**
     * 销售终端名称
     */
    private String salePointName;

    /**
     * 销售站点
     */
    @EnumField(enumClazz = SaleSiteEnum.class)
    private String saleSite;

    /**
     * 销售PlatId
     */
    @EnumField(enumClazz = PlatformEnum.class)
    private String salePlatId;
    /**
     * 销售Plat名称
     */
    private String salePlatName;
    /**
     * 销售场景值
     */
    @EnumField(enumClazz = SaleSceneEnum.class)
    private String saleScene;

    private List<String> saleRefIds;

    /**
     * 景区id
     */
    private String poiId;

    /**
     * 产品id
     */
    private String productId;
    /**
     * 套餐id
     */
    private String packageId;

    /**
     * 品类
     */
    private ResourceCategoryDto category;
    /**
     * 人民币卖价
     */
    private BigDecimal salePrice_CNY;
    /**
     * 人民币结算价
     */
    private BigDecimal netPrice_CNY;
    /**
     * 人民币门市价
     */
    private BigDecimal marketPrice_CNY;

    /**
     * 是否有售卖时间限制
     */
    @EnumField(enumClazz = WhetherEnum.class)
    private Integer saleTimeLimit;
    /**
     * 售卖开始时间
     */
    private Long saleBeginTime;
    /**
     * 售卖结束时间
     */
    private Long saleEndTime;

}
