package com.ly.ticketfun.etl.domain.pojo;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.base.PlatformEnum;
import com.ly.ticketfun.etl.common.enums.base.SaleSiteEnum;
import com.ly.ticketfun.etl.common.enums.base.SalePointCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleChannelRelation {

    /**
     * 销售终端code
     */
    @EnumField(enumClazz = SalePointCodeEnum.class)
    private String salePointCode;

    /**
     * 区域列表
     */
    private List<SaleLocalInfo> saleLocalList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleLocalInfo {

        /**
         * 销售区域code列表
         */
        @EnumField(enumClazz = SaleSiteEnum.class)
        private List<String> saleLocalCodeList;

        /**
         * 平台列表
         */
        private List<SalePlatformInfo> salePlatformList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SalePlatformInfo {

        /**
         * 销售平台code列表
         */
        @EnumField(enumClazz = PlatformEnum.class)
        private List<String> salePlatformCodeList;

        /**
         * 政策渠道id列表
         */
        private List<Integer> policyChannelIdList;

        /**
         * 场景值列表
         */
        private List<String> saleSceneCodeList;
    }
}
