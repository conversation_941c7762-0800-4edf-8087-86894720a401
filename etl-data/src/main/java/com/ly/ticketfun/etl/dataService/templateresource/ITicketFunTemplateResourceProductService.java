package com.ly.ticketfun.etl.dataService.templateresource;


import com.ly.localactivity.framework.service.IEsBaseService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;

import java.util.List;

/**
 * 门票玩乐模版产品资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
public interface ITicketFunTemplateResourceProductService extends IEsBaseService<TicketFunTemplateResourceProductInfo> {
    /**
     * 查询
     *
     * @param productId   产品id
     * @param indexSuffix
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    default TicketFunTemplateResourceProductInfo queryByProductId(String productId, String indexSuffix) {
        return null;
    }

    /**
     * 查询列表
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    default List<TicketFunTemplateResourceProductInfo> queryByProductId(List<String> productIdList, String indexSuffix) {
        return null;
    }
}
