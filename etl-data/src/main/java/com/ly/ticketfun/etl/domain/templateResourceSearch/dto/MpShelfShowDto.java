package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-9-13
 * @note
 */
@Data
public class MpShelfShowDto implements Serializable {

    /**
     * 外显可有的货架信息
     */
    @EnumField(enumClazz = ResourceSearchEnum.ShowShelfTypeEnum.class)
    private String showShelfType;

    /**
     * 货架名称
     */
    private String showShelfName;

    /**
     * 外显个数
     * >0即代表有数据
     */
    private Integer showCount;

}
