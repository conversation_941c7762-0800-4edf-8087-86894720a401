package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.localactivity.framework.annotation.EsIndex;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.ProductResourceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版产品资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@EsIndex(value = "ticketfun-product", token = "eec7a0ab-f087-494b-a7be-f320d12ffec3")
public class TicketFunTemplateResourceProductInfo extends ProductResourceDto {
    private static final long serialVersionUID = -8218253733144317432L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否有效
     */
    @EnumField(enumClazz = DataFlagEnum.class)
    @DataFlagField
    private Integer dataFlag;
    /**
     * 语言
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private String locale;
    /**
     * 创建时间
     */
    @CreateTimeField
    private Long createTime;
    /**
     * 更新时间
     */
    @ModifyTimeField
    private Long updateTime;

}
