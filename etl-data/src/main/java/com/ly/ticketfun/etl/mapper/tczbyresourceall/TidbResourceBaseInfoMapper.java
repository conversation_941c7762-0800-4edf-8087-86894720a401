package com.ly.ticketfun.etl.mapper.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import com.ly.ticketfun.etl.domain.tczbyresourceall.dto.BasicResourceInfoDto;


/**
 * <p>
 * 资源表，分片键：资源ID Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface TidbResourceBaseInfoMapper extends LaBaseMapper<ResourceBaseInfo> {

    BasicResourceInfoDto getBasicResourceInfo(Long resourceId);
}
