package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.MarketProductConditionConfig;
import com.ly.ticketfun.etl.mapper.tczbyresource.MarketProductConditionConfigMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IMarketProductConditionConfigService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 产品（货架）适用条件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class MarketProductConditionConfigServiceImpl extends DbBaseServiceImpl<MarketProductConditionConfigMapper, MarketProductConditionConfig> implements IMarketProductConditionConfigService {

}
