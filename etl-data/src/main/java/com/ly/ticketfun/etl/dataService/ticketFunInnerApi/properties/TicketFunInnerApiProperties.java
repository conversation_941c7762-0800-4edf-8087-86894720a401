package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 门票玩乐内部api属性
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
@Component
@ConfigurationProperties(prefix = "api.ticketfun.innerapi")
@Data
public class TicketFunInnerApiProperties {
    /**
     * 玩乐主资源
     */
    private String queryFunMainResourceAggListUrl;

    /**
     * 玩乐子资源
     */
    private String queryFunSubResourceAggListUrl;

    /**
     * 玩乐sku资源
     */
    private String queryFunSkuResourceAggListUrl;


    private String globalRegionUrl;

    /**
     * 汇率列表请求地址
     */
    private String queryExchangeRateListUrl;
}
