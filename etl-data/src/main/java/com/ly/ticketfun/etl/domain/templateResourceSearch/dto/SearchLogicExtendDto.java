package com.ly.ticketfun.etl.domain.templateResourceSearch.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateResource.LogicExtendEnum;
import com.ly.ticketfun.etl.common.enums.templateResourceSearch.ResourceSearchEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-9-9
 * @note 逻辑数据（数字型）
 */
@Data
public class SearchLogicExtendDto {

    private static final long serialVersionUID = 1L;
    /**
     * key
     */
    @EnumField(enumClazz = LogicExtendEnum.ResourceSearchKeyEnum.class)
    private String key;
    /**
     * 值1
     */
    private String value1;

    public SearchLogicExtendDto(String key, Object value1) {
        this.key = key;
        this.value1 = String.valueOf(value1);
    }

}
