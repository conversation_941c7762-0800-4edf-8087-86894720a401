package com.ly.ticketfun.etl.domain.tczbyresourceall;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 景区搜索所需补充信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_supplementary_Info")
@TableExtend(desc = "景区搜索所需补充信息表")
public class ResourceSupplementaryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId("DbrGuid")
    @TableFieldExtend(desc = "主键id")
    private Long dbrguid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 点评分
     */
    @TableField("comments_score")
    @TableFieldExtend(desc = "点评分")
    private String commentsScore;

    /**
     * 点评总数
     */
    @TableField("comments_count")
    @TableFieldExtend(desc = "点评总数")
    private String commentsCount;

    /**
     * 好评数
     */
    @TableField("good_comments_count")
    @TableFieldExtend(desc = "好评数")
    private String goodCommentsCount;

    /**
     * 中评数
     */
    @TableField("mid_comments_count")
    @TableFieldExtend(desc = "中评数")
    private String midCommentsCount;

    /**
     * 差评数
     */
    @TableField("bad_comments_count")
    @TableFieldExtend(desc = "差评数")
    private String badCommentsCount;

    /**
     * 点评AI一句话
     */
    @TableField("comment_light_content")
    @TableFieldExtend(desc = "点评AI一句话")
    private String commentLightContent;

    /**
     * 好评率
     */
    @TableField("good_comments_rate")
    @TableFieldExtend(desc = "好评率")
    private BigDecimal goodCommentsRate;

    /**
     * 最近30天差评率
     */
    @TableField("latest_bad_comments_rate")
    @TableFieldExtend(desc = "最近30天差评率")
    private BigDecimal latestBadCommentsRate;

    /**
     * 测试状态（1：测试，0：否）
     */
    @TableField("test_status")
    @TableFieldExtend(desc = "测试状态（1：测试，0：否）")
    private Integer testStatus;

    /**
     * 展示状态（1：对客，0：屏蔽对客）
     */
    @TableField("show_status")
    @TableFieldExtend(desc = "展示状态（1：对客，0：屏蔽对客）")
    private Integer showStatus;

    /**
     * 4个月日期是否可销售（20250601:1235464733[最晚可订时间戳]），| 分隔
     */
    @TableField("dates_can_sales")
    @TableFieldExtend(desc = "4个月日期是否可销售（20250601:1235464733[最晚可订时间戳]），| 分隔")
    private String datesCanSales;

    /**
     * 7天每天开闭园时间(需要时间戳判断显示，和售卖日期差不多格式)
     */
    @TableField("days_open_time")
    @TableFieldExtend(desc = "7天每天开闭园时间(需要时间戳判断显示，和售卖日期差不多格式)")
    private String daysOpenTime;

    /**
     * 30 天内订单正负佣金（1：正，0：平，-1：负）
     */
    @TableField("commission_status")
    @TableFieldExtend(desc = "30 天内订单正负佣金（1：正，0：平，-1：负）")
    private Integer commissionStatus;

    /**
     * 出价状态（1已出 0无）
     */
    @TableField("price_status")
    @TableFieldExtend(desc = "出价状态（1已出 0无）")
    private Integer priceStatus;

    /**
     * 是否有价格优势（M B L）
     */
    @TableField("price_mbl_status")
    @TableFieldExtend(desc = "是否有价格优势（M B L）")
    private String priceMblStatus;

    /**
     * 退改状态（0：不可退，1：随时退，
2：无损条件退，3：有损条件退）
     */
    @TableField("refundType")
    @TableFieldExtend(desc = "退改状态（0：不可退，1：随时退，2：无损条件退，3：有损条件退）")
    private Integer refundtype;

    /**
     * 条件退时间（门票逻辑还要看下）
     */
    @TableField("refund_time")
    @TableFieldExtend(desc = "条件退时间（门票逻辑还要看下）")
    private String refundTime;

    /**
     * 是否随买随用（1：是，0 否）
     */
    @TableField("use_status")
    @TableFieldExtend(desc = "是否随买随用（1：是，0 否）")
    private Integer useStatus;

    /**
     * 信息完善度
     */
    @TableField("maintenance_score")
    @TableFieldExtend(desc = "信息完善度")
    private Integer maintenanceScore;

    /**
     * 多少个用户浏览过
     */
    @TableField("pv_half_year")
    @TableFieldExtend(desc = "多少个用户浏览过")
    private Long pvHalfYear;

    /**
     * 多少个用户购买过
     */
    @TableField("uv_order_one_year")
    @TableFieldExtend(desc = "多少个用户购买过")
    private Long uvOrderOneYear;

    /**
     * 点击7天UV
     */
    @TableField("user_click_7")
    @TableFieldExtend(desc = "点击7天UV")
    private Long userClick7;

    /**
     * 曝光7天UV
     */
    @TableField("user_exposure_7")
    @TableFieldExtend(desc = "曝光7天UV")
    private Long userExposure7;

    /**
     * 点击去年同期7天UV
     */
    @TableField("user_click_yoy_7")
    @TableFieldExtend(desc = "点击去年同期7天UV")
    private Long userClickYoy7;

    /**
     * 曝光去年同期7天UV
     */
    @TableField("user_exposure_yoy_7")
    @TableFieldExtend(desc = "曝光去年同期7天UV")
    private Long userExposureYoy7;

    /**
     * 点击30天UV
     */
    @TableField("user_click_30")
    @TableFieldExtend(desc = "点击30天UV")
    private Long userClick30;

    /**
     * 曝光30天UV
     */
    @TableField("user_exposure_30")
    @TableFieldExtend(desc = "曝光30天UV")
    private Long userExposure30;

    /**
     * 30天CVR
     */
    @TableField("conversion_num_30")
    @TableFieldExtend(desc = "30天CVR")
    private BigDecimal conversionNum30;

    /**
     * 去年同期30天CVR
     */
    @TableField("conversion_num_yoy_30")
    @TableFieldExtend(desc = "去年同期30天CVR")
    private BigDecimal conversionNumYoy30;

    /**
     * 自营平台7天单量
     */
    @TableField("order_official_7")
    @TableFieldExtend(desc = "自营平台7天单量")
    private Long orderOfficial7;

    /**
     * 自营平台180天单量
     */
    @TableField("order_official_180")
    @TableFieldExtend(desc = "自营平台180天单量")
    private Long orderOfficial180;

    /**
     * 自营平台365天单量
     */
    @TableField("order_official_365")
    @TableFieldExtend(desc = "自营平台365天单量")
    private Long orderOfficial365;

    /**
     * 全渠道7天单量
     */
    @TableField("order_all_7")
    @TableFieldExtend(desc = "全渠道7天单量")
    private Long orderAll7;

    /**
     * 全渠道180天单量
     */
    @TableField("order_all_180")
    @TableFieldExtend(desc = "全渠道180天单量")
    private Long orderAll180;

    /**
     * 全渠道365天单量
     */
    @TableField("order_all_channel_365")
    @TableFieldExtend(desc = "全渠道365天单量")
    private Long orderAllChannel365;

    /**
     * 热度分
     */
    @TableField("hotness_score")
    @TableFieldExtend(desc = "热度分")
    private BigDecimal hotnessScore;

    /**
     * 竞对销量
     */
    @TableField("competitor_order")
    @TableFieldExtend(desc = "竞对销量")
    private Long competitorOrder;

    /**
     * 有效性1有效0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性1有效0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "更新时间")
    private LocalDateTime updatetime;

    /**
     * 中文全称
     */
    @TableField("chinese_all_name")
    @TableFieldExtend(desc = "中文全称")
    private String chineseAllName;

    /**
     * 短名
     */
    @TableField("short_name")
    @TableFieldExtend(desc = "短名")
    private String shortName;

    /**
     * 曾用名
     */
    @TableField("former_name")
    @TableFieldExtend(desc = "曾用名")
    private String formerName;

    /**
     * 别名
     */
    @TableField("other_name")
    @TableFieldExtend(desc = "别名")
    private String otherName;

    /**
     * 下挂品类名称
     */
    @TableField("category_info")
    @TableFieldExtend(desc = "下挂品类名称")
    private String categoryInfo;

    /**
     * 品类产品关键词
     */
    @TableField("category_key_word")
    @TableFieldExtend(desc = "品类产品关键词")
    private String categoryKeyWord;

    /**
     * 周边/关联景点id
     */
    @TableField("relation_resource_ids")
    @TableFieldExtend(desc = "周边/关联景点id")
    private String relationResourceIds;

    /**
     * 周边/关联景点名称
     */
    @TableField("relation_resource_names")
    @TableFieldExtend(desc = "周边/关联景点名称")
    private String relationResourceNames;

    /**
     * 是否优待政策 1是 0否
     */
    @TableField("preferential_ticket")
    @TableFieldExtend(desc = "是否优待政策 1是 0否")
    private Integer preferentialTicket;

    /**
     * 游玩时长
     */
    @TableField("duration_of_play")
    @TableFieldExtend(desc = "游玩时长")
    private String durationOfPlay;

    /**
     * 亮点
     */
    @TableField("highlights")
    @TableFieldExtend(desc = "亮点")
    private String highlights;


}
