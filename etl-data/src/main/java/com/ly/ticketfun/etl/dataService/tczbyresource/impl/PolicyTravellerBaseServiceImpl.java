package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerBase;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyTravellerBaseMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 出游人基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyTravellerBaseServiceImpl extends DbBaseServiceImpl<PolicyTravellerBaseMapper, PolicyTravellerBase> implements IPolicyTravellerBaseService {

    @Resource
    private PolicyTravellerBaseMapper policyTravellerBaseMapper;

    @Override
    public PolicyTravellerBase queryByPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyTravellerBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyTravellerBase::getResourceId, resourceId);
        queryWrapper.eq(PolicyTravellerBase::getPolicyId, policyId);
        queryWrapper.eq(PolicyTravellerBase::getRowStatus, DataFlagEnum.VALID.getValue());

        return policyTravellerBaseMapper.selectOne(queryWrapper);
    }
}
