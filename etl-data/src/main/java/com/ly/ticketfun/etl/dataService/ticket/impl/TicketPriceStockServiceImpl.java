package com.ly.ticketfun.etl.dataService.ticket.impl;

import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyInventoryService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyPriceCalendarService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketPriceStockService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyInventory;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketPriceStockAgg;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TicketPriceStockServiceImpl implements ITicketPriceStockService {

    @Resource
    private IPolicyPriceCalendarService policyPriceCalendarService;

    @Resource
    private IPolicyInventoryService policyInventoryService;

    @Override
    public TicketPriceStockAgg fetchPriceStock(Long resourceId, Long policyId, Integer timeZone) {
        if (resourceId == null || resourceId <= 0) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "景区id为空");
        }
        if (policyId == null || policyId <= 0) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "政策id为空");
        }

        // 默认东八区
        if (timeZone == null) {
            timeZone = 8;
        }
        // 获取当地时间
        LocalDateTime nowDateTime = LocalDateTime.now(ZoneOffset.UTC).plusHours(timeZone);
        LocalDate nowBeginDate = nowDateTime.toLocalDate();
        // 最多售卖四个月
        LocalDate endDate = nowBeginDate.plusMonths(3)
                .with(TemporalAdjusters.lastDayOfMonth());

        // 查询政策价格日历
        List<PolicyPriceCalendar> policyPriceList = policyPriceCalendarService
                .querySaleListByResourceIdAndPolicyId(resourceId, policyId, nowBeginDate, endDate);

        // 查询库存信息
        List<PolicyInventory> policyInventoryList = new ArrayList<>();
        List<Long> inventoryIdList = policyPriceList.stream()
                .map(PolicyPriceCalendar::getInventoryId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inventoryIdList)) {
            policyInventoryList = policyInventoryService.queryListByIdList(resourceId, inventoryIdList);
        }

        return TicketPriceStockAgg.builder()
                .priceList(policyPriceList)
                .inventoryList(policyInventoryList)
                .build();
    }
}
