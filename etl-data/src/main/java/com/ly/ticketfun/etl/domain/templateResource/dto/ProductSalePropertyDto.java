package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品销售属性
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ProductSalePropertyDto implements Serializable {
    private static final long serialVersionUID = 5849211981921743697L;
    /**
     * 属性id
     */
    private Long propertyId;
    /**
     * 属性Code
     */
    private String propertyCode;
    /**
     * 属性名称
     */
    private String propertyName;
    /**
     * 属性值列表
     */
    private List<ProductSalePropertyValueDto> propertyValueList;

    /**
     * <AUTHOR>
     * @date 2025/09/02
     */
    @Data
    public static class ProductSalePropertyValueDto implements Serializable {
        private static final long serialVersionUID = -8698805141401283494L;
        /**
         * 属性值id
         */
        private String propertyValueId;
        /**
         * 属性值名称
         */
        private String propertyValueName;
        /**
         * 子属性
         */
        private List<ProductSalePropertyDto> subProperties;
    }
}
