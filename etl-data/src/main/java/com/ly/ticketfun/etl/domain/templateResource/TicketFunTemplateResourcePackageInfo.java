package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.PackageResourceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版套餐资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TicketFunTemplateResourcePackageInfo extends PackageResourceDto {
    private static final long serialVersionUID = -2438204871592022185L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否在售
     */
    @EnumField(enumClazz = WhetherEnum.class)
    @DataFlagField
    private Integer isOnSale;
    /**
     * 更新时间
     */
    private Long updateTime;
}
