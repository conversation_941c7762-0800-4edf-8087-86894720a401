package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.framework.annotation.EsIndex;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.PackageResourceDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门票玩乐模版套餐资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@EsIndex(value = "ticketfun-package", token = "18068c4f-f2aa-4638-ad4b-eda0ec8ace91")
public class TicketFunTemplateResourcePackageInfo extends PackageResourceDto {
    private static final long serialVersionUID = -2438204871592022185L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否有效
     */
    @EnumField(enumClazz = DataFlagEnum.class)
    @DataFlagField
    private Integer dataFlag;
    /**
     * 语言
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private String locale;
    /**
     * 创建时间
     */
    @CreateTimeField
    private Long createTime;
    /**
     * 更新时间
     */
    @ModifyTimeField
    private Long updateTime;
}
