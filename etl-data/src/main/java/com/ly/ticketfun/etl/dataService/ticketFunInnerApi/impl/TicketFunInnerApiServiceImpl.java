package com.ly.ticketfun.etl.dataService.ticketFunInnerApi.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.model.api.CommonResult;
import com.ly.localactivity.framework.utils.RedisUtils;
import com.ly.localactivity.framework.utils.http.HttpSendUtils;
import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.ticketfun.etl.common.constant.RedisConstant;
import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ITicketFunInnerApiService;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.properties.TicketFunInnerApiProperties;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ro.FunMainResourceAggListRo;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ro.FunSkuResourceAggLisRo;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ro.FunSubResourceAggListRo;
import com.ly.ticketfun.etl.dataService.ticketFunInnerApi.ro.ticket.GetExchangeRateRo;
import com.ly.ticketfun.etl.domain.common.ExchangeRateInfoDto;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.common.GlobalRegionSearchResultDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 门票玩乐内部api服务实施
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
@Service
public class TicketFunInnerApiServiceImpl implements ITicketFunInnerApiService {
    @Resource
    private TicketFunInnerApiProperties ticketFunInnerApiProperties;

    @Resource
    private RedisUtils redisUtils;

    private final static String SUCCESS_CODE = "200";


    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param needPackage          需要套餐
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    @Override
    public MainResourceAgg queryFunMainResourceAgg(String mainResourceSerialId, Boolean needPackage, Boolean needBookInfo) {
        if (StringUtils.isEmpty(mainResourceSerialId)) {
            return null;
        }

        FunMainResourceAggListRo funMainResourceAggListRo = FunMainResourceAggListRo.builder().
                mainResourceSerialIdList(Collections.singletonList(mainResourceSerialId)).
                needSubResource(needPackage).
                needBookInfo(needBookInfo).build();

        return getMainResourceAgg(ticketFunInnerApiProperties.getQueryFunMainResourceAggListUrl(),
                funMainResourceAggListRo);
    }

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    @Override
    public MainResourceAgg queryFunSubResourceAgg(String mainResourceSerialId, String subResourceSerialId, Boolean needBookInfo, Boolean needCanAdvance, Boolean needSku, Boolean needPrice) {
        if (StringUtils.isEmpty(mainResourceSerialId)) {
            return null;
        }

        List<String> subResourceSerialIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(subResourceSerialId)) {
            subResourceSerialIdList.add(subResourceSerialId);
        }

        FunSubResourceAggListRo funSubResourceAggListRo = FunSubResourceAggListRo.builder().
                mainResourceSerialIdList(Collections.singletonList(mainResourceSerialId)).
                subResourceSerialIdList(subResourceSerialIdList).
                needCanAdvance(needCanAdvance).
                needBookInfo(needBookInfo).
                needSku(needSku).
                needPrice(needPrice).build();

        return getMainResourceAgg(ticketFunInnerApiProperties.getQueryFunSubResourceAggListUrl(), funSubResourceAggListRo);
    }

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param skuResourceSerialId  sku资源id
     * @param needPrice            需要价格
     * @return {@link MainResourceAgg}
     */
    @Override
    public MainResourceAgg queryFunSkuResourceAgg(String mainResourceSerialId, String subResourceSerialId, String skuResourceSerialId, Boolean needCanAdvance, Boolean needPrice) {
        if (StringUtils.isEmpty(mainResourceSerialId)) {
            return null;
        }

        List<String> subResourceSerialIdList = new ArrayList<>(), skuResourceSerialIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(subResourceSerialId)) {
            subResourceSerialIdList.add(subResourceSerialId);
        }
        if (StringUtils.isNotEmpty(skuResourceSerialId)) {
            skuResourceSerialIdList.add(skuResourceSerialId);
        }

        FunSkuResourceAggLisRo funSkuResourceAggLisRo = FunSkuResourceAggLisRo.builder().
                mainResourceSerialIdList(Collections.singletonList(mainResourceSerialId)).
                subResourceSerialIdList(subResourceSerialIdList).
                skuResourceSerialId(skuResourceSerialIdList).
                needCanAdvance(needCanAdvance).
                needPrice(needPrice).build();

        return getMainResourceAgg(ticketFunInnerApiProperties.getQueryFunSkuResourceAggListUrl(), funSkuResourceAggLisRo);
    }

    private MainResourceAgg getMainResourceAgg(String url, Object funMainResourceAggListRo) {
        CommonResult<List<MainResourceAgg>> commonResult = HttpSendUtils.create()
                .withUrl(url)
                .withBody(funMainResourceAggListRo)
                .withContentType(HttpSendUtils.ContentType.APPLICATION_JSON)
                .withTimeout(20000)
                .doPost(new TypeReference<CommonResult<List<MainResourceAgg>>>() {
                });
        if (commonResult == null || !Objects.equals(commonResult.getCode(), SUCCESS_CODE)) {
            // todo ....
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_API_FAIL, url
                    , JSON.toJSONString(funMainResourceAggListRo), JSON.toJSONString(commonResult));
        }
        return CollectionUtil.isNotEmpty(commonResult.getData()) ? commonResult.getData().get(0) : null;
    }

    @Override
    public GlobalRegionDateDto globalRegionSearch(Integer countryId, Integer cityId) {
        if (Objects.equals(countryId, 1)) {
            GlobalRegionDateDto globalRegionDateDto = new GlobalRegionDateDto();
            globalRegionDateDto.setTimeZone("8");
            return globalRegionDateDto;
        }
        String cacheKey = "ticket:globalRegionSearch:" + cityId;
        String cacheValue = redisUtils.get(cacheKey);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return JSON.parseObject(cacheValue, new TypeReference<GlobalRegionDateDto>() {
            });
        }

        String url = String.format(
                ticketFunInnerApiProperties.getGlobalRegionUrl() + "&pageIndex=1&pageSize=100&ids=%d&return=id,name,pid,nameShort,path,timeZone,timeZoneType",
                cityId
        );

        GlobalRegionSearchResultDto result = HttpSendUtils
                .create()
                .withUrl(url)
                .doGet(new TypeReference<GlobalRegionSearchResultDto>() {
                });
        if (result != null && result.getIsSuccess() && !CollectionUtils.isEmpty(result.getResult())) {
            redisUtils.setex(cacheKey, JSON.toJSONString(result.getResult().get(0)), RedisUtils.ONE_DAY);
            return result.getResult().get(0);
        }
        redisUtils.setex(cacheKey, "{}", 30);
        return null;
    }

    @Override
    public List<ExchangeRateInfoDto> fetchExchangeRateList() {
        String cacheKey = RedisConstant.TICKET_EXCHANGE_RATE_CACHE_KEY;

        String cacheValue = redisUtils.get(cacheKey);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return JSON.parseObject(cacheValue, new TypeReference<List<ExchangeRateInfoDto>>() {
            });
        }

        String url = ticketFunInnerApiProperties.getQueryExchangeRateListUrl();
        GetExchangeRateRo ro = new GetExchangeRateRo();

        CommonResult<List<ExchangeRateInfoDto>> result = HttpSendUtils.create()
                .withUrl(url)
                .withBody(ro)
                .withContentType(HttpSendUtils.ContentType.APPLICATION_JSON)
                .doPost(new TypeReference<CommonResult<List<ExchangeRateInfoDto>>>() {
                });

        if (result == null || !Objects.equals(result.getCode(), SUCCESS_CODE)) {
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_API_FAIL, url,
                    JSON.toJSONString(ro), JSON.toJSONString(result));
        }

        List<ExchangeRateInfoDto> exchangeRateList = result.getData();
        if (CollectionUtil.isNotEmpty(exchangeRateList)) {
            redisUtils.setex(cacheKey, JSON.toJSONString(exchangeRateList), RedisUtils.ONE_HOUR);
            return exchangeRateList;
        }

        return null;
    }
}
