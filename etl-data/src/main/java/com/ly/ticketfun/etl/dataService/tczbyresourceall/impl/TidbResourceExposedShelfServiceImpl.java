package com.ly.ticketfun.etl.dataService.tczbyresourceall.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceExposedShelf;
import com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceExposedShelfMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.ITidbResourceExposedShelfService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 搜索使用货架辅助外显状态（1对多） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
//@Service
public class TidbResourceExposedShelfServiceImpl extends DbBaseServiceImpl<TidbResourceExposedShelfMapper, ResourceExposedShelf> implements ITidbResourceExposedShelfService {

    @Override
    public List<ResourceExposedShelf> queryResourceExposedShelfListByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceExposedShelf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceExposedShelf::getResourceId, resourceId)
                .eq(ResourceExposedShelf::getRowStatus, DataFlagEnum.VALID.getValue());
        return this.queryList(queryWrapper);
    }
}
