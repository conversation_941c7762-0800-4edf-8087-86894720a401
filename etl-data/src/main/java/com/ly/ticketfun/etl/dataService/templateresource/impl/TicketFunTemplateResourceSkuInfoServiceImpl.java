package com.ly.ticketfun.etl.dataService.templateresource.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.model.es.EsDeleteRequest;
import com.ly.localactivity.framework.model.es.EsSearchResponse;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceSkuService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.SkuInfoSearchRo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门票玩乐模版SKU资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourceSkuInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourceSkuInfo> implements ITicketFunTemplateResourceSkuService {
    /**
     * 按产品id查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(String productId, String indexSuffix) {
        if (productId == null){
            return Collections.emptyList();
        }

        return query(Collections.singletonList(productId), null, null, indexSuffix);
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(List<String> productIdList, String indexSuffix) {
        return query(productIdList, null , null, indexSuffix);
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(String packageId, String indexSuffix) {
        if (packageId == null){
            return Collections.emptyList();
        }

        return query(null, Collections.singletonList(packageId), null, indexSuffix);
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(List<String> packageIdList, String indexSuffix) {
        return query(null, packageIdList, null, indexSuffix);
    }

    public List<TicketFunTemplateResourceSkuInfo> query(String productId, String packageId, List<String> packageIds, String skuId, List<String> skuIds, String indexSuffix){
        List<String> productIdList = new ArrayList<>();
        List<String> packageIdList = new ArrayList<>();
        List<String> skuIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(productId)){
            productIdList.add(productId);
        }
        if (StringUtils.isNotEmpty(packageId)){
            packageIdList.add(packageId);
        }else {
            packageIdList = packageIds;
        }
        if (StringUtils.isNotEmpty(skuId)){
            skuIdList.add(skuId);
        }else {
            skuIdList = skuIds;
        }

        return query(productIdList, packageIdList, skuIdList, indexSuffix);
    }

    private List<TicketFunTemplateResourceSkuInfo> query(List<String> productIdList, List<String> packageIdList, List<String> skuIds, String indexSuffix) {
        if (CollectionUtil.isEmpty(productIdList) && CollectionUtil.isEmpty(packageIdList) &&  CollectionUtil.isEmpty(skuIds)){
            return Collections.emptyList();
        }
        SkuInfoSearchRo skuInfoSearchRo = new SkuInfoSearchRo();
        if (CollectionUtil.isNotEmpty(productIdList)){
            skuInfoSearchRo.setProductIds(String.join(" OR ", productIdList));
        }
        if (CollectionUtil.isNotEmpty(packageIdList)){
            skuInfoSearchRo.setPackageIds(String.join(" OR ", packageIdList));
        }
        if (CollectionUtil.isNotEmpty(skuIds)){
            skuInfoSearchRo.setSkuIds(String.join(" OR ", skuIds));
        }
        EsSearchResult<TicketFunTemplateResourceSkuInfo> esSearchResult =  super.query("ticketfun_sku_query", indexSuffix, skuInfoSearchRo, new TypeReference<EsSearchResponse<TicketFunTemplateResourceSkuInfo>>() {});
        return esSearchResult != null && esSearchResult.getList() != null ? esSearchResult.getList() : new ArrayList<>();
    }
}
