package com.ly.ticketfun.etl.dataService.templateresource.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourceSkuService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceSkuInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.PackageInfoSearchRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.SkuInfoSearchRo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 门票玩乐模版SKU资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourceSkuInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourceSkuInfo> implements ITicketFunTemplateResourceSkuService {
    @PostConstruct
    public void TicketFunTemplateResourcePackageServiceImpl() {
        super.init(ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_SKU.getIndex(), ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_SKU.getToken());
    }

    /**
     * 按产品id查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(String productId) {
        if (productId == null){
            return Collections.emptyList();
        }

        return query(Collections.singletonList(productId), null, null);
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByProductId(List<String> productIdList) {
        return query(productIdList, null , null);
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(String packageId) {
        if (packageId == null){
            return Collections.emptyList();
        }

        return query(null, Collections.singletonList(packageId), null);
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id
     * @return {@link List}<{@link TicketFunTemplateResourceSkuInfo}>
     */
    @Override
    public List<TicketFunTemplateResourceSkuInfo> queryByPackageId(List<String> packageIdList) {
        return query(null, packageIdList, null);
    }

    public List<TicketFunTemplateResourceSkuInfo> query(String productId, String packageId, List<String> packageIds, String skuId, List<String> skuIds){
        List<String> productIdList = new ArrayList<>();
        List<String> packageIdList = new ArrayList<>();
        List<String> skuIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(productId)){
            productIdList.add(packageId);
        }
        if (StringUtils.isNotEmpty(packageId)){
            packageIdList.add(packageId);
        }else {
            packageIdList = packageIds;
        }
        if (StringUtils.isNotEmpty(skuId)){
            skuIdList.add(packageId);
        }else {
            skuIdList = skuIds;
        }

        return query(productIdList, packageIdList, skuIdList);
    }

    private List<TicketFunTemplateResourceSkuInfo> query(List<String> productIdList, List<String> packageIdList, List<String> skuIds) {
        if (CollectionUtil.isEmpty(productIdList) && CollectionUtil.isEmpty(packageIdList) &&  CollectionUtil.isEmpty(skuIds)){
            return Collections.emptyList();
        }
        SkuInfoSearchRo skuInfoSearchRo = new SkuInfoSearchRo();
        if (CollectionUtil.isNotEmpty(productIdList)){
            skuInfoSearchRo.setProductIds(String.join(" OR ", productIdList));
        }
        if (CollectionUtil.isNotEmpty(packageIdList)){
            skuInfoSearchRo.setPackageIds(String.join(" OR ", packageIdList));
        }
        if (CollectionUtil.isNotEmpty(skuIds)){
            skuInfoSearchRo.setSkuIds(String.join(" OR ", skuIds));
        }
        EsSearchResult<TicketFunTemplateResourceSkuInfo> esSearchResult =  super.query("", skuInfoSearchRo);
        return esSearchResult != null && esSearchResult.getList() != null ? esSearchResult.getList() : new ArrayList<>();
    }
}
