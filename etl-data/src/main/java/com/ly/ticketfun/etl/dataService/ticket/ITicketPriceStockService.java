package com.ly.ticketfun.etl.dataService.ticket;

import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketPriceStockAgg;

public interface ITicketPriceStockService {

    /**
     * 获取价格库存信息（最多四个月数据）
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @param timeZone   时区 默认东八区
     * @return 价格库存信息
     */
    TicketPriceStockAgg fetchPriceStock(Long resourceId, Long policyId, Integer timeZone);
}
