package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceLabelRelation;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 资源标签关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceLabelRelationService extends IDbBaseService<ResourceLabelRelation> {

    /**
     * 获取设施信息
     * @param resourceId 景区id
     * @return 设施信息
     */
    List<ResourceLabelRelation> queryValidListByResourceId(Long resourceId);
}
