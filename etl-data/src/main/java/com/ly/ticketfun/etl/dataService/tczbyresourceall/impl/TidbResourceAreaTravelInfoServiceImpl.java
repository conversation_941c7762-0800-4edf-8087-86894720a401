package com.ly.ticketfun.etl.dataService.tczbyresourceall.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceAreaTravelInfo;
import com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceAreaTravelInfoMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.ITidbResourceAreaTravelInfoService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 搜索用游玩景点+主题+别名(1对多) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
//@Service
public class TidbResourceAreaTravelInfoServiceImpl extends DbBaseServiceImpl<TidbResourceAreaTravelInfoMapper, ResourceAreaTravelInfo> implements ITidbResourceAreaTravelInfoService {

    @Override
    public List<ResourceAreaTravelInfo> queryValidByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceAreaTravelInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceAreaTravelInfo::getResourceId, resourceId);
        queryWrapper.eq(ResourceAreaTravelInfo::getRowStatus, DataFlagEnum.VALID.getValue());
        return this.queryList(queryWrapper);
    }
}
