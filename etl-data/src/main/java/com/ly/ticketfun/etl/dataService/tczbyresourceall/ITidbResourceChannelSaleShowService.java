package com.ly.ticketfun.etl.dataService.tczbyresourceall;

import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceChannelSaleShow;
import com.ly.localactivity.framework.service.IDbBaseService;

import java.util.List;

/**
 * <p>
 * 搜索使用价格信息（多平台） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ITidbResourceChannelSaleShowService extends IDbBaseService<ResourceChannelSaleShow> {

    /**
     * 获取渠道价格信息
     * @param resourceId 景区ID
     * @return 渠道价格信息
     */
    List<ResourceChannelSaleShow> queryValidListByResourceId(Long resourceId);
}
