package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 促销运营设置扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ResourcePromotionOperateExtend")
@TableExtend(desc = "促销运营设置扩展表")
public class ResourcePromotionOperateExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "RPOEId", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键")
    private Long id;

    /**
     * 促销运营设置主表Id
     */
    @TableField("RPOEObjectId")
    @TableFieldExtend(desc = "促销运营设置主表Id")
    private Long objectId;

    /**
     * 扩展类型：1=红包批次号
     */
    @TableField("RPOEType")
    @TableFieldExtend(desc = "扩展类型：1=红包批次号")
    private Integer type;

    /**
     * 扩展值
     */
    @TableField("RPOEValue")
    @TableFieldExtend(desc = "扩展值")
    private String value;

    /**
     * 资源供应链类型(1自营,2三方)
     */
    @TableField("RPOESupplyChainType")
    @TableFieldExtend(desc = "资源供应链类型(1自营,2三方)")
    private String supplyChainType;

    /**
     * 红包类型 1国内 2hopegoo
     */
    @TableField("RPOERedPacketType")
    @TableFieldExtend(desc = "红包类型 1国内 2hopegoo")
    private Integer redPacketType;

    /**
     * 有效性：0=无效，1=有效
     */
    @TableField("RPOERowStatus")
    @TableFieldExtend(desc = "有效性：0=无效，1=有效")
    private Integer rowStatus;

    /**
     * 创建人工号[姓名]
     */
    @TableField("RPOECreateUser")
    @TableFieldExtend(desc = "创建人工号[姓名]")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "RPOECreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("RPOEUpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "RPOEUpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
