package com.ly.ticketfun.etl.dataService.ticket.impl;

import com.ly.ticketfun.etl.common.exception.TRTransformException;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyMultipleOptionsService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerBaseService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerCrowdInfoService;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerCrowdLimitService;
import com.ly.ticketfun.etl.dataService.ticket.ITicketOrderRuleService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyMultipleOptions;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerBase;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdLimit;
import com.ly.ticketfun.etl.domain.tczbyresource.agg.TicketTravellerAgg;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TicketOrderRuleServiceImpl implements ITicketOrderRuleService {

    @Resource
    private IPolicyTravellerBaseService policyTravellerBaseService;

    @Resource
    private IPolicyTravellerCrowdLimitService policyTravellerCrowdLimitService;

    @Resource
    private IPolicyTravellerCrowdInfoService policyTravellerCrowdInfoService;

    @Resource
    private IPolicyMultipleOptionsService policyMultipleOptionsService;

    @Override
    public TicketTravellerAgg fetchTravellerInfo(Long resourceId, Long policyId) {
        if (resourceId == null || resourceId <= 0) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "景区id为空");
        }
        if (policyId == null || policyId <= 0) {
            throw new TRTransformException(TRTransformException.ErrorInfo.REQ_PARAMS_ERROR, "政策id为空");
        }
        String resourceIdStr = String.valueOf(resourceId);
        String policyIdStr = String.valueOf(policyId);

        // 游玩人基础信息
        PolicyTravellerBase travellerBase = policyTravellerBaseService.queryByPolicyId(resourceId, policyId);
        if (travellerBase == null) {
            throw new TRTransformException(TRTransformException.ErrorInfo.RESOURCE_GET_FAIL,
                    resourceIdStr, policyIdStr, "travellerBase"
            );
        }

        // 游玩人人群列表
        List<PolicyTravellerCrowdInfo> travellerCrowdList = new ArrayList<>();
        if (!Objects.equals(travellerBase.getNeedAllInfo(), 0)) {
            travellerCrowdList = policyTravellerCrowdInfoService
                    .queryListByPolicyId(resourceId, policyId);
        }

        // 出游人人群限制
        List<Long> crowdIdList = travellerCrowdList.stream()
                .filter(i -> Objects.equals(i.getHasLimitConditions(), 1))
                .map(PolicyTravellerCrowdInfo::getId)
                .collect(Collectors.toList());
        List<PolicyTravellerCrowdLimit> travellerCrowdLimitList = null;
        if (CollectionUtils.isNotEmpty(crowdIdList)) {
            travellerCrowdLimitList = policyTravellerCrowdLimitService
                    .queryListByPolicyId(resourceId, policyId, crowdIdList);
        }

        // 多选项列表
        List<PolicyMultipleOptions> multipleOptionList = policyMultipleOptionsService
                .queryListByPolicyId(resourceId, policyId, null);

        return TicketTravellerAgg.builder()
                .travellerBase(travellerBase)
                .travellerCrowdList(travellerCrowdList)
                .travellerCrowdLimitList(travellerCrowdLimitList)
                .multipleOptionList(multipleOptionList)
                .build();
    }
}
