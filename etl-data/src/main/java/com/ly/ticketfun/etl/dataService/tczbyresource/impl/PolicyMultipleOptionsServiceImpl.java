package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyMultipleOptionsService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyMultipleOptions;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyMultipleOptionsMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 政策多选项关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Service
public class PolicyMultipleOptionsServiceImpl extends DbBaseServiceImpl<PolicyMultipleOptionsMapper, PolicyMultipleOptions>
        implements IPolicyMultipleOptionsService {

    @Resource
    private PolicyMultipleOptionsMapper policyMultipleOptionsMapper;

    @Override
    public List<PolicyMultipleOptions> queryListByPolicyId(Long resourceId, Long policyId, List<Integer> selectTypeList) {
        LambdaQueryWrapper<PolicyMultipleOptions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyMultipleOptions::getResourceId, resourceId)
                .eq(PolicyMultipleOptions::getPolicyId, policyId)
                .eq(PolicyMultipleOptions::getRowStatus, DataFlagEnum.VALID.getValue());

        if (CollectionUtils.isNotEmpty(selectTypeList)) {
            queryWrapper.in(PolicyMultipleOptions::getSelectTypeId, selectTypeList);
        }

        return policyMultipleOptionsMapper.selectList(queryWrapper);
    }
}
