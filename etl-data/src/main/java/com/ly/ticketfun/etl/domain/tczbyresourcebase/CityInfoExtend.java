package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 城市信息扩展
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("city_info_extend")
@TableExtend(desc = "城市信息扩展")
public class CityInfoExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @TableFieldExtend(desc = "主键id")
    private Long id;

    /**
     * 国家id
     */
    @TableField("country_id")
    @TableFieldExtend(desc = "国家id")
    private Integer countryId;

    /**
     * 省份id
     */
    @TableField("province_id")
    @TableFieldExtend(desc = "省份id")
    private Integer provinceId;

    /**
     * 城市id
     */
    @TableField("city_id")
    @TableFieldExtend(desc = "城市id")
    private Integer cityId;

    /**
     * 县id
     */
    @TableField("county_id")
    @TableFieldExtend(desc = "县id")
    private Integer countyId;

    /**
     * 公告内容
     */
    @TableField("notice_content")
    @TableFieldExtend(desc = "公告内容")
    private String noticeContent;

    /**
     * 公告开始日期
     */
    @TableField("notice_begin_date")
    @TableFieldExtend(desc = "公告开始日期")
    private LocalDateTime noticeBeginDate;

    /**
     * 公告结束日期
     */
    @TableField("notice_end_date")
    @TableFieldExtend(desc = "公告结束日期")
    private LocalDateTime noticeEndDate;

    /**
     * 特色内容
     */
    @TableField("feature_content")
    @TableFieldExtend(desc = "特色内容")
    private String featureContent;

    /**
     * 特色开始日期
     */
    @TableField("feature_begin_date")
    @TableFieldExtend(desc = "特色开始日期")
    private LocalDateTime featureBeginDate;

    /**
     * 特色结束日期
     */
    @TableField("feature_end_date")
    @TableFieldExtend(desc = "特色结束日期")
    private LocalDateTime featureEndDate;

    /**
     * 有效性
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("create_user")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 交通时长json形式
     */
    @TableField("traffic_time")
    @TableFieldExtend(desc = "交通时长json形式")
    private String trafficTime;


}
