package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 产品（货架）属性配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("market_product_tag")
@TableExtend(desc = "产品（货架）属性配置表")
public class MarketProductTag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合使用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合使用")
    private Long dbrGuid;

    /**
     * 唯一id
     */
    @TableField("id")
    @TableFieldExtend(desc = "唯一id")
    private Long id;

    /**
     * 景区id(分片键)
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id(分片键)")
    private Long resourceId;

    /**
     * 维度 （0：景区维度 1：产品维度）
     */
    @TableField("type_id")
    @TableFieldExtend(desc = "维度 （0：景区维度 1：产品维度）")
    private Integer typeId;

    /**
     * 产品维度的产品id
     */
    @TableField("product_id")
    @TableFieldExtend(desc = "产品维度的产品id")
    private Long productId;

    /**
     * 属性id(数据字典  29014)
     */
    @TableField("prop_id")
    @TableFieldExtend(desc = "属性id(数据字典  29014)")
    private Integer propId;

    /**
     * 属性名称
     */
    @TableField("prop_name")
    @TableFieldExtend(desc = "属性名称")
    private String propName;

    /**
     * 属性排序
     */
    @TableField("prop_sort")
    @TableFieldExtend(desc = "属性排序")
    private Integer propSort;

    /**
     * 属性文本类型 1时间格式  2内容格式
     */
    @TableField("prop_content_type")
    @TableFieldExtend(desc = "属性文本类型 1时间格式  2内容格式")
    private Integer propContentType;

    /**
     * tag名称
     */
    @TableField("tag_content")
    @TableFieldExtend(desc = "tag名称")
    private String tagContent;

    /**
     * 时间类型开始时间
     */
    @TableField("tag_time_begin")
    @TableFieldExtend(desc = "时间类型开始时间")
    private LocalDateTime tagTimeBegin;

    /**
     * 时间类型结束时间
     */
    @TableField("tag_time_end")
    @TableFieldExtend(desc = "时间类型结束时间")
    private LocalDateTime tagTimeEnd;

    /**
     * 时间类型文本
     */
    @TableField("tag_time_content")
    @TableFieldExtend(desc = "时间类型文本")
    private String tagTimeContent;

    /**
     * 文本类型自定义名称
     */
    @TableField("tag_text_content")
    @TableFieldExtend(desc = "文本类型自定义名称")
    private String tagTextContent;

    /**
     * 同一组prop下面tag排序
     */
    @TableField("tag_sort")
    @TableFieldExtend(desc = "同一组prop下面tag排序")
    private Integer tagSort;

    /**
     * 继承的父tagId
     */
    @TableField("parent_tag_id")
    @TableFieldExtend(desc = "继承的父tagId")
    private Long parentTagId;

    /**
     * 数据有效性（0无效  1有效）
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性（0无效  1有效）")
    private Integer rowStatus;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 人群属性对应id
     */
    @TableField("crowd_relation_id")
    @TableFieldExtend(desc = "人群属性对应id")
    private Long crowdRelationId;


}
