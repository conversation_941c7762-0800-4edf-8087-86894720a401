package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.PolicyFeeIncludeItemExtend;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyFeeIncludeItemExtendMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyFeeIncludeItemExtendService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 费用包含扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Service
public class PolicyFeeIncludeItemExtendServiceImpl extends DbBaseServiceImpl<PolicyFeeIncludeItemExtendMapper, PolicyFeeIncludeItemExtend> implements IPolicyFeeIncludeItemExtendService {

}
