package com.ly.ticketfun.etl.domain.templateResource.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.LogField;
import com.ly.localactivity.framework.annotation.model.UniqueCompareField;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.tcbase.i18n.tran.annotation.Tran;
import com.ly.ticketfun.etl.common.enums.base.CurrencyEnum;
import com.ly.ticketfun.etl.common.enums.base.LanguageEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.BookModeEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.PackageResourceEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.ProductResourceEnum;
import com.ly.ticketfun.etl.common.enums.templateResource.SourceAndCooperationEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐资源
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
public class PackageResourceDto implements Serializable {
    private static final long serialVersionUID = -4328072312886644688L;

    /**
     * 景点 id
     */
    private String poiId;

    /**
     * 产品id
     */
    @UniqueCompareField
    @LogField
    private String productId;
    /**
     * 套餐id
     */
    @UniqueCompareField
    @LogField
    private String packageId;
    /**
     * 品类
     */
    private ResourceCategoryDto category;
    /**
     * 名称
     */
    @Tran
    private String name;
    /**
     * 挂牌语言
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private String locale;
    /**
     * 结算价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String netPriceCurrency;
    /**
     * 卖价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String salePriceCurrency;
    /**
     * 门市价币种
     */
    @EnumField(enumClazz = CurrencyEnum.class)
    private String marketPriceCurrency;
    /**
     * 服务语言列表
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private List<String> serviceLanguageList;
    /**
     * 销售属性列表
     */
    private List<PackageSalePropertyDto> salePropertyList;
    /**
     * 配送信息
     */
    private ResourceDeliveryInfoDto deliveryInfo;
    /**
     * 附加费用
     */
    private List<ResourceSurchargeDto> surchargeList;
    /**
     * 赠品清单
     */
    private List<ResourceGiftDto> giftList;
    /**
     * 条款列表(门票:包含项目、玩乐:行程条款)
     */
    private List<ClauseTypeDto> clauseList;
    /**
     * 退款政策
     */
    private RefundPolicyDto refundPolicy;
    /**
     * 预订模式
     */
    private BookModeDto bookMode;
    /**
     * 预订限制
     */
    private BookLimitDto bookLimit;
    /**
     * 预订确认
     */
    private BookConfirmDto bookConfirm;
    /**
     * 预订凭证
     */
    private BookVoucherDto bookVoucher;
    /**
     * 预订联系人信息
     */
    private BookContactInfoDto bookContactInfo;
    /**
     * 使用方式
     */
    private UseRuleDto useRuleDto;
    /**
     * 预订出游人问卷
     */
    private List<BookPassengerQuestionDto> bookPassengerQuestionList;
    /**
     * 发票提供方
     */
    @EnumField(enumClazz = PackageResourceEnum.InvoiceProvider.class)
    private String invoiceProvider;
    /**
     * 服务提供商
     */
    private ResourceServiceProviderDto serviceProvider;
    /**
     * 逻辑扩展
     */
    private List<LogicExtendDto> logicExtendList;
    /**
     * sku列表
     */
    private List<SkuResourceDto> skuList;
    /**
     * 线路列表
     */
    private TravelJourneyDto travelJourney;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 售卖状态
     */
    @EnumField(enumClazz = ProductResourceEnum.SaleStatus.class)
    private String saleStatus;

    /**
     * 来源 美团 携程...
     */
    @EnumField(enumClazz = SourceAndCooperationEnum.SourceTypeEnum.class)
    private String sourceFrom;

    /**
     * 72901 自营 三方
     */
    @EnumField(enumClazz = SourceAndCooperationEnum.CooperationTypeEnum.class)
    private String cooperationType;
    /**
     * 政策模式
     */
    @EnumField(enumClazz = BookModeEnum.PolicyMode.class)
    private String policyMode;


}
