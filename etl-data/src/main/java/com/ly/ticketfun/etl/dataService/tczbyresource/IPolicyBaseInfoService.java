package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyBaseInfo;

import java.util.List;

/**
 * <p>
 * 政策基础表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
public interface IPolicyBaseInfoService extends IDbBaseService<PolicyBaseInfo> {

    /**
     * 根据资源id和政策id查询
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 政策基础信息
     */
    PolicyBaseInfo queryByResourceIdAndPolicyId(Long resourceId, Long policyId);

    List<PolicyBaseInfo> selectListByProductId(Long resourceId, Long productId);
}
