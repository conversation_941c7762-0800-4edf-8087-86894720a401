package com.ly.ticketfun.etl.domain.tczbyresource.agg;

import com.ly.ticketfun.etl.domain.tczbyresource.PolicyMultipleOptions;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerBase;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdLimit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketTravellerAgg {

    /**
     * 游客基础信息
     */
    private PolicyTravellerBase travellerBase;

    /**
     * 人群列表
     */
    private List<PolicyTravellerCrowdInfo> travellerCrowdList;

    /**
     * 人群限制信息列表
     */
    private List<PolicyTravellerCrowdLimit> travellerCrowdLimitList;

    /**
     * 政策选项列表
     */
    private List<PolicyMultipleOptions> multipleOptionList;
}
