package com.ly.ticketfun.etl.dataService.templateresource.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.TypeReference;
import com.ly.localactivity.framework.model.es.EsSearchResponse;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourcePackageService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.PackageInfoSearchRo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 门票玩乐模版套餐资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourcePackageInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourcePackageInfo> implements ITicketFunTemplateResourcePackageService {
    /**
     * 按产品id查询
     *
     * @param productId
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(String productId, String indexSuffix) {
        if (StringUtils.isEmpty(productId)){
            return Collections.emptyList();
        }
        return queryByProductId(Collections.singletonList(productId), indexSuffix);
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(List<String> productIdList, String indexSuffix) {
        return query(productIdList, new ArrayList<>(), indexSuffix);
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    @Override
    public TicketFunTemplateResourcePackageInfo queryByPackageId(String packageId, String indexSuffix) {
        if (StringUtils.isEmpty(packageId)){
            return null;
        }
        List<TicketFunTemplateResourcePackageInfo> packageInfoList = query(new ArrayList<>(), Collections.singletonList(packageId), indexSuffix);
        return CollectionUtil.isNotEmpty(packageInfoList) ? packageInfoList.get(0) : null;
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByPackageId(List<String> packageIdList, String indexSuffix) {
        return query(new ArrayList<>(), packageIdList, indexSuffix);
    }

    /**
     * 查询
     *
     * @param productId 产品id
     * @param indexSuffix
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> query(String productId, String packageId, List<String> packageIds, String indexSuffix) {
        List<String> productIdList = new ArrayList<>();
        List<String> packageIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(productId)){
            productIdList.add(productId);
        }
        if (StringUtils.isNotEmpty(packageId)){
            packageIdList.add(packageId);
        }else {
            packageIdList = packageIds;
        }

        return query(productIdList, packageIdList, indexSuffix);
    }


    private List<TicketFunTemplateResourcePackageInfo> query(List<String> productIdList, List<String> packageIdList, String indexSuffix) {
        if (CollectionUtil.isEmpty(productIdList) && CollectionUtil.isEmpty(packageIdList)){
            return Collections.emptyList();
        }
        PackageInfoSearchRo packageInfoSearchRo = new PackageInfoSearchRo();
        if (CollectionUtil.isNotEmpty(productIdList)){
            packageInfoSearchRo.setProductIds(String.join(" OR ", productIdList));
        }
        if (CollectionUtil.isNotEmpty(packageIdList)){
            packageInfoSearchRo.setPackageIds(String.join(" OR ", packageIdList));
        }
        EsSearchResult<TicketFunTemplateResourcePackageInfo> esSearchResult =  super.query("ticketfun_package_query", indexSuffix, packageInfoSearchRo, new TypeReference<EsSearchResponse<TicketFunTemplateResourcePackageInfo>>() {});
        return esSearchResult != null && esSearchResult.getList() != null ? esSearchResult.getList() : new ArrayList<>();
    }
}
