package com.ly.ticketfun.etl.dataService.templateresource.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ly.localactivity.framework.model.es.EsSearchResult;
import com.ly.localactivity.framework.service.impl.EsBaseServiceImpl;
import com.ly.ticketfun.etl.common.enums.es.ESEnum;
import com.ly.ticketfun.etl.dataService.templateresource.ITicketFunTemplateResourcePackageService;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourcePackageInfo;
import com.ly.ticketfun.etl.domain.templateResource.TicketFunTemplateResourceProductInfo;
import com.ly.ticketfun.etl.domain.templateResource.ro.PackageInfoSearchRo;
import com.ly.ticketfun.etl.domain.templateResource.ro.ProductInfoSearchRo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 门票玩乐模版套餐资源服务
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Service
public class TicketFunTemplateResourcePackageInfoServiceImpl extends EsBaseServiceImpl<TicketFunTemplateResourcePackageInfo> implements ITicketFunTemplateResourcePackageService {
    public final static String QUERY_BY_PRODUCT_ID_TEMPLATE_NAME = "QUERY_BY_PRODUCT_ID_TEMPLATE_NAME";

    @PostConstruct
    public void TicketFunTemplateResourcePackageServiceImpl() {
        super.init(ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PACKAGE.getIndex(), ESEnum.Index.TICKET_FUN_TEMPLATE_RESOURCE_PACKAGE.getToken());
    }

    /**
     * 按产品id查询
     *
     * @param productId
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(String productId) {
        if (StringUtils.isEmpty(productId)){
            return Collections.emptyList();
        }
        return queryByProductId(Collections.singletonList(productId));
    }

    /**
     * 按产品id查询
     *
     * @param productIdList 产品id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByProductId(List<String> productIdList) {
        return query(productIdList, new ArrayList<>());
    }

    /**
     * 按套餐id查询
     *
     * @param packageId 套餐id
     * @return {@link TicketFunTemplateResourceProductInfo}
     */
    @Override
    public TicketFunTemplateResourcePackageInfo queryByPackageId(String packageId) {
        if (StringUtils.isEmpty(packageId)){
            return null;
        }
        List<TicketFunTemplateResourcePackageInfo> packageInfoList = query(new ArrayList<>(), Collections.singletonList(packageId));
        return CollectionUtil.isNotEmpty(packageInfoList) ? packageInfoList.get(0) : null;
    }

    /**
     * 按套餐id查询
     *
     * @param packageIdList 套餐id列表
     * @return {@link List}<{@link TicketFunTemplateResourceProductInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> queryByPackageId(List<String> packageIdList) {
        return query(new ArrayList<>(), packageIdList);
    }

    /**
     * 查询
     *
     * @param productId 产品id
     * @return {@link List}<{@link TicketFunTemplateResourcePackageInfo}>
     */
    @Override
    public List<TicketFunTemplateResourcePackageInfo> query(String productId, String packageId, List<String> packageIds) {
        List<String> productIdList = new ArrayList<>();
        List<String> packageIdList = new ArrayList<>();

        if (StringUtils.isNotEmpty(productId)){
            productIdList.add(packageId);
        }
        if (StringUtils.isNotEmpty(packageId)){
            packageIdList.add(packageId);
        }else {
            packageIdList = packageIds;
        }

        return query(productIdList, packageIdList);
    }


    private List<TicketFunTemplateResourcePackageInfo> query(List<String> productIdList, List<String> packageIdList) {
        if (CollectionUtil.isEmpty(productIdList) && CollectionUtil.isEmpty(packageIdList)){
            return Collections.emptyList();
        }
        PackageInfoSearchRo packageInfoSearchRo = new PackageInfoSearchRo();
        if (CollectionUtil.isNotEmpty(productIdList)){
            packageInfoSearchRo.setProductIds(String.join(" OR ", productIdList));
        }
        if (CollectionUtil.isNotEmpty(packageIdList)){
            packageInfoSearchRo.setPackageIds(String.join(" OR ", packageIdList));
        }
        EsSearchResult<TicketFunTemplateResourcePackageInfo> esSearchResult =  super.query("", packageInfoSearchRo);
        return esSearchResult != null && esSearchResult.getList() != null ? esSearchResult.getList() : new ArrayList<>();
    }
}
