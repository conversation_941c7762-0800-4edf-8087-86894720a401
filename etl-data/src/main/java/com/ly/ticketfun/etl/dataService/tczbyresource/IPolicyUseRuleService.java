package com.ly.ticketfun.etl.dataService.tczbyresource;

import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyUseRule;

/**
 * <p>
 * 政策核销规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
public interface IPolicyUseRuleService extends IDbBaseService<PolicyUseRule> {

    /**
     * 获取政策核销规则
     *
     * @param resourceId 景区id
     * @param policyId   政策id
     * @return 核销规则信息
     */
    PolicyUseRule queryByPolicyId(Long resourceId, Long policyId);
}
