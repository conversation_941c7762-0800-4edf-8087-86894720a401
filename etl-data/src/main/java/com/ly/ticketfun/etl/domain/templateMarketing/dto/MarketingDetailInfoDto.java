package com.ly.ticketfun.etl.domain.templateMarketing.dto;

import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.ticketfun.etl.common.enums.templateMarketing.MarketingEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-9-10
 * @note
 */
@Data
public class MarketingDetailInfoDto implements Serializable {

    /**
     * 促销主键ID
     */
    private Integer promotionId;

    /**
     * 促销名称
     */
    private String promotionName;

    /**
     * 促销基础类别 211
     */
    private Integer promotionTypeId;

    /**
     * 促销计算类型
     */
    @EnumField(enumClazz = MarketingEnum.promotionCalculateTypeEnum.class)
    private String promotionCalculateType;

    /**
     * 促销基础类别名称
     */
    private String promotionTypeName;

    /**
     * 价格促销类型
     */
    @EnumField(enumClazz = MarketingEnum.priceType.class)
    public String priceType;

    /**
     * 扣减规则
     */
    @EnumField(enumClazz = MarketingEnum.reduceRuleTypeEnum.class)
    private String reduceRuleType;

    /**
     * 计算促销金额类型
     */
    @EnumField(enumClazz = MarketingEnum.calculateAmountTypeEnum.class)
    private String calculateAmountType;

    /**
     * 促销开始时间
     */
    private Long beginDate;

    /**
     * 促销结束时间
     */
    private Long endDate;

    /**
     * 促销描述
     */
    private String promotionDesc;

    /**
     * 币种
     */
    private String currencyCode;

    /**
    * 参与次数
    */
    private Integer times;

    /**
     * 立减：立减金额
     * 折扣：最大折扣金额
     */
    private BigDecimal reduceAmount;

    /**
     * 扣减金额
     * 不存在此数据，实时获取
     */
    private List<PromotionCalculateInfoDto> calculateInfoList;

    /**
     * 抵扣金额列表
     */
    @Data
    public static class PromotionCalculateInfoDto
    {
        /**
         * 满足优惠开始金额
         */
        private BigDecimal leftAmount;

        /**
         * 满足优惠结束金额
         */
        private BigDecimal rightAmount;

        /**
         * 优惠最小金额
         */
        private BigDecimal reduceMinValue;

        /**
         * 优惠最大金额
         */
        private BigDecimal reduceMaxValue;
    }

    /**
     * 限制条件
     */
    private List<CheckConditionInfoDto> checkConditionInfoList;

    @Data
    public static class CheckConditionInfoDto
    {
        /**
         * 佣金规则：1=正佣，2=平佣，3=负佣，4=正平佣，5=平负佣，6=正平负佣
         */
        @EnumField(enumClazz = MarketingEnum.commissionRulesEnum.class)
        private String commissionRules;

        /**
         * 会员规则：1=新会员，2=老会员，3=新老会员
         */
        @EnumField(enumClazz = MarketingEnum.memberTypeEnum.class)
        private String memberType;

        /**
         * 今日订是否可用：1=可用，2=不可用
         */
        @EnumField(enumClazz = MarketingEnum.todayCanUseEnum.class)
        private String todayCanUse;

        /**
         * 红包批次号
         */
        private List<String> batchNos;
    }
}
