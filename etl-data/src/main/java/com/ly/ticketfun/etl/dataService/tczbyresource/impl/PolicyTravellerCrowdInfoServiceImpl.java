package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyTravellerCrowdInfoService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyTravellerCrowdInfo;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyTravellerCrowdInfoMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 政策针对人群表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PolicyTravellerCrowdInfoServiceImpl extends DbBaseServiceImpl<PolicyTravellerCrowdInfoMapper, PolicyTravellerCrowdInfo>
        implements IPolicyTravellerCrowdInfoService {

    @Resource
    private PolicyTravellerCrowdInfoMapper policyTravellerCrowdInfoMapper;

    @Override
    public List<PolicyTravellerCrowdInfo> queryListByPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyTravellerCrowdInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyTravellerCrowdInfo::getResourceId, resourceId)
                .eq(PolicyTravellerCrowdInfo::getPolicyId, policyId)
                .eq(PolicyTravellerCrowdInfo::getRowStatus, DataFlagEnum.VALID.getValue());

        return policyTravellerCrowdInfoMapper.selectList(queryWrapper);
    }
}
