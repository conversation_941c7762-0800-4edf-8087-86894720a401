package com.ly.ticketfun.etl.dataService.tczbyresourcebase.impl;

import com.ly.ticketfun.etl.domain.tczbyresourcebase.ResourcePlay;
import com.ly.ticketfun.etl.domain.templateResourceSearch.dto.HighLightInfoDto;
import com.ly.ticketfun.etl.mapper.tczbyresourcebase.ResourcePlayMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.IResourcePlayService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 目的地-游玩 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class ResourcePlayServiceImpl extends DbBaseServiceImpl<ResourcePlayMapper, ResourcePlay> implements IResourcePlayService {

    @Resource
    ResourcePlayMapper resourcePlayMapper;

    @Override
    public List<HighLightInfoDto> queryHighLightInfoByResourceId(Long resourceId) {
        return resourcePlayMapper.queryHighLightInfoByResourceId(resourceId);
    }
}
