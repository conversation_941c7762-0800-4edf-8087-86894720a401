package com.ly.ticketfun.etl.domain.templateResource.dto;

import lombok.Data;

import java.io.Serializable;


/**
 * 资源品类
 *
 * <AUTHOR>
 * @date 2025/09/01
 */
@Data
public class ResourceCategoryDto implements Serializable {
    private static final long serialVersionUID = -5193578973590967263L;
    /**
     * 品类id
     */
    private Long categoryId;
    /**
     * 品类名称
     */
    private String categoryName;

    /**
     * 二级品类id
     */
    private Long subCategoryId;
    /**
     * 二级品类名称
     */
    private String subCategoryName;

}
