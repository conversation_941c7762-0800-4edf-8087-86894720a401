package com.ly.ticketfun.etl.dataService.tczbyresourcebase;

import com.ly.ticketfun.etl.domain.tczbyresourcebase.ResourcePlay;
import com.ly.localactivity.framework.service.IDbBaseService;
import com.ly.ticketfun.etl.domain.templateResourceSearch.dto.HighLightInfoDto;

import java.util.List;

/**
 * <p>
 * 目的地-游玩 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface IResourcePlayService extends IDbBaseService<ResourcePlay> {

    /**
     * 获取亮点信息
     * @param resourceId
     * @return
     */
    List<HighLightInfoDto> queryHighLightInfoByResourceId(Long resourceId);
}
