package com.ly.ticketfun.etl.mapper.tczbyresourcebase;

import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 标签清洗数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
public interface TagResourceWashDataMapper extends LaBaseMapper<TagResourceWashData> {
    List<TagDetailBo> selectListByTagIdListAndResourceId(@Param("tagIdList") List<Long> tagIdList,
                                                         @Param("resourceId")Long resourceId);
}
