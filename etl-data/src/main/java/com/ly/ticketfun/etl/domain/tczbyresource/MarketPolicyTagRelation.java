package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 政策属性表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("market_policy_tag_relation")
@TableExtend(desc = "政策属性表")
public class MarketPolicyTagRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分片聚合使用
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "分片聚合使用")
    private Long dbrGuid;

    /**
     * 唯一id
     */
    @TableField("id")
    @TableFieldExtend(desc = "唯一id")
    private Long id;

    /**
     * 挂载景区id(分片键)
     */
    @TableField("mount_resource_id")
    @TableFieldExtend(desc = "挂载景区id(分片键)")
    private Long mountResourceId;

    /**
     * 挂载产品id
     */
    @TableField("mount_product_id")
    @TableFieldExtend(desc = "挂载产品id")
    private Long mountProductId;

    /**
     * 原政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "原政策id")
    private Long policyId;

    /**
     * 政策与属性的唯一值,重要
     */
    @TableField("policy_id_uk")
    @TableFieldExtend(desc = "政策与属性的唯一值,重要")
    private Long policyIdUk;

    /**
     * 属性id(数据字典 29014)
     */
    @TableField("prop_id")
    @TableFieldExtend(desc = "属性id(数据字典 29014)")
    private Integer propId;

    /**
     * tagId
     */
    @TableField("tag_id")
    @TableFieldExtend(desc = "tagId")
    private Long tagId;

    /**
     * 数据有效性（1有效  0无效）
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "数据有效性（1有效  0无效）")
    private Integer rowStatus;

    /**
     * 新增人工号[姓名]
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "新增人工号[姓名]")
    private String createUser;

    /**
     * 新增时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "新增时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 修改人工号[姓名]
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人工号[姓名]")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
