package com.ly.ticketfun.etl.dataService.ticketFunInnerApi;

import com.ly.localactivity.model.domain.tczbactivityresource.agg.MainResourceAgg;
import com.ly.ticketfun.etl.domain.common.GlobalRegionDateDto;
import com.ly.ticketfun.etl.domain.common.GlobalRegionSearchResultDto;

/**
 * 门票玩乐内部api服务
 *
 * <AUTHOR>
 * @date 2025/08/29
 */
public interface ITicketFunInnerApiService {
    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param needPackage          需要套餐
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    MainResourceAgg queryFunMainResourceAgg(String mainResourceSerialId, Boolean needPackage, Boolean needBookInfo);

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param needBookInfo         需要预订信息
     * @return {@link MainResourceAgg}
     */
    MainResourceAgg queryFunSubResourceAgg(String mainResourceSerialId, String subResourceSerialId, Boolean needMainResource, Boolean needBookInfo);

    /**
     * 查询玩乐主资源汇总
     *
     * @param mainResourceSerialId 主资源Id
     * @param subResourceSerialId  子资源id
     * @param skuResourceSerialId  sku资源id
     * @param needPrice            需要价格
     * @return {@link MainResourceAgg}
     */
    MainResourceAgg queryFunSkuResourceAgg(String mainResourceSerialId, String subResourceSerialId, String skuResourceSerialId, Boolean needPrice);


    /**
     * 根据同程侧城市Id获取时区信息
     * @param cityId 同程cityId
     * @return 时区信息
     */
    GlobalRegionDateDto globalRegionSearch(Integer countryId,Integer cityId);

}
