package com.ly.ticketfun.etl.domain.tczbyresourceall;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 搜索用游玩景点+主题+别名(1对多)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_area_travel_info")
@TableExtend(desc = "搜索用游玩景点+主题+别名(1对多)")
public class ResourceAreaTravelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DbrGuid")
    @TableFieldExtend(desc = "")
    private Long dbrguid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 资源类型（0：自身，1：主景点，2：子景点，3：关联景区）
     */
    @TableField("resource_type")
    @TableFieldExtend(desc = "资源类型（0：自身，1：主景点，2：子景点，3：关联景区）")
    private Integer resourceType;

    /**
     * 关联资源 id
     */
    @TableField("travel_resource_id")
    @TableFieldExtend(desc = "关联资源 id")
    private Long travelResourceId;

    /**
     * 关联资源当地名称
     */
    @TableField("local_name")
    @TableFieldExtend(desc = "关联资源当地名称")
    private String localName;

    /**
     * 关联资源英文名称
     */
    @TableField("english_name")
    @TableFieldExtend(desc = "关联资源英文名称")
    private String englishName;

    /**
     * 关联资源一级主题
     */
    @TableField("theme_id")
    @TableFieldExtend(desc = "关联资源一级主题")
    private Integer themeId;

    /**
     * 关联资源一级主题
     */
    @TableField("theme_name")
    @TableFieldExtend(desc = "关联资源一级主题")
    private String themeName;

    /**
     * 关联资源二级主题
     */
    @TableField("second_theme_id")
    @TableFieldExtend(desc = "关联资源二级主题")
    private Integer secondThemeId;

    /**
     * 关联资源二级主题
     */
    @TableField("second_theme_name")
    @TableFieldExtend(desc = "关联资源二级主题")
    private String secondThemeName;

    /**
     * 关联资源别名，逗号分隔
     */
    @TableField("travel_resource_alias_name")
    @TableFieldExtend(desc = "关联资源别名，逗号分隔")
    private String travelResourceAliasName;

    /**
     * 有效性1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "更新时间")
    private LocalDateTime updatetime;


}
