package com.ly.ticketfun.etl.domain.tczbyresource.agg;

import com.ly.ticketfun.etl.domain.tczbyresource.PolicyInventory;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyPriceCalendar;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketPriceStockAgg {

    /**
     * 政策价格列表
     */
    private List<PolicyPriceCalendar> priceList;

    /**
     * 库存列表
     */
    private List<PolicyInventory> inventoryList;
}
