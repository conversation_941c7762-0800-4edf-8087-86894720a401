package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourceHotelIncludeItems;
import com.ly.ticketfun.etl.mapper.tczbyresource.ResourceHotelIncludeItemsMapper;
import com.ly.ticketfun.etl.dataService.tczbyresource.IResourceHotelIncludeItemsService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 景酒包含项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-15
 */
@Service
public class ResourceHotelIncludeItemsServiceImpl extends DbBaseServiceImpl<ResourceHotelIncludeItemsMapper, ResourceHotelIncludeItems> implements IResourceHotelIncludeItemsService {

}
