package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 政策针对人群表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_traveller_crowd_info")
@TableExtend(desc = "政策针对人群表")
public class PolicyTravellerCrowdInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "")
    private Long dbrGuid;

    /**
     * 表id
     */
    @TableField("id")
    @TableFieldExtend(desc = "表id")
    private Long id;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 人群分类id
     */
    @TableField("crowd_type")
    @TableFieldExtend(desc = "人群分类id")
    private Integer crowdType;

    /**
     * 人群分类名称
     */
    @TableField("crowd_type_name")
    @TableFieldExtend(desc = "人群分类名称")
    private String crowdTypeName;

    /**
     * 人数限制
     */
    @TableField("num")
    @TableFieldExtend(desc = "人数限制")
    private Integer num;

    /**
     * 是否有限制条件1有 0无
     */
    @TableField("has_limit_conditions")
    @TableFieldExtend(desc = "是否有限制条件1有 0无")
    private Integer hasLimitConditions;

    /**
     * 排序
     */
    @TableField("sort")
    @TableFieldExtend(desc = "排序")
    private Integer sort;

    /**
     * 有效性 1有效0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "修改时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "修改人")
    private String updateUser;


}
