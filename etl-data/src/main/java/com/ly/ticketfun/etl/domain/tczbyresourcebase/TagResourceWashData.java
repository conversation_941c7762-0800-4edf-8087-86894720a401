package com.ly.ticketfun.etl.domain.tczbyresourcebase;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 标签清洗数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tag_resource_wash_data")
@TableExtend(desc = "标签清洗数据")
public class TagResourceWashData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @TableFieldExtend(desc = "id")
    private Long id;

    /**
     * 标签id
     */
    @TableField("tag_id")
    @TableFieldExtend(desc = "标签id")
    private Long tagId;

    /**
     * 平台id
     */
    @TableField("plat_id")
    @TableFieldExtend(desc = "平台id")
    private Integer platId;

    /**
     * 资源类型 1 景区 2货架 3 政策
     */
    @TableField("resource_type")
    @TableFieldExtend(desc = "资源类型 1 景区 2货架 3 政策")
    private Integer resourceType;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 产品id
     */
    @TableField("product_id")
    @TableFieldExtend(desc = "产品id")
    private Long productId;

    /**
     * 票 id
     */
    @TableField("orignal_ticket_id")
    @TableFieldExtend(desc = "票 id")
    private Integer orignalTicketId;

    /**
     * 长政策id
     */
    @TableField("ticket_id")
    @TableFieldExtend(desc = "长政策id")
    private Long ticketId;

    /**
     * 排序值
     */
    @TableField("sort")
    @TableFieldExtend(desc = "排序值")
    private Integer sort;

    /**
     * 是否锁定排序 1锁定 0 未锁定
     */
    @TableField("is_lock_sort")
    @TableFieldExtend(desc = "是否锁定排序 1锁定 0 未锁定")
    private Integer isLockSort;

    /**
     * 绑定状态 1绑定 0 未绑定
     */
    @TableField("bind_status")
    @TableFieldExtend(desc = "绑定状态 1绑定 0 未绑定")
    private Integer bindStatus;

    /**
     * 有效性
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性")
    private Integer rowStatus;

    /**
     * 创建人
     */
    @TableField("create_user")
    @TableFieldExtend(desc = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    @TableFieldExtend(desc = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;

    /**
     * 清洗方式 1.后台book3
     */
    @TableField("wash_method")
    @TableFieldExtend(desc = "清洗方式 1.后台book3")
    private Integer washMethod;


}
