package com.ly.ticketfun.etl.domain.tczbyresource;

import com.baomidou.mybatisplus.annotation.*;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.framework.annotation.model.TableExtend;
import com.ly.localactivity.framework.annotation.model.TableFieldExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 费用包含扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("policy_fee_include_item_extend")
@TableExtend(desc = "费用包含扩展表")
public class PolicyFeeIncludeItemExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自动编号
     */
    @TableId(value = "DbrGuid", type = IdType.AUTO)
    @TableFieldExtend(desc = "自动编号")
    private Long dbrGuid;

    /**
     * 景区id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "景区id")
    private Long resourceId;

    /**
     * 政策id
     */
    @TableField("policy_id")
    @TableFieldExtend(desc = "政策id")
    private Long policyId;

    /**
     * 包含项目主键id
     */
    @TableField("item_id")
    @TableFieldExtend(desc = "包含项目主键id")
    private Long itemId;

    /**
     * 扩展类型 1项目列 2:数量列 3:单位列 4 单次时常 5项目类型(数据字典 2901502)
     */
    @TableField("extend_type")
    @TableFieldExtend(desc = "扩展类型 1项目列 2:数量列 3:单位列 4 单次时常 5项目类型(数据字典 2901502)")
    private Integer extendType;

    /**
     * 扩展类型值
     */
    @TableField("extend_content")
    @TableFieldExtend(desc = "扩展类型值")
    private String extendContent;

    /**
     * 有效性 1有效  0 无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性 1有效  0 无效")
    private Integer rowStatus;

    /**
     * 创建用户
     */
    @TableField("CreateUser")
    @TableFieldExtend(desc = "创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "CreateTime", fill = FieldFill.INSERT)
    @TableFieldExtend(desc = "创建时间")
    @CreateTimeField
    private LocalDateTime createTime;

    /**
     * 更新用户
     */
    @TableField("UpdateUser")
    @TableFieldExtend(desc = "更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "UpdateTime", fill = FieldFill.INSERT_UPDATE)
    @TableFieldExtend(desc = "更新时间")
    @ModifyTimeField
    private LocalDateTime updateTime;


}
