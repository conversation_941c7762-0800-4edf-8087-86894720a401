package com.ly.ticketfun.etl.domain.tczbyresourceall;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.ly.localactivity.framework.annotation.model.*;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.DeleteFlagEnum;


/**
 * <p>
 * 搜索使用价格信息（多平台）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource_channel_sale_show")
@TableExtend(desc = "搜索使用价格信息（多平台）")
public class ResourceChannelSaleShow implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DbrGuid")
    @TableFieldExtend(desc = "")
    private Long dbrguid;

    /**
     * 资源id
     */
    @TableField("resource_id")
    @TableFieldExtend(desc = "资源id")
    private Long resourceId;

    /**
     * 渠道
     */
    @TableField("channel_code")
    @TableFieldExtend(desc = "渠道")
    private String channelCode;

    /**
     * 价格类型（0无外显 1 网络卖价 2门市价 3 文案描述）
     */
    @TableField("price_type")
    @TableFieldExtend(desc = "价格类型（0无外显 1 网络卖价 2门市价 3 文案描述）")
    private Integer priceType;

    /**
     * 外显价格
     */
    @TableField("show_price_desc")
    @TableFieldExtend(desc = "外显价格")
    private String showPriceDesc;

    /**
     * 立减金额
     */
    @TableField("reduce_price")
    @TableFieldExtend(desc = "立减金额")
    private BigDecimal reducePrice;

    /**
     * 前缀文案信息
     */
    @TableField("prefix_desc")
    @TableFieldExtend(desc = "前缀文案信息")
    private String prefixDesc;

    /**
     * 渠道可用红包批次号
     */
    @TableField("redpackage_batch_nos")
    @TableFieldExtend(desc = "渠道可用红包批次号")
    private String redpackageBatchNos;

    /**
     * 有效性1有效 0无效
     */
    @TableField("row_status")
    @TableFieldExtend(desc = "有效性1有效 0无效")
    private Integer rowStatus;

    /**
     * 创建时间
     */
    @TableField("CreateTime")
    @TableFieldExtend(desc = "创建时间")
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    @TableField("UpdateTime")
    @TableFieldExtend(desc = "更新时间")
    private LocalDateTime updatetime;


}
