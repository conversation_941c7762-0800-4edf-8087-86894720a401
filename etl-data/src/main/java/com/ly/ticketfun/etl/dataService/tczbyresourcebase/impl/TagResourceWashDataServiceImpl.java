package com.ly.ticketfun.etl.dataService.tczbyresourcebase.impl;

import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import com.ly.ticketfun.etl.mapper.tczbyresourcebase.TagResourceWashDataMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.ITagResourceWashDataService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 标签清洗数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Service
public class TagResourceWashDataServiceImpl extends DbBaseServiceImpl<TagResourceWashDataMapper, TagResourceWashData> implements ITagResourceWashDataService {
    @Resource
    private TagResourceWashDataMapper tagResourceWashDataMapper;

    @Override
    public List<TagDetailBo> selectTagDetailListByTagIdListAndResourceId(List<Long> tagIdList, Long resourceId) {
        if (CollectionUtils.isEmpty(tagIdList)){
            return Collections.emptyList();
        }
        return tagResourceWashDataMapper.selectListByTagIdListAndResourceId(tagIdList, resourceId);
    }
}
