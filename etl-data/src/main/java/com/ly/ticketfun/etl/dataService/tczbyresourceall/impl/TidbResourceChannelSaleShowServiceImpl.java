package com.ly.ticketfun.etl.dataService.tczbyresourceall.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.domain.tczbyresourceall.ResourceChannelSaleShow;
import com.ly.ticketfun.etl.mapper.tczbyresourceall.TidbResourceChannelSaleShowMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourceall.ITidbResourceChannelSaleShowService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 搜索使用价格信息（多平台） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
//@Service
public class TidbResourceChannelSaleShowServiceImpl extends DbBaseServiceImpl<TidbResourceChannelSaleShowMapper, ResourceChannelSaleShow> implements ITidbResourceChannelSaleShowService {

    @Override
    public List<ResourceChannelSaleShow> queryValidListByResourceId(Long resourceId) {
        LambdaQueryWrapper<ResourceChannelSaleShow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ResourceChannelSaleShow::getResourceId, resourceId)
                .eq(ResourceChannelSaleShow::getRowStatus, DataFlagEnum.VALID.getValue());
        return this.queryList(queryWrapper);
    }
}
