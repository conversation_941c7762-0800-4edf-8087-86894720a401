package com.ly.ticketfun.etl.dataService.tczbyresource.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.ticketfun.etl.dataService.tczbyresource.IPolicyChannelSalesService;
import com.ly.ticketfun.etl.domain.tczbyresource.PolicyChannelSales;
import com.ly.ticketfun.etl.mapper.tczbyresource.PolicyChannelSalesMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * B07-政策分销渠道加价规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class PolicyChannelSalesServiceImpl extends DbBaseServiceImpl<PolicyChannelSalesMapper, PolicyChannelSales> implements IPolicyChannelSalesService {

    @Resource
    private PolicyChannelSalesMapper policyChannelSalesMapper;

    @Override
    public List<PolicyChannelSales> queryListByPolicyId(Long resourceId, Long policyId) {
        LambdaQueryWrapper<PolicyChannelSales> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PolicyChannelSales::getResourceId, resourceId)
                .eq(PolicyChannelSales::getPolicyID, policyId)
                .eq(PolicyChannelSales::getRowStatus, DataFlagEnum.VALID.getValue());

        return policyChannelSalesMapper.selectList(queryWrapper);
    }
}
