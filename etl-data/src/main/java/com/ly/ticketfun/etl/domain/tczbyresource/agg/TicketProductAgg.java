package com.ly.ticketfun.etl.domain.tczbyresource.agg;

import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceBaseInfo;
import com.ly.ticketfun.etl.domain.tczbyresource.ResourceProduct;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TicketProductAgg {
    private ResourceProduct resourceProduct;

    private ResourceBaseInfo resourceBaseInfo;

    private List<TagResourceWashData> tagResourceWashDataList;

    private List<TagDetailBo> tagDetailList;
}
