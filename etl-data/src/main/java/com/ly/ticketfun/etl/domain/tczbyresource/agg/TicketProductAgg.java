package com.ly.ticketfun.etl.domain.tczbyresource.agg;

import com.ly.ticketfun.etl.domain.bo.TagDetailBo;
import com.ly.ticketfun.etl.domain.tczbyresource.*;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.ShelfRecommendWash;
import com.ly.ticketfun.etl.domain.tczbyresourcebase.TagResourceWashData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TicketProductAgg {
    /**
     * 货架信息
     */
    private ResourceProduct resourceProduct;

    /**
     * 景区信息
     */
    private ResourceBaseInfo resourceBaseInfo;

    /**
     * 营销标签景区
     */
    private List<TagResourceWashData> tagResourceWashDataList;

    /**
     * 营销标签基础信息
     */
    private List<TagDetailBo> tagDetailList;

    /**
     * 销售属性
     */
    private List<MarketProductTag> salePropertyList;

    /**
     * 人群列表
     */
    private List<MarketProductCrowdRelation> crowdList;

    /**
     * 是否新商品库信息 true是 false 否
     */
    private Boolean newSyncResource;

    /**
     * 政策信息列表 newSyncResource = true时有效
     */
    private List<PolicyBaseInfo> policyBaseInfoList;

    /**
     * 政策信息列表 newSyncResource = false时有效
     */
    private List<ResourcePolicy> resourcePolicyList;

    /**
     * 人群分类限制
     */
    private List<MarketProductConditionConfig> crowdConfigList;

    /**
     * 购物车推荐数据列表
     */
    private List<ShelfRecommendWash> shelfRecommendWashList;
}
