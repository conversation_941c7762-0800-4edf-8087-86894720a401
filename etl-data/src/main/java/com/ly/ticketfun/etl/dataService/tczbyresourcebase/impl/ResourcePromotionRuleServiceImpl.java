package com.ly.ticketfun.etl.dataService.tczbyresourcebase.impl;

import com.ly.ticketfun.etl.domain.tczbyresourcebase.ResourcePromotionRule;
import com.ly.ticketfun.etl.mapper.tczbyresourcebase.ResourcePromotionRuleMapper;
import com.ly.ticketfun.etl.dataService.tczbyresourcebase.IResourcePromotionRuleService;
import com.ly.localactivity.framework.service.impl.DbBaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 促销基本设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class ResourcePromotionRuleServiceImpl extends DbBaseServiceImpl<ResourcePromotionRuleMapper, ResourcePromotionRule> implements IResourcePromotionRuleService {

    public List<ResourcePromotionRule> queryListByResourceId()
    {
//        return baseMapper.selectList();
        return null;
//        return baseMapper.selectList();
    }
}
