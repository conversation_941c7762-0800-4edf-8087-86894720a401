package com.ly.ticketfun.etl.mapper.tczbyresource;

import com.ly.ticketfun.etl.domain.tczbyresource.ResourcePolicy;
import com.ly.localactivity.framework.datasource.LaBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 资源政策表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
public interface ResourcePolicyMapper extends LaBaseMapper<ResourcePolicy> {

    List<ResourcePolicy> selectResourcePolicyListByProductId(@Param("resourceId") Long resourceId,@Param("productId") Long productId);

}
