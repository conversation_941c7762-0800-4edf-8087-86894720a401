package com.ly.ticketfun.etl.domain.templateResource;

import com.baomidou.mybatisplus.annotation.TableId;
import com.ly.localactivity.framework.annotation.model.CreateTimeField;
import com.ly.localactivity.framework.annotation.model.DataFlagField;
import com.ly.localactivity.framework.annotation.model.EnumField;
import com.ly.localactivity.framework.annotation.model.ModifyTimeField;
import com.ly.localactivity.model.enums.common.DataFlagEnum;
import com.ly.localactivity.model.enums.common.LocaleEnum;
import com.ly.localactivity.model.enums.common.WhetherEnum;
import com.ly.ticketfun.etl.domain.templateResource.dto.SaleChannelDto;
import lombok.Data;

/**
 * 门票玩乐模版资源销售渠道
 *
 * <AUTHOR>
 * @date 2025/09/03
 */
@Data
public class TicketFunTemplateResourceSaleChannelInfo extends SaleChannelDto {
    private static final long serialVersionUID = -4429891685431509264L;
    /**
     * id
     */
    @TableId("id")
    private String id;
    /**
     * 是否有效
     */
    @EnumField(enumClazz = DataFlagEnum.class)
    @DataFlagField
    private Integer dataFlag;
    /**
     * 语言
     */
    @EnumField(enumClazz = LocaleEnum.class)
    private String locale;
    /**
     * 创建时间
     */
    @CreateTimeField
    private Long createTime;
    /**
     * 更新时间
     */
    @ModifyTimeField
    private Long updateTime;
}
